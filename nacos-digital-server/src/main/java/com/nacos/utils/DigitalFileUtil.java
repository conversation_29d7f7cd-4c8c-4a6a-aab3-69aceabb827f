package com.nacos.utils;

import com.aliyun.oss.model.AsyncProcessObjectResult;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.result.Result;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.AudioHeader;
import org.jaudiotagger.audio.AudioFile;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.File;
import java.io.FileOutputStream;

/**
 * 数字人文件处理工具类
 */
@Slf4j
public class DigitalFileUtil {

    /**
     * 统一的数字人资源上传方法
     *
     * @param source   源文件（支持byte[]、MultipartFile、String URL）
     * @param fileName 文件名（可选，为null时自动生成）
     * @param userId   用户ID
     * @param groupId  分组ID
     * @param type     文件类型：
     * （用户）0-授权视频目录，1-训练视频（数字人分身）分组存储，2-生成的数字人视频临时目录，3-拼接后的数字人视频目录，4-音频临时目录，5-音频目录，6-数字人形象封面目录，7-数字人形象头像目录 9 知识空间
     * （系统）10-上传系统音频目录，11-上传系统数字人目录，12-上传系统音频临时目录，13-上传系统视频临时目录，14-数字人形象封面目录
     * @param isSystem 是否系统文件，true表示系统文件，false表示用户文件，null则根据type自动判断
     * @return 上传后的OSS路径
     */
    public static String uploadDigitalResource(Object source, String fileName, String userId, String groupId, Integer type, Boolean isSystem) {
        if (source == null) {
            log.error("上传源文件不能为空");
            return null;
        }

        // 调整目录类型
        type = adjustDirectoryType(type, isSystem);

        // 如果文件名为空，生成新的文件名
        if (StringUtils.isBlank(fileName)) {
            fileName = UUID.randomUUID().toString();
        }

        // 根据source类型选择上传方式
        if (source instanceof byte[]) {
            // 上传字节文件
            return uploadWithRetry((byte[]) source, fileName, userId, groupId, type);
        } else if (source instanceof MultipartFile) {
            try {
                // 上传MultipartFile文件
                return uploadWithRetry(((MultipartFile) source).getBytes(), fileName, userId, groupId, type);
            } catch (IOException e) {
                log.error("读取MultipartFile内容失败", e);
                return null;
            }
        } else if (source instanceof String) {
            // 上传URL文件
            String url = (String) source;
            if (StringUtils.isBlank(url)) {
                log.error("URL不能为空");
                return null;
            }
            // 上传URL文件
            return BOssUtil.uploadDigitalFileByUrl(url, fileName, userId, groupId, type);
        } else {
            log.error("不支持的源文件类型: {}", source.getClass().getName());
            return null;
        }
    }

    /**
     * 调整目录类型
     */
    private static Integer adjustDirectoryType(Integer type, Boolean isSystem) {
        if (isSystem == null) {
            return type;
        }

        if (isSystem) {
            // 如果是系统文件，确保type是系统目录（10-14）
            if (type < 10) {
                return switch (type) {
                    case 1 -> 11; // 用户上传的训练视频目录（数字人）分组存储 -> 系统数字人目录
                    case 2, 3 -> 13; // 视频临时目录 -> 系统视频临时目录
                    case 4 -> 12; // 音频临时目录 -> 系统音频临时目录
                    case 5 -> 10; // 音频目录 -> 系统音频目录
                    case 6 -> 14; // 数字人形象封面目录 -> 系统数字人封面目录
                    case 7 -> 11; // 数字人形象头像目录 -> 系统数字人目录
                    case 8 -> 14; // 数字人视频封面目录 -> 系统数字人封面目录
                    default -> type;
                };
            }
        } else {
            // 如果是用户文件，确保type是用户目录（0-9）
            if (type >= 10) {
                return switch (type) {
                    case 10 -> 5; // 系统音频目录 -> 音频目录
                    case 11 -> 1; // 系统数字人目录 -> 用户上传的训练视频目录（数字人）分组存储
                    case 12 -> 4; // 系统音频临时目录 -> 音频临时目录
                    case 13 -> 3; // 系统视频临时目录 -> 视频临时目录
                    case 14 -> 6; // 系统数字人封面目录 -> 数字人形象封面目录
                    default -> type;
                };
            }
        }
        return type;
    }

    /**
     * 带重试的文件上传
     */
    private static String uploadWithRetry(byte[] fileBytes, String fileName, String userId, String groupId, Integer type) {
        if (fileBytes == null || fileBytes.length == 0) {
            return null;
        }

        // 最大重试次数
        int maxRetries = 6;
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                log.info("开始上传文件，类型：{}，文件名：{}", type, fileName);
                String path = BOssUtil.uploadDigitalFile(fileBytes, fileName, userId, groupId, type);
                if (path != null) {
                    return path;
                }
                Thread.sleep(500);
                log.error("文件上传失败，类型：{}，文件名：{}，重试次数：{}", type, fileName, retryCount + 1);
            } catch (Exception e) {
                log.error("文件上传到OSS发生异常，类型：{}，文件名：{}，重试次数：{}", type, fileName, retryCount + 1, e);
            }
            retryCount++;
        }
        log.error("文件上传失败，超过最大重试次数，类型：{}，文件名：{}", type, fileName);
        return null;
    }

    /**
     * 从视频中提取封面
     *
     * @param videoUrl      视频URL
     * @param fileName      文件名
     * @param userId        用户ID
     * @param groupId       组ID
     * @param t          文件类型
     * @param isSystem      是否系统文件
     * @return 封面URL，如果生成失败则返回null
     */
    public static String extractVideoCover(String videoUrl, String fileName, String userId, String groupId, Integer t, Boolean isSystem) {
        String methodName = "extractVideoCover";
        try {
            // 检查videoUrl是否为空
            if (StringUtils.isBlank(videoUrl)) {
                log.warn("[{}] 视频URL为空，无法生成封面", methodName);
                return null;
            }
            
            // 从完整URL中提取对象名称
            String videoObjectName = videoUrl;
            if (videoUrl.contains("cdn.diandiansheji.com/")) {
                videoObjectName = videoUrl.split("cdn.diandiansheji.com/")[1];
            }

            // 调整目录类型
            Integer type = adjustDirectoryType(t, isSystem);

            // 使用视频文件名生成封面文件名
            String coverFileName = "cover_" + fileName;

            // 调用视频截帧功能
            Result<String> result = BOssUtil.extractVideoFrame(videoObjectName, coverFileName, userId, groupId, type);
            if (result.isSuccess()) {
                // 构建封面URL
                String domain = "https://cdn.diandiansheji.com";
                String url = domain + "/".concat(result.getData());
                log.info("[{}] 生成视频封面成功，coverFileName：{}，URL：{}", methodName, coverFileName, url);
                return url;
            } else {
                log.warn("[{}] 生成视频封面失败，错误信息：{}", methodName, result.getMessage());
                return null;
            }
        } catch (Exception e) {
            log.error("[{}] 生成视频封面过程发生异常", methodName, e);
            return null;
        }
    }

    /**
     * 合并视频
     *
     * @param videoObjectNames OSS中的视频对象名称列表（按顺序合并）
     * @param userId          用户ID
     * @param groupId         分组ID（可选）
     * @return 合并后的视频URL，如果合并失败则返回null
     */
    public static String mergeVideos(List<String> videoObjectNames, String userId, String groupId) {
        String methodName = "mergeVideos";
        
        try {
            if (videoObjectNames == null || videoObjectNames.isEmpty()) {
                log.error("[{}] 视频列表不能为空", methodName);
                return null;
            }

            // 生成输出文件名：merged_时间戳_用户ID
            String outputFileName = "merged_" + System.currentTimeMillis() + "_" + userId;

            // 处理视频路径，移除域名前缀
            List<String> processedVideos = videoObjectNames.stream()
                    .map(video -> {
                        if (video.startsWith("https://cdn.diandiansheji.com/")) {
                            return video.substring("https://cdn.diandiansheji.com/".length());
                        } else if (video.startsWith("http://cdn.diandiansheji.com/")) {
                            return video.substring("http://cdn.diandiansheji.com/".length());
                        } else if (video.startsWith("/")) {
                            return video.substring(1);
                        }
                        return video;
                    })
                    .collect(Collectors.toList());

            // 如果只有一个视频，直接返回该视频的URL
            if (processedVideos.size() == 1) {
                String domain = "https://cdn.diandiansheji.com";
                String videoUrl = domain + "/" + processedVideos.get(0);
                return videoUrl;
            }

            // 添加重试机制
            int maxRetries = 3;
            AsyncProcessObjectResult result = null;
            Exception lastException = null;
            
            for (int attempt = 0; attempt < maxRetries; attempt++) {
                try {
                    // 调用BOssUtil的视频拼接方法
                    result = BOssUtil.concatVideos(processedVideos, outputFileName, userId);
                    if (result != null && StringUtils.isNotBlank(result.getTaskId())) {
                        break; // 成功获取结果，跳出重试循环
                    }
                    
                    // 如果没有获取到有效结果，等待一段时间后重试
                    if (attempt < maxRetries - 1) {
                        log.warn("[{}] 提交视频合并任务失败，准备第{}次重试", methodName, attempt + 1);
                        Thread.sleep(2000); // 等待2秒后重试
                    }
                } catch (Exception e) {
                    lastException = e;
                    log.error("[{}] 第{}次提交视频合并任务异常: {}", methodName, attempt + 1, e.getMessage());
                    if (attempt < maxRetries - 1) {
                        Thread.sleep(2000); // 等待2秒后重试
                    }
                }
            }
            
            if (result == null || StringUtils.isBlank(result.getTaskId())) {
                String errorMsg = lastException != null ? lastException.getMessage() : "未知错误";
                log.error("[{}] 提交视频合并任务失败，已重试{}次: {}", methodName, maxRetries, errorMsg);
                return null;
            }

            // 构建合并后的视频访问路径
            String domain = "https://cdn.diandiansheji.com";
            String videoPath = OssClientConfig.getDigitalPath(userId, null, 3)
                    .concat(outputFileName).concat(OssClientConfig.FILE_SUFFIX_VIDEO);
            String videoUrl = domain + "/" + videoPath;

            log.info("[{}] 提交视频合并任务成功: taskId={}, videoUrl={}", 
                    methodName, result.getTaskId(), videoUrl);
                    
            return videoUrl;
        } catch (Exception e) {
            log.error("[{}] 合并视频过程发生异常", methodName, e);
            return null;
        }
    }

    @SneakyThrows
    public static Long getAudioLength(MultipartFile voiceFile) {
        // 1. 将 MultipartFile 转换为临时 File
        String originalFilename = voiceFile.getOriginalFilename();
        String suffix = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        File tempFile = File.createTempFile("audio-", suffix);
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(voiceFile.getBytes());
        }

        return getAudioLengthByFile(tempFile);

    }

    @SneakyThrows
    public static Long getAudioLengthByFile(File tempFile) {
        // 2. 使用 jaudiotagger 读取音频头信息
        AudioFile audioFile = AudioFileIO.read(tempFile);
        AudioHeader audioHeader = audioFile.getAudioHeader();

        // 返回音频时长（毫秒）
        return (long) (audioHeader.getTrackLength() * 1000); // 秒转毫秒
    }

    /**
     * 根据音频URL获取音频时长（毫秒）
     * 该方法会将远程音频下载到本地临时文件，然后进行处理。
     * * @param voiceUrl 音频文件的URL
     * @return 音频时长（毫秒），如果获取失败则返回null
     */
    public static Long getAudioLengthByUrl(String voiceUrl) {
        Path tempFile = null; // 用于存储临时文件的路径
        try {
            URL url = new URL(voiceUrl);

            // 1. 创建临时文件并下载音频
            // Files.createTempFile 会在系统默认的临时目录中创建一个唯一文件名的临时文件
            // "audio" 是文件名前缀，".tmp" 是文件后缀
            // 1. 从 URL 中提取文件后缀
            String path = url.getPath();
            String fileExtension = ".tmp"; // 默认后缀，如果无法获取则使用
            int dotIndex = path.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < path.length() - 1) {
                // 确保点号不在开头，且后面有字符
                fileExtension = path.substring(dotIndex);
            }
            // 确保后缀以点开始，例如 ".mp3"
            if (!fileExtension.startsWith(".")) {
                fileExtension = "." + fileExtension;
            }
            tempFile = Files.createTempFile("audio", fileExtension);

            // 打开 URL 的输入流，并将内容复制到临时文件
            try (InputStream in = url.openStream()) {
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }

            // 2. 调用 getAudioLengthByFile 方法处理下载好的临时文件
            return getAudioLengthByFile(tempFile.toFile());

        } catch (IOException e) {
            System.err.println("Error downloading or reading audio file from URL: " + voiceUrl + " - " + e.getMessage());
        } catch (Exception e) { // 捕获其他未预期异常
            System.err.println("An unexpected error occurred while getting audio length for " + voiceUrl + " - " + e.getMessage());
        } finally {
            // 3. 确保临时文件被删除，即使发生异常
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile); // 删除临时文件
                } catch (IOException e) {
                    System.err.println("Error deleting temporary file: " + tempFile.toString() + " - " + e.getMessage());
                }
            }
        }
        return null; // 如果获取失败，返回 null
    }
}