package com.nacos.entity.dto;

import com.nacos.constants.MiniMaxDefaults;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax服务商特定参数
 *
 * <AUTHOR> @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "MiniMax服务商特定参数")
public class MinimaxParams {

    @NotBlank(message = "音色ID不能为空")
    @Schema(description = "音色ID", example = "male-qn-qingse", required = true)
    private String voiceId;

    @Schema(description = "待合成的文本", example = "你好，这是一段测试文本")
    private String text;

    @Schema(description = "模型", example = "speech-01-turbo",
            allowableValues = {"speech-01-turbo", "speech-01-hd", "speech-01-240228", "speech-01-turbo-240228", "speech-02-turbo"})
    @Builder.Default
    private String model = "speech-01-turbo";

    @DecimalMin(value = "0.1", message = "语速必须在0.1-3.0之间")
    @DecimalMax(value = "3.0", message = "语速必须在0.1-3.0之间")
    @Schema(description = "语速", example = "1.0", minimum = "0.1", maximum = "3.0")
    @Builder.Default
    private Float speed = 1.0f;

    @DecimalMin(value = "0.1", message = "音量必须在0.1-2.0之间")
    @DecimalMax(value = "2.0", message = "音量必须在0.1-2.0之间")
    @Schema(description = "音量", example = "1.0", minimum = "0.1", maximum = "2.0")
    @Builder.Default
    private Float vol = 1.0f;

    @Min(value = -12, message = "音调必须在-12到12之间")
    @Max(value = 12, message = "音调必须在-12到12之间")
    @Schema(description = "音调", example = "0", minimum = "-12", maximum = "12")
    @Builder.Default
    private Integer pitch = 0;

    @Schema(description = "情感", example = "neutral",
            allowableValues = {"neutral", "happy", "sad", "angry", "fearful", "disgusted", "surprised"})
    @Builder.Default
    private String emotion = "neutral";

    @Schema(description = "是否启用LaTeX读取", example = "false")
    @Builder.Default
    private Boolean latexRead = false;

    @Schema(description = "音频格式", example = "mp3", allowableValues = {"mp3", "wav", "pcm"})
    @Builder.Default
    private String audioFormat = "mp3";

    @Schema(description = "采样率", example = "32000", allowableValues = {"16000", "24000", "32000", "48000"})
    @Builder.Default
    private Integer sampleRate = MiniMaxDefaults.DEFAULT_SAMPLE_RATE;

    @Schema(description = "比特率", example = "128000", allowableValues = {"32000", "64000", "128000", "256000"})
    @Builder.Default
    private Integer bitRate = 128000;

    @Schema(description = "是否启用字幕服务", example = "false")
    @Builder.Default
    private Boolean subtitleEnable = MiniMaxDefaults.DEFAULT_SUBTITLE_ENABLE;

    /**
     * 验证参数的有效性
     *
     * @return true如果所有必需参数都有效
     */
    public boolean isValid() {
        return voiceId != null && !voiceId.trim().isEmpty() &&
               model != null && !model.trim().isEmpty() &&
               speed != null && speed >= 0.1f && speed <= 3.0f &&
               vol != null && vol >= 0.1f && vol <= 2.0f &&
               pitch != null && pitch >= -12 && pitch <= 12;
    }

    /**
     * 获取默认的MiniMax参数
     *
     * @param voiceId 音色ID
     * @return 默认参数对象
     */
    public static MinimaxParams getDefault(String voiceId) {
        return MinimaxParams.builder()
                .voiceId(voiceId)
                .model(MiniMaxDefaults.DEFAULT_MODEL)
                .speed(MiniMaxDefaults.DEFAULT_SPEED)
                .vol(MiniMaxDefaults.DEFAULT_VOL)
                .pitch(MiniMaxDefaults.DEFAULT_PITCH)
                .emotion(MiniMaxDefaults.DEFAULT_EMOTION)
                .latexRead(MiniMaxDefaults.DEFAULT_LATEX_READ)
                .audioFormat(MiniMaxDefaults.DEFAULT_AUDIO_FORMAT)
                .sampleRate(MiniMaxDefaults.DEFAULT_SAMPLE_RATE)
                .bitRate(MiniMaxDefaults.DEFAULT_BIT_RATE)
                .subtitleEnable(MiniMaxDefaults.DEFAULT_SUBTITLE_ENABLE)
                .build();
    }
}