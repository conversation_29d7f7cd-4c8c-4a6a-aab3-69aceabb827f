package com.nacos.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nacos.model.ChanJing.model.config.VideoAudioConfig;
import com.nacos.model.ChanJing.model.config.VideoBgConfig;
import com.nacos.model.ChanJing.model.config.VideoPersonConfig;
import com.nacos.model.ChanJing.model.config.VideoSubtitleConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "禅境创建视频合成任务请求DTO")
public class CreateVideoRequestDTO {

    @Schema(description = "数字人配置")
    private VideoPersonConfig person;

    @Schema(description = "音频配置")
    private VideoAudioConfig audio;

    @Schema(description = "背景颜色, HEX格式, 例如: #000000")
    @JsonProperty("bg_color")
    private String bgColor = "#000000";

    @Schema(description = "背景图片配置")
    private VideoBgConfig bg;

    @Schema(description = "字幕配置")
    @JsonProperty("subtitle_config")
    private VideoSubtitleConfig subtitleConfig;

    @Schema(description = "屏幕宽度, 默认 1080")
    @JsonProperty("screen_width")
    private Integer screenWidth;

    @Schema(description = "屏幕高度, 默认 1920")
    @JsonProperty("screen_height")
    private Integer screenHeight;

    @Schema(description = "回调地址 (可选)")
    private String callback;
} 