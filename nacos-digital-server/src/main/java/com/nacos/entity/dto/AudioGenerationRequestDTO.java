package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 音频生成请求DTO - 支持多服务商
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "音频生成请求DTO - 支持多服务商")
public class AudioGenerationRequestDTO {

    @Schema(description = "任务名称", example = "任务名称")
    private String taskName;

    @NotBlank(message = "服务商标识不能为空")
    @Schema(description = "服务商标识", example = "MINIMAX", allowableValues = {"MINIMAX", "MICROSOFT", "PROVIDER_B"})
    private String provider;
    
    @Valid
    @Schema(description = "通用TTS参数")
    private TtsParams ttsParams;


}