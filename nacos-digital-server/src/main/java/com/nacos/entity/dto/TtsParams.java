package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

import javax.validation.constraints.NotNull;

/**
 * 通用TTS参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通用TTS参数")
public class TtsParams {

    @NotBlank(message = "语音ID不能为空")
    @Schema(description = "语音ID", example = "zh-CN-XiaoxiaoNeural", required = true)
    private String voiceId;

    @Schema(description = "语言代码", example = "zh-CN")
    private String language;

    @DecimalMin(value = "0.1", message = "语速必须在0.1-3.0之间")
    @DecimalMax(value = "3.0", message = "语速必须在0.1-3.0之间")
    @Schema(description = "语速", example = "1.0", minimum = "0.1", maximum = "3.0")
    @NotNull(message = "语速不能为空")
    private Float speed;

    @NotBlank(message = "待合成的文本不能为空")
    @Size(max = 10000, message = "待合成的文本长度不能超过10000字符")
    @Schema(description = "待合成的文本", example = "你好，这是一段测试文本", required = true)
    private String text;

    @Schema(description = "扩展参数", example = "{\"emotion\": \"neutral\", \"latexRead\": false}")
    private Map<String, Object> extraParams = new HashMap<>();

    /**
     * 获取扩展参数
     * 
     * @param key 参数键
     * @param <T> 参数类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraParam(String key) {
        return extraParams != null ? (T) extraParams.get(key) : null;
    }

    /**
     * 获取扩展参数，如果不存在则返回默认值
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @param <T> 参数类型
     * @return 参数值或默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraParam(String key, T defaultValue) {
        if (extraParams == null || !extraParams.containsKey(key)) {
            return defaultValue;
        }
        return (T) extraParams.get(key);
    }

    /**
     * 设置扩展参数
     * 
     * @param key 参数键
     * @param value 参数值
     * @return 当前对象
     */
    public TtsParams setExtraParam(String key, Object value) {
        if (extraParams == null) {
            extraParams = new HashMap<>();
        }
        extraParams.put(key, value);
        return this;
    }


} 