package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 其他服务商特定参数（预留扩展）
 *
 * <AUTHOR> @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "其他服务商特定参数（预留扩展）")
public class ProviderBParams {

    @Schema(description = "音质参数", example = "high", allowableValues = {"low", "medium", "high", "ultra"})
    @Builder.Default
    private String quality = "high";

    @Schema(description = "语速参数", example = "normal", allowableValues = {"slow", "normal", "fast"})
    @Builder.Default
    private String speed = "normal";

    @Schema(description = "音色ID", example = "voice_001")
    private String voiceId;

    @Schema(description = "语言代码", example = "zh-CN")
    @Builder.Default
    private String language = "zh-CN";

    @Schema(description = "音频格式", example = "mp3", allowableValues = {"mp3", "wav", "ogg"})
    @Builder.Default
    private String format = "mp3";

    @Schema(description = "采样率", example = "22050")
    @Builder.Default
    private Integer sampleRate = 22050;

    @Schema(description = "其他自定义参数（JSON格式）")
    private String customParams;

    /**
     * 验证参数的有效性
     *
     * @return true如果所有必需参数都有效
     */
    public boolean isValid() {
        return quality != null && !quality.trim().isEmpty() &&
               speed != null && !speed.trim().isEmpty() &&
               language != null && !language.trim().isEmpty() &&
               format != null && !format.trim().isEmpty();
    }

    /**
     * 获取默认的ProviderB参数
     *
     * @return 默认参数对象
     */
    public static ProviderBParams getDefault() {
        return ProviderBParams.builder()
                .quality("high")
                .speed("normal")
                .language("zh-CN")
                .format("mp3")
                .sampleRate(22050)
                .build();
    }

    /**
     * 获取自定义的ProviderB参数
     *
     * @param voiceId 音色ID
     * @param quality 音质
     * @param speed 语速
     * @return 自定义参数对象
     */
    public static ProviderBParams getCustom(String voiceId, String quality, String speed) {
        return ProviderBParams.builder()
                .voiceId(voiceId)
                .quality(quality)
                .speed(speed)
                .language("zh-CN")
                .format("mp3")
                .sampleRate(22050)
                .build();
    }
}