package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数字人音频生成任务表PO（音频生成记录，包含原始请求参数）
 *
 * <AUTHOR> @since 2024-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("digital_audio_task")
@Schema(description = "数字人音频生成任务表PO（音频生成记录，包含原始请求参数）")
public class DigitalAudioTaskPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "任务ID (唯一)")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    @Schema(description = "服务商标识", example = "MINIMAX")
    @TableField("provider")
    private String provider;

    @Schema(description = "生成的音频URL")
    @TableField("generated_audio_url")
    private String generatedAudioUrl;

    @Schema(description = "状态：0-排队中 1-进行中 2-生成成功 3-失败 4-超时 5-已取消")
    @TableField("status")
    private Integer status;

    @Schema(description = "错误信息")
    @TableField("error_msg")
    private String errorMsg;

    @Schema(description = "生成音频的时长 (毫秒)")
    @TableField("duration_ms")
    private Integer durationMs;

    @Schema(description = "语音生成请求参数 (JSON格式)")
    @TableField("request_params_json")
    private String requestParamsJson;

    @Schema(description = "字幕信息 (JSON格式)，包含时间戳和文本内容", example = "[{\"text\":\"你好\",\"startTime\":0,\"endTime\":500},{\"text\":\"世界\",\"startTime\":500,\"endTime\":1000}]")
    @TableField("subtitles_json")
    private String subtitlesJson;

    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除：0-未删除 1-已删除, 默认0")
    @TableField("is_deleted")
    private Boolean isDeleted;

} 