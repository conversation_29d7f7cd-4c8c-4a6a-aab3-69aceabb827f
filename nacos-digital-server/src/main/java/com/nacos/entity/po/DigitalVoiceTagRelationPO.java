package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "音色标签关联表")
@TableName("digital_voice_tag_relation")
public class DigitalVoiceTagRelationPO {
    
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "音色ID")
    @TableField(value = "voice_id", fill = FieldFill.INSERT)
    private String voiceId;
    
    @Schema(description = "标签编码")
    private String tagCode;
    
    @Schema(description = "维度编码")
    private String dimensionCode;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
