package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数字人默认形象与音色VO")
public class DigitalAvatarDefaultVO {

    @Schema(description = "数字人形象ID")
    private String avatarId;

    @Schema(description = "数字人名称")
    private String avatarName;

    @Schema(description = "数字人组ID")
    private String groupId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "数字人分类编码")
    private String avatarCategoryCode;

    @Schema(description = "数字人音色ID")
    private String voiceId;

    @Schema(description = "音色供应商标识")
    private String provider;

    @Schema(description = "数字人音色名称")
    private String voiceName;

    @Schema(description = "音色分类编码")
    private String voiceCategoryCode;
}
