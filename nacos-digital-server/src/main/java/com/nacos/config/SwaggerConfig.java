package com.nacos.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.info.Contact;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Swagger配置类
 * 支持模块化Controller架构的API文档分组
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Configuration
public class SwaggerConfig {

    @Bean
    @Primary
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Digital Server API")
                        .version("2.0")
                        .description("数字服务 API 文档 - 模块化架构版本\n\n" +
                                "## 架构说明\n" +
                                "本API采用模块化架构设计：\n" +
                                "- **Admin API**: 数据管理操作，路径前缀 `/admin/v1/`\n" +
                                "- **业务API**: 业务逻辑操作，路径前缀 `/api/v1/`\n" +
                                "- **Legacy API**: 兼容层接口，保持原有路径不变\n\n" +
                                "## 使用建议\n" +
                                "- 新功能开发请使用Admin API或业务API\n" +
                                "- Legacy API仅用于向后兼容，建议逐步迁移")
                        .termsOfService("http://swagger.io/terms/")
                        .contact(new Contact()
                                .name("Digital Team")
                                .email("<EMAIL>"))
                        .license(new License().name("Apache 2.0").url("http://springdoc.org")));
    }

    /**
     * Admin API分组 - 数据管理操作
     */
    @Bean
    public GroupedOpenApi adminApi() {
        return GroupedOpenApi.builder()
                .group("1. Admin API (数据管理)")
                .pathsToMatch("/admin/v1/**")
                .build();
    }

    /**
     * 数字人业务API分组
     */
    @Bean
    public GroupedOpenApi avatarApi() {
        return GroupedOpenApi.builder()
                .group("2. Avatar API (数字人业务)")
                .pathsToMatch("/api/v1/avatars/**")
                .build();
    }

    /**
     * 媒体处理API分组
     */
    @Bean
    public GroupedOpenApi mediaApi() {
        return GroupedOpenApi.builder()
                .group("3. Media API (媒体处理)")
                .pathsToMatch("/api/v1/media/**")
                .build();
    }

    /**
     * 任务管理API分组
     */
    @Bean
    public GroupedOpenApi taskApi() {
        return GroupedOpenApi.builder()
                .group("4. Task API (任务管理)")
                .pathsToMatch("/api/v1/tasks/**")
                .build();
    }

    /**
     * 系统管理API分组
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("5. System API (系统管理)")
                .pathsToMatch("/api/v1/system/**")
                .build();
    }

    /**
     * Legacy API分组 - 兼容层接口
     */
    @Bean
    public GroupedOpenApi legacyApi() {
        return GroupedOpenApi.builder()
                .group("6. Legacy API (兼容层)")
                .pathsToMatch("/avatar/**", "/opear/**", "/audio/**", "/video/**", "/upload/**")
                .build();
    }

    /**
     * 所有API分组
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("0. All APIs (全部接口)")
                .pathsToMatch("/**")
                .build();
    }
}