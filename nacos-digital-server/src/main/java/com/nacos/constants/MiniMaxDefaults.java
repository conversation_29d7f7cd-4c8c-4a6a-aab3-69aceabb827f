package com.nacos.constants;

/**
 * MiniMax音频生成服务默认值常量类
 * 
 * 统一管理所有MiniMax相关的默认参数值，避免在多个地方重复定义
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-14
 */
public final class MiniMaxDefaults {

    private MiniMaxDefaults() {
        // 工具类，禁止实例化
    }

    // ==================== 模型相关 ====================
    
    /**
     * 默认模型
     * 选择speech-01-turbo作为默认值，因为它支持字幕功能且性能较好
     */
    public static final String DEFAULT_MODEL = "speech-01-turbo";

    // ==================== 音色相关 ====================
    
    /**
     * 默认语速
     * 1.0表示正常语速，范围[0.5, 2.0]
     */
    public static final Float DEFAULT_SPEED = 1.0f;

    /**
     * 默认音量
     * 1.0表示正常音量，范围(0, 10]
     */
    public static final Float DEFAULT_VOL = 1.0f;

    /**
     * 默认语调
     * 0表示正常语调，范围[-12, 12]
     */
    public static final Integer DEFAULT_PITCH = 0;

    /**
     * 默认情绪
     * neutral表示中性情绪
     */
    public static final String DEFAULT_EMOTION = "neutral";

    /**
     * 默认LaTeX读取设置
     * false表示不启用LaTeX读取功能
     */
    public static final Boolean DEFAULT_LATEX_READ = false;

    // ==================== 音频相关 ====================
    
    /**
     * 默认采样率
     * 32000Hz提供较好的音质，且被MiniMax API广泛支持
     * 注意：统一使用32000，解决之前24000和32000的冲突
     */
    public static final Integer DEFAULT_SAMPLE_RATE = 32000;

    /**
     * 默认比特率
     * 128000bps提供CD质量的音频
     */
    public static final Integer DEFAULT_BIT_RATE = 128000;

    /**
     * 默认音频格式
     * MP3格式兼容性最好
     */
    public static final String DEFAULT_AUDIO_FORMAT = "mp3";

    /**
     * 默认声道数
     * 1表示单声道，适合语音合成
     */
    public static final Integer DEFAULT_CHANNEL = 1;

    // ==================== 功能相关 ====================
    
    /**
     * 默认字幕启用状态
     * false表示默认不启用字幕功能
     */
    public static final Boolean DEFAULT_SUBTITLE_ENABLE = true;

    /**
     * 默认流式输出设置
     * false表示非流式输出，适合同步调用
     */
    public static final Boolean DEFAULT_STREAM = false;

    // ==================== 验证相关 ====================
    
    /**
     * 支持字幕功能的模型列表
     */
    public static final String[] SUBTITLE_SUPPORTED_MODELS = {
        "speech-01-turbo",
        "speech-01-hd"
    };

    /**
     * 检查指定模型是否支持字幕功能
     * 
     * @param model 模型名称
     * @return true如果支持字幕功能
     */
    public static boolean isSubtitleSupported(String model) {
        if (model == null) {
            return false;
        }
        for (String supportedModel : SUBTITLE_SUPPORTED_MODELS) {
            if (supportedModel.equals(model)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取默认值说明信息
     * 用于调试和文档生成
     * 
     * @return 默认值说明字符串
     */
    public static String getDefaultsInfo() {
        return String.format(
            "MiniMax默认值配置:\n" +
            "  模型: %s\n" +
            "  语速: %.1f\n" +
            "  音量: %.1f\n" +
            "  语调: %d\n" +
            "  情绪: %s\n" +
            "  LaTeX读取: %s\n" +
            "  采样率: %d Hz\n" +
            "  比特率: %d bps\n" +
            "  音频格式: %s\n" +
            "  声道数: %d\n" +
            "  字幕启用: %s\n" +
            "  流式输出: %s",
            DEFAULT_MODEL,
            DEFAULT_SPEED,
            DEFAULT_VOL,
            DEFAULT_PITCH,
            DEFAULT_EMOTION,
            DEFAULT_LATEX_READ,
            DEFAULT_SAMPLE_RATE,
            DEFAULT_BIT_RATE,
            DEFAULT_AUDIO_FORMAT,
            DEFAULT_CHANNEL,
            DEFAULT_SUBTITLE_ENABLE,
            DEFAULT_STREAM
        );
    }
}
