package com.nacos.service;

import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.entity.dto.DigitalAudioGenerationDTO;
import com.nacos.entity.dto.TextToSpeechFileRequestDTO;
import com.nacos.entity.dto.TextToSpeechFileResponseDTO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.entity.vo.VoiceListResponseVO;
import com.nacos.result.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DigitalAudioService {


    /**
     * 上传音频
     * @param file 音频文件
     * @param purpose 音频用途
     * @param userId 用户ID
     * @return 音频URL
     */
    Result<String> uploadAudio(MultipartFile file, String purpose, String userId);

    /**
     * 语音克隆
     * @param digitalVoiceCloningDTO 语音克隆参数
     * @param userId 用户ID
     * @return 语音克隆结果
     */
    Result<String> voiceCloning(DigitalVoiceCloningDTO digitalVoiceCloningDTO, String userId);

    /**
     * 获取系统音色列表
     * @return 系统音色列表
     */
    Result<List<DigitalVoiceStyleVO>> getSystemVoiceStyles();

    /**
     * 获取用户自定义音色列表
     * @param userId 用户ID
     * @return 用户自定义音色列表
     */
    Result<List<DigitalVoiceUserCloneVO>> getUserVoiceStyles(String userId);

    /**
     * 语音生成
     * @param digitalAudioGenerationDTO 语音生成参数
     * @param userId 用户ID
     * @return 语音生成结果
     */
    Result<DigitalAudioVO> voiceGeneration(DigitalAudioGenerationDTO digitalAudioGenerationDTO, String userId);

    /**
     * 异步提交音频生成任务
     * @param requestDTO 音频生成参数
     * @param userId 用户ID
     * @return 包含 taskId 的响应，或错误信息
     */
    Result<String> submitAudioGenerationTask(AudioGenerationRequestDTO requestDTO, String userId);

    /**
     * 获取支持的语音列表
     * @return 语音列表
     */
    Result<List<VoiceListResponseVO>> getSupportedVoices();
    
    /**
     * 文本转语音
     *
     * @param requestDTO 文本转语音请求参数
     * @param userId 用户ID，用于OSS上传权限验证
     * @return 包含文件路径的响应
     */
    Result<TextToSpeechFileResponseDTO> textToSpeech(TextToSpeechFileRequestDTO requestDTO, String userId);

    /**
     * 音频生成（同步）
     * 支持多服务商的同步音频生成，提供即时响应
     *
     * @param requestDTO 音频生成请求参数，包含服务商信息和TTS参数
     * @param userId 用户ID，用于权限验证、限流控制和扣费处理
     * @return 包含音频URL、时长、文件大小等信息的同步响应
     */
    Result<AudioGenerationResponseVO> generateAudioSync(AudioGenerationRequestDTO requestDTO, String userId);
}
