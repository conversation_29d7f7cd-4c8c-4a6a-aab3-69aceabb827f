package com.nacos.service;

import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 视频翻译异步处理Service接口
 * 负责处理视频翻译任务的异步执行，包括状态同步、进度监控等
 * 参考DigitalVideoAsyncService的设计模式
 * 
 * <AUTHOR>
 * @since 2025-01-29
 */
public interface VideoTranslateAsyncService {

    /**
     * 异步处理视频翻译任务
     * 主要负责任务的异步执行流程控制
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     */
    void processTranslateTask(String taskId, String userId);

    /**
     * 提交视频翻译任务到羚羊平台
     * 
     * @param taskPO 任务PO对象
     * @return 提交结果，包含羚羊平台任务ID
     */
    Result<Map<String, Object>> submitTranslateTaskToLingyang(VideoTranslateTaskPO taskPO);

    /**
     * 轮询检查任务状态
     * 定期查询羚羊平台的任务状态并更新本地数据库
     * 
     * @param lingyangTaskId 羚羊平台任务ID
     * @param taskPO 本地任务对象
     * @param userId 用户ID
     */
    void pollTaskStatus(String lingyangTaskId, VideoTranslateTaskPO taskPO, String userId);

    /**
     * 处理排队中的翻译任务
     * 由定时任务调用，将排队状态的任务提交到羚羊平台
     */
    void processQueueingTasks();

    /**
     * 处理超时的翻译任务
     * 由定时任务调用，检查并处理超时的任务
     */
    void processTimeoutTasks();

    /**
     * 同步单个任务状态
     * 从羚羊平台获取最新状态并更新本地数据库
     * 
     * @param taskPO 任务对象
     * @return 同步结果
     */
    Result<Map<String, Object>> syncTaskStatus(VideoTranslateTaskPO taskPO);

    /**
     * 批量同步任务状态
     * 批量处理多个任务的状态同步
     * 
     * @param taskList 任务列表
     * @return 同步结果统计
     */
    Result<Map<String, Object>> batchSyncTaskStatus(List<VideoTranslateTaskPO> taskList);

    /**
     * 处理任务完成逻辑
     * 当任务完成时的后续处理，包括通知、统计等
     * 
     * @param taskPO 已完成的任务
     * @param resultData 结果数据
     * @return 处理结果
     */
    Result<String> handleTaskCompletion(VideoTranslateTaskPO taskPO, Map<String, Object> resultData);

    /**
     * 处理任务失败逻辑
     * 当任务失败时的处理，包括错误记录、通知等
     * 
     * @param taskPO 失败的任务
     * @param errorMessage 错误信息
     * @return 处理结果
     */
    Result<String> handleTaskFailure(VideoTranslateTaskPO taskPO, String errorMessage);

    /**
     * 取消正在进行的任务
     * 取消羚羊平台的任务并更新本地状态
     * 
     * @param taskPO 要取消的任务
     * @param userId 用户ID
     * @return 取消结果
     */
    Result<String> cancelTask(VideoTranslateTaskPO taskPO, String userId);

    /**
     * 重试失败的任务
     * 重新提交失败的任务到羚羊平台
     * 
     * @param taskPO 要重试的任务
     * @param userId 用户ID
     * @return 重试结果
     */
    Result<String> retryTask(VideoTranslateTaskPO taskPO, String userId);

    /**
     * 获取任务进度信息
     * 计算并返回任务的详细进度信息
     * 
     * @param taskPO 任务对象
     * @return 进度信息
     */
    Result<Map<String, Object>> getTaskProgress(VideoTranslateTaskPO taskPO);

    /**
     * 推送任务状态变更通知
     * 通过WebSocket或其他方式推送状态变更
     * 
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 新状态
     * @param message 状态描述
     */
    void pushTaskStatusNotification(String userId, String taskId, String status, String message);

    /**
     * 检查系统健康状态
     * 检查异步服务的运行状态和资源使用情况
     * 
     * @return 健康状态信息
     */
    Result<Map<String, Object>> checkSystemHealth();

    /**
     * 获取任务统计信息
     * 获取各种状态的任务数量统计
     *
     * @param userId 用户ID（可选，为空则获取全局统计）
     * @return 统计信息
     */
    Result<Map<String, Object>> getTaskStatistics(String userId);

    /**
     * 同步所有处理中任务的状态
     * 由定时任务调用，查询所有处理中的任务并同步状态
     */
    void syncProcessingTasksStatus();
}
