package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalUserGroupPO;
import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.entity.vo.DigitalAvatarGroupListVO;
import com.nacos.entity.vo.PageResultVO;
import com.nacos.result.Result;
import java.util.List;

/**
 * 数字人用户组服务接口
 */
public interface DigitalUserGroupService extends IService<DigitalUserGroupPO> {
    
    /**
     * 创建数字人用户组
     * 创建新的数字人用户组记录
     * @param groupDTO 数字人用户组信息
     * @return 创建的数字人用户组ID，失败返回错误信息
     */
    Result<DigitalUserGroupVO> createGroup(DigitalUserGroupDTO groupDTO);
    
    /**
     * 更新数字人用户组
     * 更新现有数字人用户组记录
     * @param groupDTO 数字人用户组信息
     * @return 操作结果，失败返回错误信息
     */
    Result<Boolean> updateGroup(DigitalUserGroupDTO groupDTO);
    
    /**
     * 删除数字人用户组
     * 逻辑删除指定ID的数字人用户组记录
     * @param id 数字人用户组id
     * @return 操作结果，失败返回错误信息
     */
    Result<Boolean> deleteGroup(Long id);
    
    /**
     * 获取用户的所有数字人用户组
     * 获取指定用户ID的所有未删除的数字人用户组
     * @param userId 用户ID
     * @return 数字人用户组列表，失败返回错误信息
     */
    Result<List<DigitalUserGroupVO>> listUserGroups(String userId);
    
    /**
     * 获取数字人用户组详情
     * 获取指定ID的数字人用户组详细信息
     * @param groupId 数字人用户组ID
     * @return 数字人用户组详情，失败返回错误信息
     */
    Result<DigitalUserGroupVO> getGroupDetail(Long groupId);

    /**
     * 分页查询所有数字人组列表（通用接口）
     * 支持按组类型和分类筛选的通用分页查询接口
     *
     * @param userId 用户ID，用于权限验证和标识用户组所有权
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @param groupType 组类型筛选：USER-用户组，SYSTEM-系统组，ALL-全部
     * @param categoryCode 分类编码，可选，用于筛选特定分类下的组
     * @return 分页的数字人组列表，包含统计信息，失败返回错误信息
     */
    Result<PageResultVO<DigitalAvatarGroupListVO>> queryAllGroupsPage(String userId, Integer pageNum, Integer pageSize, String groupType, String categoryCode);
}