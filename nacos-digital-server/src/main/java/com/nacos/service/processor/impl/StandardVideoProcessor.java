package com.nacos.service.processor.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.mapper.DigitalSystemAvatarMapper;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.model.ChanJing.ChanJingApi;
import com.nacos.model.ChanJing.ChanJingApiUtil;
import com.nacos.model.ChanJing.ChanJingApiUtil.ChanJingApiException;
import com.nacos.model.ChanJing.model.CreateVideoRequest;
import com.nacos.model.ChanJing.model.CreateVideoResponse;
import com.nacos.model.ChanJing.model.GetVideoDetailResponse;
import com.nacos.model.ChanJing.model.config.VideoAudioConfig;
import com.nacos.model.ChanJing.model.config.VideoPersonConfig;
import com.nacos.result.Result;
import com.nacos.service.processor.VideoEditProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 数字人视频编辑处理器
 * 统一的数字人视频编辑处理实现，基于avatarId和sourceVoiceUrl进行处理
 * 使用禅境API进行视频生成
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardVideoProcessor implements VideoEditProcessor {

    private final ChanJingApi chanJingApi;
    private final DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final com.nacos.mapper.VideoEditTaskItemMapper videoEditTaskItemMapper;

    @Value(value = "${changjin_secret_key}")
    private String secretKey;

    @Override
    public String getProcessorName() {
        return "DigitalAvatarVideoProcessor";
    }

    @Override
    public boolean supportsProvider(String apiProvider) {
        // 支持多个API供应商，可以根据需要扩展
        return StringUtils.isNotBlank(apiProvider) &&
                (apiProvider.equals("default") ||
                        apiProvider.equals("provider1") ||
                        apiProvider.equals("provider2"));
    }

    @Override
    public Result<VideoEditResult> process(VideoEditTaskItemPO taskItem, String parameters) {
        String methodName = "process";
        log.info("[{}] 开始数字人视频编辑处理，subTaskId：{}，avatarId：{}，voiceUrl：{}",
                methodName, taskItem.getSubTaskId(), taskItem.getAvatarId(), taskItem.getSourceVoiceUrl());

        try {
            // 参数验证
            if (taskItem == null || StringUtils.isBlank(taskItem.getAvatarId())
                    || StringUtils.isBlank(taskItem.getSourceVoiceUrl())) {
                log.error("[{}] 任务项、数字人ID或音频链接为空", methodName);
                return Result.ERROR("任务项、数字人ID或音频链接不能为空");
            }

            // 执行数字人视频编辑逻辑
            VideoEditResult result = executeDigitalAvatarVideoEdit(taskItem, parameters);

            if (result != null && StringUtils.isNotBlank(result.getApiJobId())) {
                log.info("[{}] 数字人视频编辑处理提交成功，subTaskId：{}，apiJobId：{}",
                        methodName, taskItem.getSubTaskId(), result.getApiJobId());
                return Result.SUCCESS(result);
            } else {
                log.error("[{}] 数字人视频编辑处理提交失败，返回结果为空", methodName);
                return Result.ERROR("数字人视频编辑处理提交失败");
            }

        } catch (Exception e) {
            log.error("[{}] 数字人视频编辑处理异常", methodName, e);
            return Result.ERROR("数字人视频编辑处理失败：" + e.getMessage());
        }
    }

    @Override
    public Result<VideoEditResult> checkStatus(String apiJobId) {
        String methodName = "checkStatus";
        log.info("[{}] 检查数字人视频编辑任务状态，apiJobId：{}", methodName, apiJobId);

        try {
            if (StringUtils.isBlank(apiJobId)) {
                log.error("[{}] API任务ID为空", methodName);
                return Result.ERROR("API任务ID不能为空");
            }

            // 检查数字人视频编辑状态
            VideoEditResult result = checkDigitalAvatarVideoStatus(apiJobId);

            log.info("[{}] 数字人视频编辑任务状态检查完成，apiJobId：{}，status：{}",
                    methodName, apiJobId, result.getStatus());
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 检查数字人视频编辑任务状态异常", methodName, e);
            return Result.ERROR("检查任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 执行数字人视频编辑
     * 使用禅境API创建视频合成任务
     */
    private VideoEditResult executeDigitalAvatarVideoEdit(VideoEditTaskItemPO taskItem, String parameters) {
        String methodName = "executeDigitalAvatarVideoEdit";

        try {
            log.info("[{}] 开始调用禅境API创建视频合成任务，avatarId：{}，voiceUrl：{}",
                    methodName, taskItem.getAvatarId(), taskItem.getSourceVoiceUrl());

            // 1. 构建禅境API请求参数
            CreateVideoRequest apiRequest = buildCreateVideoRequest(taskItem, parameters);

            // 2. 存储完整的API请求参数到taskParameters字段
            saveCompleteApiParameters(taskItem, apiRequest);

            // 3. 调用禅境API工具类创建视频
            CreateVideoResponse apiResponse = ChanJingApiUtil.createVideo(chanJingApi, apiRequest, secretKey);

            if (apiResponse.getCode() == 0) {
                // 成功：返回处理中状态，包含API任务ID
                String apiJobId = apiResponse.getData(); // 禅境API返回的视频任务ID
                log.info("[{}] 禅境API创建视频合成任务成功，apiJobId：{}", methodName, apiJobId);

                VideoEditResult result = new VideoEditResult(apiJobId, 0); // 0-处理中
                result.setProgress(0);
                return result;
            } else {
                // 失败：返回错误状态
                String errorMsg = "禅境API创建视频合成任务失败：" + apiResponse.getCode() + " - " + apiResponse.getMsg();
                log.error("[{}] {}", methodName, errorMsg);

                VideoEditResult errorResult = new VideoEditResult();
                errorResult.setStatus(2); // 失败
                errorResult.setErrorMsg(errorMsg);
                return errorResult;
            }

        } catch (ChanJingApiException e) {
            String errorMsg = "禅境API业务错误：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult errorResult = new VideoEditResult();
            errorResult.setStatus(2); // 失败
            errorResult.setErrorMsg(errorMsg);
            return errorResult;

        } catch (IllegalArgumentException e) {
            String errorMsg = "禅境API参数错误：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult errorResult = new VideoEditResult();
            errorResult.setStatus(2); // 失败
            errorResult.setErrorMsg(errorMsg);
            return errorResult;

        } catch (Exception e) {
            String errorMsg = "执行数字人视频编辑异常：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult errorResult = new VideoEditResult();
            errorResult.setStatus(2); // 失败
            errorResult.setErrorMsg(errorMsg);
            return errorResult;
        }
    }

    /**
     * 检查数字人视频编辑状态
     * 使用禅境API查询视频详情
     */
    private VideoEditResult checkDigitalAvatarVideoStatus(String apiJobId) {
        String methodName = "checkDigitalAvatarVideoStatus";

        try {
            log.info("[{}] 开始查询禅境API视频状态，apiJobId：{}", methodName, apiJobId);

            // 1. 调用禅境API工具类查询视频详情
            GetVideoDetailResponse apiResponse = ChanJingApiUtil.getVideoDetail(chanJingApi, apiJobId, secretKey);

            VideoEditResult result = new VideoEditResult();
            result.setApiJobId(apiJobId);

            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                // 成功获取视频详情
                var videoDetail = apiResponse.getData();

                // 根据禅境API的状态映射到我们的状态
                // 禅境API状态：10-生成中；30-成功；4X-参数异常；5X-服务异常
                // 系统内部状态：0-待处理 1-准备中 2-处理中 3-处理成功 4-处理失败 5-处理超时 6-已取消 7-验证中 8-上传中 9-下载失败 10-上传失败
                Integer changjingStatus = videoDetail.getStatus();
                if (changjingStatus != null) {
                    switch (changjingStatus) {
                        case 10: // 禅境：生成中
                            result.setStatus(2); // 系统：处理中
                            result.setProgress(50);
                            log.info("[{}] 禅境API视频生成中，状态：{}，进度：{}%",
                                    methodName, changjingStatus, result.getProgress());
                            break;
                        case 30: // 禅境：成功
                            result.setStatus(3); // 系统：处理成功
                            result.setProgress(100);
                            result.setOutputVideoUrl(videoDetail.getVideoUrl());
                            log.info("[{}] 禅境API视频生成成功，视频URL：{}", methodName, videoDetail.getVideoUrl());
                            break;
                        default:
                            // 处理4X参数异常和5X服务异常
                            if (changjingStatus >= 40 && changjingStatus <= 49) {
                                result.setStatus(4); // 系统：处理失败
                                String paramErrorMsg = "禅境API视频生成失败，状态：" + changjingStatus + "（参数异常）";
                                result.setErrorMsg(paramErrorMsg);
                                log.error("[{}] {}", methodName, paramErrorMsg);
                            } else if (changjingStatus >= 50 && changjingStatus <= 59) {
                                result.setStatus(4); // 系统：处理失败
                                String serviceErrorMsg = "禅境API视频生成失败，状态：" + changjingStatus + "（服务异常）";
                                result.setErrorMsg(serviceErrorMsg);
                                log.error("[{}] {}", methodName, serviceErrorMsg);
                            } else {
                                result.setStatus(4); // 系统：处理失败
                                String unknownErrorMsg = "禅境API视频生成失败，未知状态：" + changjingStatus;
                                result.setErrorMsg(unknownErrorMsg);
                                log.error("[{}] {}", methodName, unknownErrorMsg);
                            }
                            break;
                    }
                } else {
                    result.setStatus(0); // 处理中
                    result.setProgress(10);
                }

                log.info("[{}] 禅境API视频状态查询成功，状态：{}，进度：{}%",
                        methodName, result.getStatus(), result.getProgress());

            } else {
                // API调用失败
                String errorMsg = "禅境API查询视频详情失败：" + apiResponse.getCode() + " - " + apiResponse.getMsg();
                log.error("[{}] {}", methodName, errorMsg);

                result.setStatus(2); // 失败
                result.setErrorMsg(errorMsg);
            }

            return result;

        } catch (ChanJingApiException e) {
            String errorMsg = "禅境API业务错误：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult result = new VideoEditResult();
            result.setApiJobId(apiJobId);
            result.setStatus(2); // 失败
            result.setErrorMsg(errorMsg);
            return result;

        } catch (IllegalArgumentException e) {
            String errorMsg = "禅境API参数错误：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult result = new VideoEditResult();
            result.setApiJobId(apiJobId);
            result.setStatus(2); // 失败
            result.setErrorMsg(errorMsg);
            return result;

        } catch (Exception e) {
            String errorMsg = "检查数字人视频编辑状态异常：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);

            VideoEditResult result = new VideoEditResult();
            result.setApiJobId(apiJobId);
            result.setStatus(2); // 失败
            result.setErrorMsg(errorMsg);
            return result;
        }
    }

    /**
     * 构建禅境API创建视频请求参数
     * 参考ChangjinMiniServiceImpl.processVideoGeneration的逻辑
     */
    private CreateVideoRequest buildCreateVideoRequest(VideoEditTaskItemPO taskItem, String parameters) {
        String methodName = "buildCreateVideoRequest";

        try {
            CreateVideoRequest request = new CreateVideoRequest();

            // 1. 查询关联的数字人记录（同时查询用户数字人和系统数字人）
            String avatarId = taskItem.getAvatarId();
            DigitalUserAvatarPO avatarUser = digitalUserAvatarMapper.selectOne(
                    new LambdaQueryWrapper<DigitalUserAvatarPO>()
                            .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                            .eq(DigitalUserAvatarPO::getAvatarId, avatarId)
            );
            // 查询系统数字人表
            DigitalSystemAvatarPO avatarSystem = digitalSystemAvatarMapper.selectOne(
                    new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                            .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                            .eq(DigitalSystemAvatarPO::getAvatarId, avatarId)
            );
            if (avatarUser == null && avatarSystem == null) {
                String errorMsg = "未找到对应的数字人形象记录";
                log.error("[{}] {}: avatarId={}", methodName, errorMsg, avatarId);
                throw new IllegalArgumentException(errorMsg + ": " + avatarId);
            }

            // 2. 判断使用哪个数字人记录（优先使用用户数字人）
            VideoPersonConfig person = new VideoPersonConfig();

            if (avatarUser != null) {
                log.info("[{}] 使用用户数字人，avatarId：{}", methodName, avatarId);
                setPersonConfigFromUserAvatar(person, avatarUser, methodName);
            } else {
                log.info("[{}] 使用系统数字人，avatarId：{}", methodName, avatarId);
                setPersonConfigFromSystemAvatar(person, avatarSystem, methodName);
            }

            // 设置默认位置
            // person.setX(0);
            // person.setY(480);

            // 验证person对象的字段
            log.info("[{}] 构建的person对象 - id：{}，x：{}，y：{}，width：{}，height：{}，figureType：{}",
                    methodName, person.getId(), person.getX(), person.getY(),
                    person.getWidth(), person.getHeight(), person.getFigureType());

            request.setPerson(person);

            // 4. 设置音频配置
            VideoAudioConfig audio = new VideoAudioConfig();
            audio.setWavUrl(taskItem.getSourceVoiceUrl());  // 使用传入的音频URL
            audio.setType("audio");  // 明确设置为音频文件模式
            audio.setVolume(100);    // 设置音量
            audio.setLanguage("cn"); // 设置语言
            request.setAudio(audio);

            // 5. 设置屏幕尺寸（优先使用全局传入的自定义尺寸，否则使用数字人的实际尺寸）
            Integer finalScreenWidth = person.getWidth();
            Integer finalScreenHeight = person.getHeight();

            // 解析taskParameters中的全局屏幕尺寸参数
            // 注意：taskParameters可能包含两种格式：
            // 1. 初始的全局参数（仅包含screenWidth/screenHeight）
            // 2. 完整的API参数（如果是重试任务）
            String taskParameters = taskItem.getTaskParameters();
            if (taskParameters != null && !taskParameters.trim().isEmpty()) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper paramMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    com.fasterxml.jackson.databind.JsonNode paramNode = paramMapper.readTree(taskParameters);

                    // 检查是否为完整的API参数格式（包含person、audio等字段）
                    if (paramNode.has("person") && paramNode.has("audio")) {
                        // 这是完整的API参数，从screen_width/screen_height读取
                        if (paramNode.has("screen_width") && !paramNode.get("screen_width").isNull()) {
                            Integer customWidth = paramNode.get("screen_width").asInt();
                            if (customWidth > 0) {
                                finalScreenWidth = customWidth;
                                log.info("[{}] 从完整API参数中使用屏幕宽度：{}", methodName, customWidth);
                            }
                        }
                        if (paramNode.has("screen_height") && !paramNode.get("screen_height").isNull()) {
                            Integer customHeight = paramNode.get("screen_height").asInt();
                            if (customHeight > 0) {
                                finalScreenHeight = customHeight;
                                log.info("[{}] 从完整API参数中使用屏幕高度：{}", methodName, customHeight);
                            }
                        }
                    } else {
                        // 这是初始的全局参数格式，从screenWidth/screenHeight读取
                        if (paramNode.has("screenWidth") && !paramNode.get("screenWidth").isNull()) {
                            Integer customWidth = paramNode.get("screenWidth").asInt();
                            if (customWidth > 0) {
                                finalScreenWidth = customWidth;
                                log.info("[{}] 使用全局传入的自定义屏幕宽度：{}", methodName, customWidth);
                            }
                        }
                        if (paramNode.has("screenHeight") && !paramNode.get("screenHeight").isNull()) {
                            Integer customHeight = paramNode.get("screenHeight").asInt();
                            if (customHeight > 0) {
                                finalScreenHeight = customHeight;
                                log.info("[{}] 使用全局传入的自定义屏幕高度：{}", methodName, customHeight);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("[{}] 解析taskParameters中的屏幕尺寸参数失败，使用数字人默认尺寸：{}", methodName, e.getMessage());
                }
            }

            request.setScreenWidth(finalScreenWidth);
            request.setScreenHeight(finalScreenHeight);

            // 6. 设置背景颜色（修改为黑色）
            request.setBgColor("#000000");

            log.info("[{}] 构建禅境API请求参数成功，avatarId：{}，avatarVideoId：{}，audioUrl：{}，最终尺寸：{}x{}（数字人原始尺寸：{}x{}），背景颜色：{}",
                    methodName, avatarId, person.getId(), taskItem.getSourceVoiceUrl(),
                    finalScreenWidth, finalScreenHeight, person.getWidth(), person.getHeight(), "#000000");

            log.debug("[{}] 初始taskParameters内容：{}", methodName, taskParameters);

            // 添加序列化测试日志
            try {
                com.fasterxml.jackson.databind.ObjectMapper testMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                String testJson = testMapper.writeValueAsString(request);
                log.info("[{}] 序列化测试结果：{}", methodName, testJson);
            } catch (Exception ex) {
                log.error("[{}] 序列化测试失败", methodName, ex);
            }

            return request;

        } catch (Exception e) {
            log.error("[{}] 构建禅境API请求参数异常", methodName, e);
            throw new IllegalArgumentException("构建禅境API请求参数失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从用户数字人设置Person配置
     */
    private void setPersonConfigFromUserAvatar(VideoPersonConfig person, DigitalUserAvatarPO avatarUser, String methodName) {
        log.info("[{}] 用户数字人字段值 - avatarVideoId：{}，width：{}，height：{}，figuresType：{}",
                methodName, avatarUser.getAvatarVideoId(), avatarUser.getWidth(),
                avatarUser.getHeight(), avatarUser.getFiguresType());

        setPersonConfigFields(person, avatarUser.getAvatarVideoId(), avatarUser.getWidth(),
                             avatarUser.getHeight(), avatarUser.getFiguresType());
    }

    /**
     * 从系统数字人设置Person配置
     */
    private void setPersonConfigFromSystemAvatar(VideoPersonConfig person, DigitalSystemAvatarPO avatarSystem, String methodName) {
        log.info("[{}] 系统数字人字段值 - avatarVideoId：{}，width：{}，height：{}，figuresType：{}",
                methodName, avatarSystem.getAvatarVideoId(), avatarSystem.getWidth(),
                avatarSystem.getHeight(), avatarSystem.getFiguresType());

        setPersonConfigFields(person, avatarSystem.getAvatarVideoId(), avatarSystem.getWidth(),
                             avatarSystem.getHeight(), avatarSystem.getFiguresType());
    }

    /**
     * 设置Person配置的公共字段
     */
    private void setPersonConfigFields(VideoPersonConfig person, String avatarVideoId, Integer width, Integer height, String figuresType) {
        person.setId(avatarVideoId);
        person.setWidth(width);
        person.setHeight(height);

        if (StringUtils.isNotEmpty(figuresType)) {
            person.setFigureType(figuresType);
        }
    }

    /**
     * 存储完整的API请求参数到taskParameters字段
     * 将即将发送到禅境API的完整参数序列化为JSON并存储，便于调试、重试和审计
     */
    private void saveCompleteApiParameters(VideoEditTaskItemPO taskItem, CreateVideoRequest apiRequest) {
        String methodName = "saveCompleteApiParameters";

        try {
            // 序列化完整的API请求参数
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            String completeApiParametersJson = mapper.writeValueAsString(apiRequest);

            // 更新数据库中的taskParameters字段
            com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<VideoEditTaskItemPO> updateWrapper =
                    new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<>();
            updateWrapper.eq(VideoEditTaskItemPO::getSubTaskId, taskItem.getSubTaskId())
                    .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                    .set(VideoEditTaskItemPO::getTaskParameters, completeApiParametersJson)
                    .set(VideoEditTaskItemPO::getUpdateTime, new java.util.Date());

            int updateCount = videoEditTaskItemMapper.update(null, updateWrapper);

            if (updateCount > 0) {
                // 同时更新内存中的对象
                taskItem.setTaskParameters(completeApiParametersJson);

                log.info("[{}] 已存储完整API请求参数到taskParameters，subTaskId：{}，参数长度：{}字符",
                        methodName, taskItem.getSubTaskId(), completeApiParametersJson.length());

                // 记录详细的参数内容（可选，用于调试）
                log.debug("[{}] 完整API参数内容：{}", methodName, completeApiParametersJson);
            } else {
                log.warn("[{}] 更新taskParameters失败，未找到对应的子任务记录，subTaskId：{}", methodName, taskItem.getSubTaskId());
            }

        } catch (Exception e) {
            log.error("[{}] 序列化并存储API请求参数失败，subTaskId：{}", methodName, taskItem.getSubTaskId(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}
