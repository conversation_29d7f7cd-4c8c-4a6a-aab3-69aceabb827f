package com.nacos.service.processor;

import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.result.Result;

/**
 * 数字人视频编辑处理器接口
 * 定义数字人视频编辑处理的统一规范，支持多供应商API集成
 */
public interface VideoEditProcessor {

    /**
     * 处理数字人视频编辑任务
     * @param taskItem 任务项，包含avatarId、sourceVoiceUrl、other等字段
     * @param parameters 处理参数JSON字符串
     * @return 处理结果，包含任务ID或错误信息
     */
    Result<VideoEditResult> process(VideoEditTaskItemPO taskItem, String parameters);

    /**
     * 获取处理器名称
     * @return 处理器名称，用于标识不同的处理器实现
     */
    String getProcessorName();

    /**
     * 检查任务状态
     * @param apiJobId 第三方API任务ID
     * @return 任务状态结果
     */
    Result<VideoEditResult> checkStatus(String apiJobId);

    /**
     * 检查是否支持指定的API供应商
     * @param apiProvider API供应商标识
     * @return true表示支持，false表示不支持
     */
    boolean supportsProvider(String apiProvider);

    /**
     * 视频编辑处理结果
     */
    class VideoEditResult {
        /**
         * 第三方API任务ID
         */
        private String apiJobId;

        /**
         * 处理状态：0-处理中 1-成功 2-失败
         */
        private Integer status;

        /**
         * 输出视频URL
         */
        private String outputVideoUrl;

        /**
         * 处理进度百分比(0-100)
         */
        private Integer progress;

        /**
         * 输出文件大小(字节)
         */
        private Long outputFileSize;

        /**
         * 处理耗时(秒)
         */
        private Long processingDuration;

        /**
         * 费用金额
         */
        private Double cost;

        /**
         * 错误信息
         */
        private String errorMsg;

        // 构造方法
        public VideoEditResult() {}

        public VideoEditResult(String apiJobId, Integer status) {
            this.apiJobId = apiJobId;
            this.status = status;
        }

        public VideoEditResult(String apiJobId, Integer status, String outputVideoUrl) {
            this.apiJobId = apiJobId;
            this.status = status;
            this.outputVideoUrl = outputVideoUrl;
        }

        // Getter和Setter方法
        public String getApiJobId() { return apiJobId; }
        public void setApiJobId(String apiJobId) { this.apiJobId = apiJobId; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public String getOutputVideoUrl() { return outputVideoUrl; }
        public void setOutputVideoUrl(String outputVideoUrl) { this.outputVideoUrl = outputVideoUrl; }

        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; }

        public Long getOutputFileSize() { return outputFileSize; }
        public void setOutputFileSize(Long outputFileSize) { this.outputFileSize = outputFileSize; }

        public Long getProcessingDuration() { return processingDuration; }
        public void setProcessingDuration(Long processingDuration) { this.processingDuration = processingDuration; }

        public Double getCost() { return cost; }
        public void setCost(Double cost) { this.cost = cost; }

        public String getErrorMsg() { return errorMsg; }
        public void setErrorMsg(String errorMsg) { this.errorMsg = errorMsg; }
    }
}
