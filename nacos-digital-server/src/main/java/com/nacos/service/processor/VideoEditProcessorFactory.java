package com.nacos.service.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字人视频编辑处理器工厂类
 * 支持多供应商处理器管理和自动发现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoEditProcessorFactory {

    @Autowired
    private List<VideoEditProcessor> processors;

    private Map<String, VideoEditProcessor> processorMap = new HashMap<>();
    private VideoEditProcessor defaultProcessor;

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initProcessors() {
        String methodName = "initProcessors";
        log.info("[{}] 开始初始化数字人视频编辑处理器映射", methodName);

        if (processors != null) {
            VideoEditProcessor standardProcessor = null;

            for (VideoEditProcessor processor : processors) {
                String processorName = processor.getProcessorName();
                processorMap.put(processorName, processor);
                log.info("[{}] 注册处理器：{} -> {}", methodName, processorName, processor.getClass().getSimpleName());

                // 优先选择StandardVideoProcessor作为默认处理器
                if ("DigitalAvatarVideoProcessor".equals(processorName)) {
                    standardProcessor = processor;
                }

                // 如果没有找到StandardVideoProcessor，设置第一个处理器为默认处理器
                if (defaultProcessor == null) {
                    defaultProcessor = processor;
                }
            }

            // 如果找到了StandardVideoProcessor，将其设置为默认处理器
            if (standardProcessor != null) {
                defaultProcessor = standardProcessor;
                log.info("[{}] 设置StandardVideoProcessor为默认处理器：{}", methodName, standardProcessor.getProcessorName());
            } else if (defaultProcessor != null) {
                log.info("[{}] 设置默认处理器：{}", methodName, defaultProcessor.getProcessorName());
            }
        }

        log.info("[{}] 数字人视频编辑处理器映射初始化完成，共注册{}个处理器", methodName, processorMap.size());
    }

    /**
     * 获取默认处理器
     * @return 默认的数字人视频编辑处理器
     */
    public VideoEditProcessor getDefaultProcessor() {
        String methodName = "getDefaultProcessor";

        if (defaultProcessor == null) {
            log.error("[{}] 默认处理器未初始化", methodName);
            throw new IllegalStateException("默认处理器未初始化");
        }

        log.debug("[{}] 获取默认处理器：{}", methodName, defaultProcessor.getClass().getSimpleName());
        return defaultProcessor;
    }

    /**
     * 根据处理器名称获取对应的处理器
     * @param processorName 处理器名称
     * @return 对应的处理器实例
     * @throws IllegalArgumentException 如果不支持该处理器
     */
    public VideoEditProcessor getProcessor(String processorName) {
        String methodName = "getProcessor";

        if (StringUtils.isBlank(processorName)) {
            log.error("[{}] 处理器名称不能为空", methodName);
            throw new IllegalArgumentException("处理器名称不能为空");
        }

        VideoEditProcessor processor = processorMap.get(processorName);
        if (processor == null) {
            log.error("[{}] 不支持的处理器：{}", methodName, processorName);
            throw new IllegalArgumentException("不支持的处理器: " + processorName);
        }

        log.debug("[{}] 获取处理器成功：{} -> {}", methodName, processorName, processor.getClass().getSimpleName());
        return processor;
    }

    /**
     * 根据API供应商获取支持的处理器
     * @param apiProvider API供应商标识
     * @return 支持该供应商的处理器，如果没有则返回默认处理器
     */
    public VideoEditProcessor getProcessorByProvider(String apiProvider) {
        String methodName = "getProcessorByProvider";

        if (StringUtils.isBlank(apiProvider)) {
            log.debug("[{}] API供应商为空，返回默认处理器", methodName);
            return getDefaultProcessor();
        }

        // 查找支持该供应商的处理器
        for (VideoEditProcessor processor : processorMap.values()) {
            if (processor.supportsProvider(apiProvider)) {
                log.debug("[{}] 找到支持供应商{}的处理器：{}", methodName, apiProvider, processor.getClass().getSimpleName());
                return processor;
            }
        }

        log.debug("[{}] 未找到支持供应商{}的处理器，返回默认处理器", methodName, apiProvider);
        return getDefaultProcessor();
    }

    /**
     * 根据处理类型值获取对应的处理器（兼容旧接口）
     * @param typeValue 处理类型值
     * @return 对应的处理器实例
     */
    public VideoEditProcessor getProcessor(Integer typeValue) {
        String methodName = "getProcessor";
        log.debug("[{}] 兼容模式：根据类型值{}获取处理器，返回默认处理器", methodName, typeValue);
        return getDefaultProcessor();
    }

    /**
     * 检查是否支持指定的处理器名称
     * @param processorName 处理器名称
     * @return true表示支持，false表示不支持
     */
    public boolean isSupported(String processorName) {
        return StringUtils.isNotBlank(processorName) && processorMap.containsKey(processorName);
    }

    /**
     * 检查是否支持指定的API供应商
     * @param apiProvider API供应商标识
     * @return true表示支持，false表示不支持
     */
    public boolean isSupportedProvider(String apiProvider) {
        if (StringUtils.isBlank(apiProvider)) {
            return true; // 空供应商使用默认处理器
        }

        for (VideoEditProcessor processor : processorMap.values()) {
            if (processor.supportsProvider(apiProvider)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有注册的处理器
     * @return 处理器映射表
     */
    public Map<String, VideoEditProcessor> getAllProcessors() {
        return new HashMap<>(processorMap);
    }
}
