package com.nacos.service.processor.impl;

import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.entity.dto.TtsParams;
import com.nacos.model.AzureAudio.AzureAudioApiUtil;
import com.nacos.model.AzureAudio.model.TextToSpeechRequestBO;
import com.nacos.model.AzureAudio.model.TextToSpeechResponseBO;
import com.nacos.result.Result;
import com.nacos.service.processor.AudioProviderProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Microsoft Azure音频处理器
 * 
 * <p>负责处理Microsoft Azure服务商的音频生成请求，实现AudioProviderProcessor接口。
 * 该处理器复用现有的AzureAudioApiUtil工具类，实现参数转换和API调用。</p>
 * 
 * <h3>主要功能</h3>
 * <ul>
 *   <li>将通用AudioGenerationRequestDTO转换为Azure特定的请求参数</li>
 *   <li>调用AzureAudioApiUtil进行音频生成</li>
 *   <li>将Azure响应转换为通用的AudioGenerationResponseDTO</li>
 *   <li>处理Azure特定的错误情况和异常</li>
 * </ul>
 * 
 * <h3>支持的参数</h3>
 * <ul>
 *   <li><strong>基础参数</strong>：文本、语音名称、语言代码</li>
 *   <li><strong>语音设置</strong>：语速、音调</li>
 *   <li><strong>音频设置</strong>：输出格式</li>
 *   <li><strong>Azure特定</strong>：SSML支持、文件保存选项</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-14
 * @version 1.0
 */
@Slf4j
@Component
public class MicrosoftAudioProcessor implements AudioProviderProcessor {

    @Value("${azure.speech.subscription-key}")
    private String azureSubscriptionKey;

    @Value("${azure.speech.region}")
    private String azureRegion;

    /**
     * 处理Microsoft Azure音频生成请求
     * 
     * @param requestDTO 音频生成请求参数
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    public Result<AudioGenerationResponseVO> processRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        String methodName = "processRequest";
        log.info("[{}] Microsoft音频生成开始，userId={}, provider={}", methodName, userId,
                requestDTO != null ? requestDTO.getProvider() : "null");

        try {
            // 1. 参数验证
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败，userId={}", methodName, userId);
                return Result.ERROR("参数验证失败");
            }
            
            // 2. 获取Azure配置
            if (azureSubscriptionKey == null || azureRegion == null ||
                azureSubscriptionKey.trim().isEmpty() || azureRegion.trim().isEmpty()) {
                log.error("[{}] Azure语音服务配置无效，userId={}", methodName, userId);
                return Result.ERROR("Azure语音服务配置无效，请联系管理员");
            }
            
            // 3. 参数转换：AudioGenerationRequestDTO -> TextToSpeechRequestBO
            TextToSpeechRequestBO requestBO = convertToAzureBO(requestDTO);
            log.debug("[{}] 参数转换完成，userId={}, voiceName={}, language={}", 
                     methodName, userId, requestBO.getVoiceName(), requestBO.getLanguage());
            
            // 4. 调用Azure API
            String fileName = generateFileName(requestDTO, userId);
            Result<TextToSpeechResponseBO> apiResult =
                AzureAudioApiUtil.textToSpeech(requestBO, fileName, azureSubscriptionKey, azureRegion, userId);
                
            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] Azure API调用失败，userId={}, error={}", methodName, userId, apiResult.getMessage());
                return Result.ERROR("Microsoft音频生成失败：" + apiResult.getMessage());
            }
            
            // 5. 响应转换：TextToSpeechResponseBO -> AudioGenerationResponseVO
            AudioGenerationResponseVO responseVO = convertToResponseVO(apiResult.getData(), requestDTO);

            log.info("[{}] Microsoft音频生成成功，userId={}, audioUrl={}, duration={}ms",
                    methodName, userId, responseVO.getAudioUrl(), responseVO.getDurationMs());

            return Result.SUCCESS("Microsoft音频生成成功", responseVO);
            
        } catch (IllegalArgumentException e) {
            log.warn("[{}] 参数错误，userId={}, error={}", methodName, userId, e.getMessage());
            return Result.ERROR("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("[{}] Microsoft音频生成异常，userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("Microsoft音频生成异常：" + e.getMessage());
        }
    }

    /**
     * 获取服务商名称
     * 
     * @return 服务商标识
     */
    @Override
    public String getProviderName() {
        return "MICROSOFT";
    }

    /**
     * 检查是否支持指定服务商
     * 
     * @param provider 服务商标识
     * @return true表示支持，false表示不支持
     */
    @Override
    public boolean isSupported(String provider) {
        return "MICROSOFT".equalsIgnoreCase(provider);
    }

    /**
     * 获取服务商优先级
     * 
     * @return 优先级数值，Microsoft作为备用服务商设置为中等优先级
     */
    @Override
    public int getPriority() {
        return 20; // 中等优先级
    }

    /**
     * 获取服务商描述信息
     * 
     * @return 服务商描述
     */
    @Override
    public String getDescription() {
        return "Microsoft Azure语音合成服务";
    }

    /**
     * 验证Microsoft特定的请求参数
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return true表示参数有效，false表示参数无效
     */
    @Override
    public boolean isValidRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        // 调用父接口的基础验证
        if (!AudioProviderProcessor.super.isValidRequest(requestDTO, userId)) {
            return false;
        }
        
        TtsParams ttsParams = requestDTO.getTtsParams();
        
        // Microsoft特定验证
        // 1. 检查语音名称格式（Azure语音名称通常有特定格式）
        String voiceId = ttsParams.getVoiceId();
        if (voiceId == null || voiceId.trim().isEmpty()) {
            log.warn("Microsoft语音名称不能为空");
            return false;
        }
        
        // 2. 检查语速范围（Azure支持0.5-2.0）
        Float rate = ttsParams.getSpeed();
        if (rate != null && (rate < 0.5f || rate > 2.0f)) {
            log.warn("Microsoft语速超出范围[0.5, 2.0]：{}", rate);
            return false;
        }
        
        // 3. 检查音调范围（如果设置了的话）
        Float pitch = ttsParams.getExtraParam("pitch", 1.0f);
        if (pitch != null && (pitch < 0.5f || pitch > 2.0f)) {
            log.warn("Microsoft音调超出范围[0.5, 2.0]：{}", pitch);
            return false;
        }
        
        return true;
    }

    /**
     * 将通用DTO转换为Azure特定的BO
     * 
     * @param requestDTO 通用请求DTO
     * @return Azure请求BO
     */
    private TextToSpeechRequestBO convertToAzureBO(AudioGenerationRequestDTO requestDTO) {
        TtsParams ttsParams = requestDTO.getTtsParams();
        
        TextToSpeechRequestBO requestBO = new TextToSpeechRequestBO();
        
        // 设置基础参数
        requestBO.setText(ttsParams.getText());
        requestBO.setVoiceName(ttsParams.getVoiceId());
        requestBO.setLanguage(ttsParams.getExtraParam("language", "zh-CN"));
        
        // 设置语音参数
        requestBO.setRate(ttsParams.getSpeed() != null ? ttsParams.getSpeed() : 1.0f);
        requestBO.setPitch(ttsParams.getExtraParam("pitch", 1.0f));
        
        // 设置音频参数
        requestBO.setOutputFormat(ttsParams.getExtraParam("outputFormat", "Audio24Khz96KBitRateMonoMp3"));
        
        // 设置其他参数
        requestBO.setUseSsml(ttsParams.getExtraParam("useSsml", false));
        requestBO.setSaveToFile(true); // 默认保存到文件
        
        return requestBO;
    }

    /**
     * 将Azure响应转换为通用响应VO
     *
     * @param responseBO Azure响应BO
     * @param requestDTO 原始请求DTO（用于补充信息）
     * @return 通用响应VO
     */
    private AudioGenerationResponseVO convertToResponseVO(TextToSpeechResponseBO responseBO,
                                                           AudioGenerationRequestDTO requestDTO) {
        AudioGenerationResponseVO.AudioGenerationResponseVOBuilder builder =
            AudioGenerationResponseVO.builder();
        
        // 设置基础信息
        builder.audioUrl(responseBO.getUrl())
               .provider("MICROSOFT")
               .createdTime(new Date());
        
        // 设置音频信息
        if (responseBO.getDuration() > 0) {
            builder.durationMs((int) responseBO.getDuration());
        }
        
        if (responseBO.getFormat() != null) {
            builder.audioFormat(responseBO.getFormat());
        }
        
        if (responseBO.getAudioLength() > 0) {
            builder.fileSize(responseBO.getAudioLength());
        }
        
        // 设置任务相关信息
        if (requestDTO.getTaskName() != null) {
            builder.audioName(requestDTO.getTaskName());
        }
        
        return builder.build();
    }

    /**
     * 生成文件名
     * 
     * @param requestDTO 请求DTO
     * @param userId 用户ID
     * @return 文件名
     */
    private String generateFileName(AudioGenerationRequestDTO requestDTO, String userId) {
        if (requestDTO.getTaskName() != null && !requestDTO.getTaskName().trim().isEmpty()) {
            return "microsoft-" + userId + "-" + requestDTO.getTaskName();
        }
        return "microsoft-" + userId + "-" + System.currentTimeMillis();
    }


}
