package com.nacos.service.processor;

import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.result.Result;

/**
 * 音频服务提供商处理器接口
 * 
 * <p>定义统一的音频生成处理契约，所有音频服务商处理器都必须实现此接口。
 * 该接口采用策略模式设计，支持多种音频服务商的统一管理和调用。</p>
 * 
 * <h3>设计原则</h3>
 * <ul>
 *   <li><strong>统一接口</strong>：所有服务商使用相同的方法签名，简化调用方式</li>
 *   <li><strong>职责分离</strong>：每个实现类只负责一个服务商的处理逻辑</li>
 *   <li><strong>扩展性</strong>：新增服务商只需实现此接口，无需修改现有代码</li>
 *   <li><strong>错误隔离</strong>：服务商特定错误不会影响其他服务商</li>
 * </ul>
 * 
 * <h3>实现要求</h3>
 * <ul>
 *   <li>实现类必须使用 {@code @Component} 注解，以便Spring容器管理</li>
 *   <li>实现类应该复用现有的API工具类（如MiniMaxApiUtil、AzureAudioApiUtil等）</li>
 *   <li>参数转换应该在实现类内部完成，对外提供统一接口</li>
 *   <li>错误处理应该遵循项目统一的异常处理规范</li>
 *   <li>日志记录应该使用 {@code @Slf4j} 注解，保持日志格式一致</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>{@code
 * @Component
 * @Slf4j
 * public class MiniMaxAudioProcessor implements AudioProviderProcessor {
 *     
 *     @Override
 *     public Result<AudioGenerationResponseDTO> processRequest(
 *             AudioGenerationRequestDTO requestDTO, String userId) {
 *         try {
 *             // 1. 参数验证
 *             if (!isValidRequest(requestDTO, userId)) {
 *                 return Result.ERROR("参数验证失败");
 *             }
 *             
 *             // 2. 参数转换
 *             MiniMaxRequestBO requestBO = convertToMiniMaxBO(requestDTO);
 *             
 *             // 3. 调用API
 *             Result<MiniMaxResponseBO> apiResult = MiniMaxApiUtil.generateAudio(requestBO);
 *             
 *             // 4. 响应转换
 *             AudioGenerationResponseDTO responseDTO = convertToResponseDTO(apiResult.getData());
 *             
 *             return Result.SUCCESS("音频生成成功", responseDTO);
 *         } catch (Exception e) {
 *             log.error("MiniMax音频生成失败", e);
 *             return Result.ERROR("音频生成失败：" + e.getMessage());
 *         }
 *     }
 *     
 *     @Override
 *     public String getProviderName() {
 *         return "MINIMAX";
 *     }
 *     
 *     @Override
 *     public boolean isSupported(String provider) {
 *         return "MINIMAX".equalsIgnoreCase(provider);
 *     }
 * }
 * }</pre>
 * 
 * <AUTHOR>
 * @since 2025-01-14
 * @version 1.0
 */
public interface AudioProviderProcessor {

    /**
     * 处理音频生成请求
     * 
     * <p>这是接口的核心方法，负责处理音频生成的完整流程：</p>
     * <ol>
     *   <li>验证请求参数的有效性</li>
     *   <li>将通用DTO转换为服务商特定的BO</li>
     *   <li>调用服务商API进行音频生成</li>
     *   <li>将API响应转换为通用响应DTO</li>
     *   <li>处理异常情况并返回错误信息</li>
     * </ol>
     * 
     * <h4>参数验证要求</h4>
     * <ul>
     *   <li>requestDTO不能为null，且ttsParams不能为null</li>
     *   <li>userId不能为null或空字符串</li>
     *   <li>provider必须与当前处理器支持的服务商匹配</li>
     *   <li>文本内容不能为空，且长度在合理范围内</li>
     * </ul>
     * 
     * <h4>错误处理规范</h4>
     * <ul>
     *   <li>参数验证失败：返回Result.ERROR("参数验证失败：具体原因")</li>
     *   <li>API调用失败：返回Result.ERROR("API调用失败：具体原因")</li>
     *   <li>系统异常：返回Result.ERROR("系统异常：异常信息")</li>
     *   <li>服务商特定错误：根据错误码返回相应的错误信息</li>
     * </ul>
     * 
     * @param requestDTO 音频生成请求参数，包含文本内容、音色设置、服务商标识等信息
     * @param userId 用户ID，用于日志记录、限流控制、扣费等业务逻辑
     * @return 处理结果，成功时包含音频URL、时长等信息，失败时包含错误信息
     * 
     * @throws IllegalArgumentException 当参数为null或无效时抛出
     * @throws RuntimeException 当发生不可预期的系统异常时抛出
     */
    Result<AudioGenerationResponseVO> processRequest(AudioGenerationRequestDTO requestDTO, String userId);

    /**
     * 获取服务商名称
     * 
     * <p>返回当前处理器支持的服务商标识，用于工厂类进行处理器注册和查找。</p>
     * 
     * <h4>命名规范</h4>
     * <ul>
     *   <li>必须使用大写字母，如"MINIMAX"、"MICROSOFT"、"ALIYUN"</li>
     *   <li>必须与AudioProviderEnum中定义的代码保持一致</li>
     *   <li>不能包含空格或特殊字符</li>
     *   <li>应该具有明确的业务含义</li>
     * </ul>
     * 
     * @return 服务商标识，如"MINIMAX"、"MICROSOFT"等
     */
    String getProviderName();

    /**
     * 检查是否支持指定服务商
     * 
     * <p>用于验证当前处理器是否能够处理指定的服务商请求。
     * 通常情况下，实现类只需要检查provider是否与getProviderName()返回值匹配。</p>
     * 
     * <h4>实现建议</h4>
     * <pre>{@code
     * @Override
     * public boolean isSupported(String provider) {
     *     return getProviderName().equalsIgnoreCase(provider);
     * }
     * }</pre>
     * 
     * @param provider 服务商标识，支持大小写不敏感匹配
     * @return true表示支持该服务商，false表示不支持
     */
    boolean isSupported(String provider);

    /**
     * 获取服务商优先级
     * 
     * <p>用于负载均衡和服务商选择策略。数值越小表示优先级越高。</p>
     * 
     * <h4>优先级建议</h4>
     * <ul>
     *   <li>主要服务商：1-10（如MiniMax可以设置为1）</li>
     *   <li>备用服务商：11-50（如Microsoft可以设置为20）</li>
     *   <li>测试服务商：51-100（如新接入的服务商）</li>
     * </ul>
     * 
     * @return 优先级数值，默认为100
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 检查服务商健康状态
     * 
     * <p>用于服务商健康检查和故障转移。实现类可以根据实际情况进行健康检查，
     * 如检查API连通性、响应时间、错误率等指标。</p>
     * 
     * <h4>健康检查建议</h4>
     * <ul>
     *   <li>简单实现：直接返回true（默认健康）</li>
     *   <li>基础检查：检查配置是否完整（API密钥、端点等）</li>
     *   <li>高级检查：调用服务商健康检查接口</li>
     *   <li>缓存结果：避免频繁的健康检查调用</li>
     * </ul>
     * 
     * @return true表示服务商健康可用，false表示服务商不可用
     */
    default boolean isHealthy() {
        return true;
    }

    /**
     * 获取服务商描述信息
     * 
     * <p>返回服务商的详细描述信息，用于日志记录、监控展示等场景。</p>
     * 
     * @return 服务商描述信息，如"MiniMax语音合成服务"
     */
    default String getDescription() {
        return getProviderName() + "音频生成服务";
    }

    /**
     * 验证请求参数
     * 
     * <p>提供默认的参数验证逻辑，实现类可以重写此方法添加服务商特定的验证规则。</p>
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return true表示参数有效，false表示参数无效
     */
    default boolean isValidRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        if (requestDTO == null || requestDTO.getTtsParams() == null) {
            return false;
        }
        
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        if (!isSupported(requestDTO.getProvider())) {
            return false;
        }
        
        String text = requestDTO.getTtsParams().getText();
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        // 检查文本长度（可以根据实际需求调整）
        if (text.length() > 10000) {
            return false;
        }
        
        return true;
    }
}
