package com.nacos.service.processor;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.result.Result;

/**
 * 视频翻译服务提供商处理器接口
 * 
 * <p>定义统一的视频翻译处理契约，所有视频翻译服务商处理器都必须实现此接口。
 * 该接口采用策略模式设计，支持多种视频翻译服务商的统一管理和调用。</p>
 * 
 * <h3>设计原则</h3>
 * <ul>
 *   <li><strong>统一接口</strong>：所有服务商使用相同的方法签名，简化调用方式</li>
 *   <li><strong>职责分离</strong>：每个实现类只负责一个服务商的处理逻辑</li>
 *   <li><strong>扩展性</strong>：新增服务商只需实现此接口，无需修改现有代码</li>
 *   <li><strong>错误隔离</strong>：服务商特定错误不会影响其他服务商</li>
 * </ul>
 * 
 * <h3>实现要求</h3>
 * <ul>
 *   <li>实现类必须使用 {@code @Component} 注解，以便Spring容器管理</li>
 *   <li>实现类应该复用现有的API工具类（如SoundViewApiUtil、AliVideoApiUtil等）</li>
 *   <li>参数转换应该在实现类内部完成，对外提供统一接口</li>
 *   <li>错误处理应该遵循项目统一的异常处理规范</li>
 *   <li>日志记录应该使用 {@code @Slf4j} 注解，保持日志格式一致</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>{@code
 * @Component
 * @Slf4j
 * public class LingyangVideoTranslateProcessor implements VideoTranslateProcessor {
 *     
 *     @Override
 *     public Result<VideoTranslateResult> processRequest(
 *             VideoTranslateRequestDTO requestDTO, String userId) {
 *         try {
 *             // 1. 参数验证
 *             if (!isValidRequest(requestDTO, userId)) {
 *                 return Result.ERROR("参数验证失败");
 *             }
 *             
 *             // 2. 参数转换
 *             VideoTranslateRequestBO requestBO = convertToLingyangBO(requestDTO);
 *             
 *             // 3. 调用API
 *             Result<TaskStatusResponseBO> apiResult = SoundViewApiUtil.submitVideoTranslation(requestBO);
 *             
 *             // 4. 响应转换
 *             VideoTranslateResult result = convertToResult(apiResult.getData());
 *             
 *             return Result.SUCCESS("视频翻译任务提交成功", result);
 *         } catch (Exception e) {
 *             log.error("羚羊平台视频翻译失败", e);
 *             return Result.ERROR("视频翻译失败：" + e.getMessage());
 *         }
 *     }
 *     
 *     @Override
 *     public String getProviderName() {
 *         return "LINGYANG";
 *     }
 *     
 *     @Override
 *     public boolean isSupported(String provider) {
 *         return "LINGYANG".equalsIgnoreCase(provider);
 *     }
 * }
 * }</pre>
 * 
 * <AUTHOR>
 * @since 2025-01-29
 * @version 1.0
 */
public interface VideoTranslateProcessor {

    /**
     * 处理视频翻译请求
     * 
     * <p>这是接口的核心方法，负责处理视频翻译的完整流程：</p>
     * <ol>
     *   <li>验证请求参数的有效性</li>
     *   <li>将通用DTO转换为服务商特定的BO</li>
     *   <li>调用服务商API进行视频翻译任务提交</li>
     *   <li>将API响应转换为通用响应结果</li>
     *   <li>处理异常情况并返回错误信息</li>
     * </ol>
     * 
     * <h4>参数验证要求</h4>
     * <ul>
     *   <li>requestDTO不能为null，且必要字段不能为空</li>
     *   <li>userId不能为null或空字符串</li>
     *   <li>provider必须与当前处理器支持的服务商匹配</li>
     *   <li>视频URL必须有效且可访问</li>
     *   <li>源语言和目标语言必须在支持范围内</li>
     * </ul>
     * 
     * <h4>错误处理规范</h4>
     * <ul>
     *   <li>参数验证失败：返回Result.ERROR("参数验证失败：具体原因")</li>
     *   <li>API调用失败：返回Result.ERROR("API调用失败：具体原因")</li>
     *   <li>系统异常：返回Result.ERROR("系统异常：异常信息")</li>
     *   <li>服务商特定错误：根据错误码返回相应的错误信息</li>
     * </ul>
     * 
     * @param requestDTO 视频翻译请求参数，包含视频URL、源语言、目标语言、音色ID等信息
     * @param userId 用户ID，用于日志记录、限流控制、扣费等业务逻辑
     * @return 处理结果，成功时包含任务ID、状态等信息，失败时包含错误信息
     * 
     * @throws IllegalArgumentException 当参数为null或无效时抛出
     * @throws RuntimeException 当发生不可预期的系统异常时抛出
     */
    Result<VideoTranslateResult> processRequest(VideoTranslateRequestDTO requestDTO, String userId);

    /**
     * 检查视频翻译任务状态
     * 
     * <p>查询指定任务的当前状态，包括处理进度、结果URL等信息。</p>
     * 
     * <h4>状态查询要求</h4>
     * <ul>
     *   <li>providerTaskId不能为null或空字符串</li>
     *   <li>返回的状态信息应该包含进度百分比</li>
     *   <li>如果任务完成，应该包含结果视频URL</li>
     *   <li>如果任务失败，应该包含错误信息</li>
     * </ul>
     * 
     * @param providerTaskId 服务商任务ID，由processRequest方法返回
     * @return 任务状态结果，包含状态、进度、结果URL等信息
     */
    Result<VideoTranslateResult> checkTaskStatus(String providerTaskId);

    /**
     * 获取服务商名称
     * 
     * <p>返回当前处理器支持的服务商标识，用于工厂类进行处理器注册和查找。</p>
     * 
     * <h4>命名规范</h4>
     * <ul>
     *   <li>必须使用大写字母，如"LINGYANG"、"ALIYUN"、"TENCENT"</li>
     *   <li>必须与VideoTranslateProviderEnum中定义的代码保持一致</li>
     *   <li>不能包含空格或特殊字符</li>
     *   <li>应该具有明确的业务含义</li>
     * </ul>
     * 
     * @return 服务商标识，如"LINGYANG"、"ALIYUN"等
     */
    String getProviderName();

    /**
     * 检查是否支持指定服务商
     * 
     * <p>用于验证当前处理器是否能够处理指定的服务商请求。
     * 通常情况下，实现类只需要检查provider是否与getProviderName()返回值匹配。</p>
     * 
     * <h4>实现建议</h4>
     * <pre>{@code
     * @Override
     * public boolean isSupported(String provider) {
     *     return getProviderName().equalsIgnoreCase(provider);
     * }
     * }</pre>
     * 
     * @param provider 服务商标识，支持大小写不敏感匹配
     * @return true表示支持该服务商，false表示不支持
     */
    boolean isSupported(String provider);

    /**
     * 获取服务商优先级
     * 
     * <p>用于负载均衡和服务商选择策略。数值越小表示优先级越高。</p>
     * 
     * <h4>优先级建议</h4>
     * <ul>
     *   <li>主要服务商：1-10（如羚羊平台可以设置为1）</li>
     *   <li>备用服务商：11-50（如阿里云可以设置为20）</li>
     *   <li>测试服务商：51-100（如新接入的服务商）</li>
     * </ul>
     * 
     * @return 优先级数值，默认为100
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 检查服务商健康状态
     * 
     * <p>用于服务商健康检查和故障转移。实现类可以根据实际情况进行健康检查，
     * 如检查API连通性、响应时间、错误率等指标。</p>
     * 
     * <h4>健康检查建议</h4>
     * <ul>
     *   <li>简单实现：直接返回true（默认健康）</li>
     *   <li>基础检查：检查配置是否完整（API密钥、端点等）</li>
     *   <li>高级检查：调用服务商健康检查接口</li>
     *   <li>缓存结果：避免频繁的健康检查调用</li>
     * </ul>
     * 
     * @return true表示服务商健康可用，false表示服务商不可用
     */
    default boolean isHealthy() {
        return true;
    }

    /**
     * 获取服务商描述信息
     * 
     * <p>返回服务商的详细描述信息，用于日志记录、监控展示等场景。</p>
     * 
     * @return 服务商描述信息，如"羚羊平台视频翻译服务"
     */
    default String getDescription() {
        return getProviderName() + "视频翻译服务";
    }

    /**
     * 验证请求参数
     * 
     * <p>提供默认的参数验证逻辑，实现类可以重写此方法添加服务商特定的验证规则。</p>
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return true表示参数有效，false表示参数无效
     */
    default boolean isValidRequest(VideoTranslateRequestDTO requestDTO, String userId) {
        if (requestDTO == null) {
            return false;
        }
        
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        if (requestDTO.getVideoUrl() == null || requestDTO.getVideoUrl().trim().isEmpty()) {
            return false;
        }
        
        if (requestDTO.getSourceLanguage() == null || requestDTO.getSourceLanguage().trim().isEmpty()) {
            return false;
        }
        
        if (requestDTO.getTargetLanguage() == null || requestDTO.getTargetLanguage().trim().isEmpty()) {
            return false;
        }
        
        if (requestDTO.getVoiceId() == null || requestDTO.getVoiceId().trim().isEmpty()) {
            return false;
        }
        
        // 检查源语言和目标语言是否相同
        if (requestDTO.isSameLanguage()) {
            return false;
        }
        
        return true;
    }

    /**
     * 视频翻译处理结果
     * 
     * <p>封装视频翻译处理的结果信息，包含任务状态、进度、结果URL等。</p>
     */
    class VideoTranslateResult {
        
        /**
         * 本地任务ID
         */
        private String taskId;
        
        /**
         * 服务商任务ID
         */
        private String providerTaskId;
        
        /**
         * 任务状态：submitted-已提交, processing-处理中, completed-已完成, failed-失败, cancelled-已取消, timeout-超时
         */
        private String status;
        
        /**
         * 处理进度百分比(0-100)
         */
        private Integer progress;
        
        /**
         * 结果视频URL
         */
        private String resultVideoUrl;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 处理耗时(秒)
         */
        private Long processingDuration;
        
        /**
         * 费用金额
         */
        private Double cost;
        
        /**
         * 服务商名称
         */
        private String providerName;
        
        /**
         * 创建时间戳
         */
        private Long createTime;
        
        /**
         * 更新时间戳
         */
        private Long updateTime;

        // 构造方法
        public VideoTranslateResult() {
            this.createTime = System.currentTimeMillis();
            this.updateTime = System.currentTimeMillis();
        }

        public VideoTranslateResult(String taskId, String providerTaskId, String status) {
            this();
            this.taskId = taskId;
            this.providerTaskId = providerTaskId;
            this.status = status;
        }

        public VideoTranslateResult(String taskId, String providerTaskId, String status, String providerName) {
            this(taskId, providerTaskId, status);
            this.providerName = providerName;
        }

        // Getter和Setter方法
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; this.updateTime = System.currentTimeMillis(); }

        public String getProviderTaskId() { return providerTaskId; }
        public void setProviderTaskId(String providerTaskId) { this.providerTaskId = providerTaskId; this.updateTime = System.currentTimeMillis(); }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; this.updateTime = System.currentTimeMillis(); }

        public Integer getProgress() { return progress; }
        public void setProgress(Integer progress) { this.progress = progress; this.updateTime = System.currentTimeMillis(); }

        public String getResultVideoUrl() { return resultVideoUrl; }
        public void setResultVideoUrl(String resultVideoUrl) { this.resultVideoUrl = resultVideoUrl; this.updateTime = System.currentTimeMillis(); }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; this.updateTime = System.currentTimeMillis(); }

        public Long getProcessingDuration() { return processingDuration; }
        public void setProcessingDuration(Long processingDuration) { this.processingDuration = processingDuration; this.updateTime = System.currentTimeMillis(); }

        public Double getCost() { return cost; }
        public void setCost(Double cost) { this.cost = cost; this.updateTime = System.currentTimeMillis(); }

        public String getProviderName() { return providerName; }
        public void setProviderName(String providerName) { this.providerName = providerName; this.updateTime = System.currentTimeMillis(); }

        public Long getCreateTime() { return createTime; }
        public void setCreateTime(Long createTime) { this.createTime = createTime; }

        public Long getUpdateTime() { return updateTime; }
        public void setUpdateTime(Long updateTime) { this.updateTime = updateTime; }

        @Override
        public String toString() {
            return "VideoTranslateResult{" +
                    "taskId='" + taskId + '\'' +
                    ", providerTaskId='" + providerTaskId + '\'' +
                    ", status='" + status + '\'' +
                    ", progress=" + progress +
                    ", resultVideoUrl='" + resultVideoUrl + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", processingDuration=" + processingDuration +
                    ", cost=" + cost +
                    ", providerName='" + providerName + '\'' +
                    ", createTime=" + createTime +
                    ", updateTime=" + updateTime +
                    '}';
        }
    }
}
