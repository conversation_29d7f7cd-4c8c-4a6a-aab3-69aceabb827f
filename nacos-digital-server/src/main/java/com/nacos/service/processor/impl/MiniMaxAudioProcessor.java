package com.nacos.service.processor.impl;

import com.nacos.constants.MiniMaxDefaults;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.dto.TtsParams;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationResponseBO;
import com.nacos.result.Result;
import com.nacos.service.processor.AudioProviderProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * MiniMax音频处理器
 * 
 * <p>负责处理MiniMax服务商的音频生成请求，实现AudioProviderProcessor接口。
 * 该处理器复用现有的MiniMaxApiUtil工具类，实现参数转换和API调用。</p>
 * 
 * <h3>主要功能</h3>
 * <ul>
 *   <li>将通用AudioGenerationRequestDTO转换为MiniMax特定的请求参数</li>
 *   <li>调用MiniMaxApiUtil进行音频生成</li>
 *   <li>将MiniMax响应转换为通用的AudioGenerationResponseDTO</li>
 *   <li>处理MiniMax特定的错误情况和异常</li>
 * </ul>
 * 
 * <h3>支持的参数</h3>
 * <ul>
 *   <li><strong>基础参数</strong>：文本、音色ID、语速</li>
 *   <li><strong>音色设置</strong>：音量、语调、情感、LaTeX读取</li>
 *   <li><strong>音频设置</strong>：采样率、比特率、格式、声道数</li>
 *   <li><strong>模型设置</strong>：支持speech-01-turbo、speech-02-turbo等模型</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-14
 * @version 1.0
 */
@Slf4j
@Component
public class MiniMaxAudioProcessor implements AudioProviderProcessor {

    /**
     * 处理MiniMax音频生成请求
     * 
     * @param requestDTO 音频生成请求参数
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    public Result<AudioGenerationResponseVO> processRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        String methodName = "processRequest";
        log.info("[{}] MiniMax音频生成开始，userId={}, provider={}", methodName, userId,
                requestDTO != null ? requestDTO.getProvider() : "null");

        try {
            // 1. 参数验证
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败，userId={}", methodName, userId);
                return Result.ERROR("参数验证失败");
            }

            // 2. 参数转换：AudioGenerationRequestDTO -> MiniMaxVoiceGenerationRequestBO
            MiniMaxVoiceGenerationRequestBO requestBO = convertToMiniMaxBO(requestDTO);
            log.debug("[{}] 参数转换完成，userId={}, model={}, voiceId={}",
                     methodName, userId, requestBO.getModel(), requestBO.getVoiceSetting().getVoiceId());

            // 3. 调用MiniMax API
            Result<MiniMaxVoiceGenerationResponseBO> apiResult =
                MiniMaxApiUtil.generateAndProcessAudio(requestBO, userId, null, 4);

            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] MiniMax API调用失败，userId={}, error={}", methodName, userId, apiResult.getMessage());
                return Result.ERROR("MiniMax音频生成失败：" + apiResult.getMessage());
            }

            // 4. 响应转换：MiniMaxVoiceGenerationResponseBO -> AudioGenerationResponseVO
            AudioGenerationResponseVO responseVO = convertToResponseVO(apiResult.getData(), requestDTO);

            log.info("[{}] MiniMax音频生成成功，userId={}, audioUrl={}, duration={}ms",
                    methodName, userId, responseVO.getAudioUrl(), responseVO.getDurationMs());

            return Result.SUCCESS("MiniMax音频生成成功", responseVO);
            
        } catch (IllegalArgumentException e) {
            log.warn("[{}] 参数错误，userId={}, error={}", methodName, userId, e.getMessage());
            return Result.ERROR("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("[{}] MiniMax音频生成异常，userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("MiniMax音频生成异常：" + e.getMessage());
        }
    }

    /**
     * 获取服务商名称
     * 
     * @return 服务商标识
     */
    @Override
    public String getProviderName() {
        return "MINIMAX";
    }

    /**
     * 检查是否支持指定服务商
     * 
     * @param provider 服务商标识
     * @return true表示支持，false表示不支持
     */
    @Override
    public boolean isSupported(String provider) {
        return "MINIMAX".equalsIgnoreCase(provider);
    }

    /**
     * 获取服务商优先级
     * 
     * @return 优先级数值，MiniMax作为主要服务商设置为最高优先级
     */
    @Override
    public int getPriority() {
        return 1; // 最高优先级
    }

    /**
     * 获取服务商描述信息
     * 
     * @return 服务商描述
     */
    @Override
    public String getDescription() {
        return "MiniMax语音合成服务";
    }

    /**
     * 验证MiniMax特定的请求参数
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return true表示参数有效，false表示参数无效
     */
    @Override
    public boolean isValidRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        // 调用父接口的基础验证
        if (!AudioProviderProcessor.super.isValidRequest(requestDTO, userId)) {
            return false;
        }
        
        TtsParams ttsParams = requestDTO.getTtsParams();
        
        // MiniMax特定验证
        // 1. 检查音色ID格式（MiniMax音色ID通常有特定格式）
        String voiceId = ttsParams.getVoiceId();
        if (voiceId == null || voiceId.trim().isEmpty()) {
            log.warn("MiniMax音色ID不能为空");
            return false;
        }
        
        // 2. 检查语速范围（MiniMax支持0.5-2.0）
        Float speed = ttsParams.getSpeed();
        if (speed != null && (speed < 0.5f || speed > 2.0f)) {
            log.warn("MiniMax语速超出范围[0.5, 2.0]：{}", speed);
            return false;
        }
        
        // 3. 检查音量范围（如果设置了的话）
        Float vol = ttsParams.getExtraParam("vol", 1.0f);
        if (vol != null && (vol < 0.1f || vol > 10.0f)) {
            log.warn("MiniMax音量超出范围[0.1, 10.0]：{}", vol);
            return false;
        }
        
        // 4. 检查语调范围（如果设置了的话）
        Integer pitch = ttsParams.getExtraParam("pitch", 0);
        if (pitch != null && (pitch < -12 || pitch > 12)) {
            log.warn("MiniMax语调超出范围[-12, 12]：{}", pitch);
            return false;
        }

        // 5. 检查字幕功能模型兼容性
        Boolean subtitleEnable = ttsParams.getExtraParam("subtitleEnable", MiniMaxDefaults.DEFAULT_SUBTITLE_ENABLE);
        if (subtitleEnable != null && subtitleEnable) {
            String model = ttsParams.getExtraParam("model", MiniMaxDefaults.DEFAULT_MODEL);
            if (!MiniMaxDefaults.isSubtitleSupported(model)) {
                log.warn("字幕功能仅支持speech-01-turbo和speech-01-hd模型，当前模型：{}", model);
                return false;
            }
        }

        return true;
    }

    /**
     * 将通用DTO转换为MiniMax特定的BO
     * 
     * @param requestDTO 通用请求DTO
     * @return MiniMax请求BO
     */
    private MiniMaxVoiceGenerationRequestBO convertToMiniMaxBO(AudioGenerationRequestDTO requestDTO) {
        TtsParams ttsParams = requestDTO.getTtsParams();
        
        MiniMaxVoiceGenerationRequestBO requestBO = new MiniMaxVoiceGenerationRequestBO();
        
        // 设置基础参数
        requestBO.setModel(ttsParams.getExtraParam("model", MiniMaxDefaults.DEFAULT_MODEL));
        requestBO.setText(ttsParams.getText());

        // 设置音色参数
        MiniMaxVoiceGenerationRequestBO.VoiceSetting voiceSetting =
            new MiniMaxVoiceGenerationRequestBO.VoiceSetting();
        voiceSetting.setVoiceId(ttsParams.getVoiceId());
        voiceSetting.setSpeed(ttsParams.getSpeed() != null ? ttsParams.getSpeed() : MiniMaxDefaults.DEFAULT_SPEED);
        voiceSetting.setVol(ttsParams.getExtraParam("vol", MiniMaxDefaults.DEFAULT_VOL));
        voiceSetting.setPitch(ttsParams.getExtraParam("pitch", MiniMaxDefaults.DEFAULT_PITCH));
        voiceSetting.setEmotion(ttsParams.getExtraParam("emotion", MiniMaxDefaults.DEFAULT_EMOTION));
        voiceSetting.setLatexRead(ttsParams.getExtraParam("latexRead", MiniMaxDefaults.DEFAULT_LATEX_READ));
        requestBO.setVoiceSetting(voiceSetting);
        
        // 设置音频参数
        MiniMaxVoiceGenerationRequestBO.AudioSetting audioSetting =
            new MiniMaxVoiceGenerationRequestBO.AudioSetting();
        audioSetting.setSampleRate(ttsParams.getExtraParam("sampleRate", MiniMaxDefaults.DEFAULT_SAMPLE_RATE));
        audioSetting.setBitrate(ttsParams.getExtraParam("bitRate", MiniMaxDefaults.DEFAULT_BIT_RATE));
        audioSetting.setFormat(ttsParams.getExtraParam("audioFormat", MiniMaxDefaults.DEFAULT_AUDIO_FORMAT));
        audioSetting.setChannel(MiniMaxDefaults.DEFAULT_CHANNEL);
        requestBO.setAudioSetting(audioSetting);

        // 设置其他参数
        requestBO.setStream(MiniMaxDefaults.DEFAULT_STREAM);
        requestBO.setSubtitleEnable(ttsParams.getExtraParam("subtitleEnable", MiniMaxDefaults.DEFAULT_SUBTITLE_ENABLE));
        
        // 如果有语言增强设置
        String languageBoost = ttsParams.getExtraParam("languageBoost");
        if (languageBoost != null && !languageBoost.trim().isEmpty()) {
            requestBO.setLanguageBoost(languageBoost);
        }
        
        return requestBO;
    }

    /**
     * 将MiniMax响应转换为通用响应VO
     *
     * @param responseBO MiniMax响应BO
     * @param requestDTO 原始请求DTO（用于补充信息）
     * @return 通用响应VO
     */
    private AudioGenerationResponseVO convertToResponseVO(MiniMaxVoiceGenerationResponseBO responseBO,
                                                           AudioGenerationRequestDTO requestDTO) {
        AudioGenerationResponseVO.AudioGenerationResponseVOBuilder builder =
            AudioGenerationResponseVO.builder();

        // 设置基础信息
        builder.audioUrl(responseBO.getAudioUrl())
               .provider("MINIMAX")
               .createdTime(new Date());

        // 设置扩展信息（如果存在）
        if (responseBO.getExtraInfo() != null) {
            MiniMaxVoiceGenerationResponseBO.ExtraInfo extraInfo = responseBO.getExtraInfo();

            // 音频时长（毫秒）
            if (extraInfo.getAudioLength() != null) {
                builder.durationMs(extraInfo.getAudioLength().intValue());
            }

            // 音频格式
            if (extraInfo.getAudioFormat() != null) {
                builder.audioFormat(extraInfo.getAudioFormat());
            }

            // 音频大小（字节）- 使用fileSize字段
            if (extraInfo.getAudioSize() != null) {
                builder.fileSize(extraInfo.getAudioSize());
            }
        }

        // 设置任务相关信息
        if (requestDTO.getTaskName() != null) {
            builder.audioName(requestDTO.getTaskName());
        }

        // 设置字幕信息（如果存在）
        if (responseBO.getData() != null && responseBO.getData().getSubtitleFile() != null) {
            String subtitleUrl = responseBO.getData().getSubtitleFile();

            // 保持向后兼容：设置字幕文件URL
            // builder.subtitleFileUrl(subtitleUrl);

            // 新增：获取并设置字幕内容
            try {
                List<AudioGenerationResponseVO.SubtitleInfo> subtitles =
                    MiniMaxApiUtil.processSubtitles(subtitleUrl);
                builder.subtitles(subtitles);

                if (!subtitles.isEmpty()) {
                    log.debug("字幕内容处理成功，字幕数量: {}", subtitles.size());
                }
            } catch (Exception e) {
                log.warn("字幕处理失败，保留字幕文件URL: {}", e.getMessage());
            }
        }

        return builder.build();
    }

}
