package com.nacos.service.processor;

import com.nacos.config.VideoTranslateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 视频翻译提供商处理器工厂类
 * 
 * <p>负责管理所有视频翻译处理器实例，提供根据服务商名称获取对应处理器的功能。
 * 使用Spring的依赖注入机制自动发现和管理所有VideoTranslateProcessor实现类。</p>
 * 
 * <h3>主要功能</h3>
 * <ul>
 *   <li>自动发现和注册所有VideoTranslateProcessor实现类</li>
 *   <li>提供根据服务商名称获取处理器的功能</li>
 *   <li>支持处理器优先级排序和健康状态检查</li>
 *   <li>提供服务商列表查询和处理器统计功能</li>
 * </ul>
 * 
 * <h3>设计特点</h3>
 * <ul>
 *   <li><strong>自动扩展</strong>：新增处理器只需实现接口并添加@Component注解，无需修改工厂代码</li>
 *   <li><strong>线程安全</strong>：使用ConcurrentHashMap确保多线程环境下的安全性</li>
 *   <li><strong>故障转移</strong>：支持健康检查和备用处理器选择</li>
 *   <li><strong>性能优化</strong>：处理器映射缓存，避免重复查找</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>{@code
 * @Autowired
 * private VideoTranslateProcessorFactory factory;
 * 
 * // 获取指定服务商的处理器
 * Optional<VideoTranslateProcessor> processor = factory.getProcessor("LINGYANG");
 * 
 * // 获取健康的处理器（按优先级排序）
 * List<VideoTranslateProcessor> healthyProcessors = factory.getHealthyProcessors();
 * 
 * // 获取支持的服务商列表
 * Set<String> supportedProviders = factory.getSupportedProviders();
 * }</pre>
 * 
 * <AUTHOR>
 * @since 2025-01-29
 * @version 1.0
 */
@Slf4j
@Component
public class VideoTranslateProcessorFactory {

    /**
     * 所有视频翻译处理器实例列表
     * 通过Spring构造函数注入自动获取所有VideoTranslateProcessor实现类
     */
    private final List<VideoTranslateProcessor> processors;

    /**
     * 视频翻译配置
     */
    private final VideoTranslateConfig config;

    /**
     * 处理器映射缓存
     * Key: 服务商名称（大写），Value: 对应的处理器实例
     */
    private final Map<String, VideoTranslateProcessor> processorMap = new ConcurrentHashMap<>();

    /**
     * 按优先级排序的处理器列表缓存
     */
    private volatile List<VideoTranslateProcessor> sortedProcessors;

    /**
     * 构造函数，通过Spring依赖注入获取所有处理器实例和配置
     *
     * @param processors Spring自动注入的所有VideoTranslateProcessor实现类
     * @param config 视频翻译配置
     */
    public VideoTranslateProcessorFactory(List<VideoTranslateProcessor> processors, VideoTranslateConfig config) {
        this.processors = processors != null ? processors : new ArrayList<>();
        this.config = config;
        log.info("VideoTranslateProcessorFactory构造完成，发现{}个处理器", this.processors.size());
    }

    /**
     * 初始化处理器映射和缓存
     * 在Spring容器完成依赖注入后自动调用
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化视频翻译处理器工厂...");

        // 初始化配置默认值
        config.initDefaultConfigs();

        // 验证配置
        if (!config.validateConfig()) {
            log.warn("视频翻译配置验证失败，将使用默认配置");
        }

        // 清空现有映射
        processorMap.clear();

        // 注册所有处理器
        for (VideoTranslateProcessor processor : processors) {
            registerProcessor(processor);
        }

        // 初始化排序缓存
        refreshSortedProcessors();

        log.info("视频翻译处理器工厂初始化完成 - 注册处理器数量: {}, 支持的服务商: {}",
                processorMap.size(), getSupportedProviders());

        // 输出处理器详细信息
        logProcessorDetails();

        // 输出配置信息
        logConfigDetails();
    }

    /**
     * 注册单个处理器
     * 
     * @param processor 要注册的处理器
     */
    private void registerProcessor(VideoTranslateProcessor processor) {
        try {
            String providerName = processor.getProviderName();
            if (providerName == null || providerName.trim().isEmpty()) {
                log.warn("处理器{}的服务商名称为空，跳过注册", processor.getClass().getSimpleName());
                return;
            }
            
            String upperProviderName = providerName.toUpperCase();
            
            // 检查是否已存在同名处理器
            if (processorMap.containsKey(upperProviderName)) {
                VideoTranslateProcessor existingProcessor = processorMap.get(upperProviderName);
                log.warn("发现重复的服务商处理器 - 服务商: {}, 现有: {}, 新增: {}", 
                        upperProviderName, 
                        existingProcessor.getClass().getSimpleName(),
                        processor.getClass().getSimpleName());
                
                // 根据优先级决定是否替换
                if (processor.getPriority() < existingProcessor.getPriority()) {
                    processorMap.put(upperProviderName, processor);
                    log.info("替换为更高优先级的处理器 - 服务商: {}, 处理器: {}, 优先级: {}", 
                            upperProviderName, processor.getClass().getSimpleName(), processor.getPriority());
                }
            } else {
                processorMap.put(upperProviderName, processor);
                log.info("注册视频翻译处理器 - 服务商: {}, 处理器: {}, 优先级: {}, 描述: {}", 
                        upperProviderName, 
                        processor.getClass().getSimpleName(), 
                        processor.getPriority(),
                        processor.getDescription());
            }
        } catch (Exception e) {
            log.error("注册处理器失败 - 处理器: {}, 错误: {}", 
                     processor.getClass().getSimpleName(), e.getMessage(), e);
        }
    }

    /**
     * 根据服务商名称获取对应的处理器
     *
     * @param provider 服务商名称，支持大小写不敏感
     * @return 处理器实例，如果不存在则返回Optional.empty()
     */
    public Optional<VideoTranslateProcessor> getProcessor(String provider) {
        if (provider == null || provider.trim().isEmpty()) {
            log.warn("服务商名称为空，无法获取处理器");
            return Optional.empty();
        }

        String upperProvider = provider.toUpperCase();

        // 检查配置中是否启用该服务商
        if (!config.isProviderEnabled(upperProvider)) {
            log.warn("服务商{}在配置中被禁用", provider);
            return Optional.empty();
        }

        VideoTranslateProcessor processor = processorMap.get(upperProvider);

        if (processor == null) {
            log.debug("未找到服务商{}的处理器", provider);
            return Optional.empty();
        }

        // 检查处理器健康状态
        if (!processor.isHealthy()) {
            log.warn("处理器{}健康检查失败，建议使用备用处理器", processor.getClass().getSimpleName());
            // 注意：这里不直接返回empty，而是返回处理器，让调用方决定是否使用
        }

        return Optional.of(processor);
    }

    /**
     * 获取健康的处理器列表，按优先级排序
     * 只返回配置中启用的处理器
     *
     * @return 健康的处理器列表
     */
    public List<VideoTranslateProcessor> getHealthyProcessors() {
        return getSortedProcessors().stream()
                .filter(processor -> config.isProviderEnabled(processor.getProviderName()))
                .filter(VideoTranslateProcessor::isHealthy)
                .collect(Collectors.toList());
    }

    /**
     * 获取按优先级排序的所有处理器列表
     * 
     * @return 排序后的处理器列表
     */
    public List<VideoTranslateProcessor> getSortedProcessors() {
        if (sortedProcessors == null) {
            refreshSortedProcessors();
        }
        return new ArrayList<>(sortedProcessors);
    }

    /**
     * 获取支持的服务商名称集合
     * 
     * @return 服务商名称集合
     */
    public Set<String> getSupportedProviders() {
        return new HashSet<>(processorMap.keySet());
    }

    /**
     * 获取处理器统计信息
     * 
     * @return 统计信息映射
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProcessors", processorMap.size());
        stats.put("healthyProcessors", getHealthyProcessors().size());
        stats.put("supportedProviders", getSupportedProviders());
        
        // 按优先级分组统计
        Map<Integer, Long> priorityStats = processorMap.values().stream()
                .collect(Collectors.groupingBy(
                        VideoTranslateProcessor::getPriority,
                        Collectors.counting()
                ));
        stats.put("priorityDistribution", priorityStats);
        
        return stats;
    }

    /**
     * 检查是否支持指定的服务商
     * 
     * @param provider 服务商名称
     * @return true表示支持，false表示不支持
     */
    public boolean isSupported(String provider) {
        return getProcessor(provider).isPresent();
    }



    /**
     * 刷新排序缓存
     */
    private void refreshSortedProcessors() {
        sortedProcessors = processorMap.values().stream()
                .sorted(Comparator.comparingInt(VideoTranslateProcessor::getPriority))
                .collect(Collectors.toList());
    }

    /**
     * 输出处理器详细信息到日志
     */
    private void logProcessorDetails() {
        if (log.isInfoEnabled()) {
            log.info("=== 视频翻译处理器详细信息 ===");
            getSortedProcessors().forEach(processor -> {
                log.info("处理器: {} | 服务商: {} | 优先级: {} | 健康状态: {} | 描述: {}", 
                        processor.getClass().getSimpleName(),
                        processor.getProviderName(),
                        processor.getPriority(),
                        processor.isHealthy() ? "健康" : "异常",
                        processor.getDescription());
            });
            log.info("=== 处理器信息输出完成 ===");
        }
    }

    /**
     * 重新加载处理器映射
     * 用于运行时动态刷新处理器状态
     */
    public void reload() {
        log.info("重新加载视频翻译处理器工厂...");
        init();
    }

    /**
     * 获取默认服务商处理器
     *
     * @return 默认服务商处理器
     */
    public Optional<VideoTranslateProcessor> getDefaultProcessor() {
        String defaultProvider = config.getDefaultProvider();
        return getProcessor(defaultProvider);
    }

    /**
     * 获取配置信息
     *
     * @return 配置信息
     */
    public VideoTranslateConfig getConfig() {
        return config;
    }

    /**
     * 获取启用的服务商列表
     *
     * @return 启用的服务商名称集合
     */
    public Set<String> getEnabledProviders() {
        return config.getEnabledProviders().keySet();
    }

    /**
     * 获取故障转移处理器列表
     * 排除指定的失败处理器，返回其他健康的处理器
     *
     * @param failedProvider 失败的服务商名称
     * @return 故障转移处理器列表
     */
    public List<VideoTranslateProcessor> getFallbackProcessors(String failedProvider) {
        if (!config.isFallbackEnabled()) {
            return new ArrayList<>();
        }

        return getHealthyProcessors().stream()
                .filter(processor -> !processor.getProviderName().equalsIgnoreCase(failedProvider))
                .collect(Collectors.toList());
    }

    /**
     * 输出配置详细信息到日志
     */
    private void logConfigDetails() {
        if (log.isInfoEnabled()) {
            log.info("=== 视频翻译配置详细信息 ===");
            log.info("默认服务商: {}", config.getDefaultProvider());
            log.info("故障转移启用: {}", config.isFallbackEnabled());
            log.info("全局超时时间: {}分钟", config.getGlobalTimeoutMinutes());
            log.info("轮询间隔: {}毫秒", config.getPollIntervalMs());
            log.info("最大重试次数: {}", config.getMaxRetries());

            log.info("--- 服务商配置 ---");
            config.getProviders().forEach((provider, providerConfig) -> {
                log.info("服务商: {} | 启用: {} | 优先级: {} | 超时: {}分钟 | 描述: {}",
                        provider,
                        providerConfig.isEnabled() ? "是" : "否",
                        providerConfig.getPriority(),
                        providerConfig.getTimeoutMinutes(),
                        providerConfig.getDescription());
            });
            log.info("=== 配置信息输出完成 ===");
        }
    }
}
