package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.business.db.mapper.KnowledgeSpaceMapper;
import com.business.db.mapper.UserDDRecordMapper;
import com.business.db.model.po.KnowledgeSpacePO;
import com.business.db.model.po.UserDDRecordPO;
import com.nacos.enums.UserDDrecordEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.E;
import com.nacos.result.Result;
import com.nacos.service.CheckService;
import com.nacos.utils.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service("digitalCheckService")
@Slf4j
public class CheckServiceImpl implements CheckService {

    @Resource
    private UserDDRecordMapper userDDRecordMapper;

    @Resource
    private KnowledgeSpaceMapper knowledgeSpaceMapper;

    @Override
    public Result<Boolean> getResidueDDDeduct(Long userId, Double deduct) {
        log.info("扣点子参数 userId= {}, deduct= {}", userId, deduct);
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper =  Wrappers.lambdaQuery();
        queryWrapper.eq(UserDDRecordPO::getUserId, userId);
        queryWrapper.ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai());
        queryWrapper.and(i -> i.last("total > total_usage"));
        queryWrapper.orderByAsc(UserDDRecordPO::getExpirationTime);
        List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(queryWrapper);
        log.info("用户点子余额= {}",userDDRecordPOS);
        if (userDDRecordPOS== null  || userDDRecordPOS.isEmpty()){
            // return Result.ERROR("点子余额不足");
            throw new DzBalanceE("剩余点子数不足");
        }
        //装载是否存在回退的余额记录
        UserDDRecordPO userDDRecordPOReturn = null;
        List<UserDDRecordPO> userDDRecordPOActivityList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOTaskList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct){
            // return Result.ERROR("点子余额不足");
            throw new DzBalanceE("点子余额不足");
        }
        //点子余额充足，扣除
        //1优先扣除回退点子数量
        if (userDDRecordPOReturn!= null){
            Result<Double> result = getUserDDRecordPODeduct(userDDRecordPOReturn, deduct);
            if (result.getData() == null){
                throw new E("更新点点余额失败");
                // return Result.ERROR("更新点点余额失败");
            }else if (result.getData() > 0){
                deduct = result.getData();
            }else {
                return Result.SUCCESS(true);
            }
        }

        //活动余额不为空
        if (!userDDRecordPOActivityList.isEmpty() && deduct > 0){
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOActivityList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                log.info("====result= {}", result);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //任务余额不为空
        if (!userDDRecordPOTaskList.isEmpty() && deduct > 0){
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOTaskList){
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //支付余额不为空
        if (!userDDRecordPOPayList.isEmpty() && deduct > 0){
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOPayList){
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }
        throw new DzBalanceE("点点余额不足");
        //return Result.ERROR("点点余额不足");
    }

    @Override
    public Result<Boolean> kouSpace(Long userId, Double deduct) {
        log.info("扣空间参数 userId= {}, deduct= {}", userId, deduct);
        LambdaQueryWrapper<KnowledgeSpacePO> queryWrapper =  Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeSpacePO::getUserId, userId);
        queryWrapper.ge(KnowledgeSpacePO::getExpirationTime, DateUtil.getDateNowShanghai());
        queryWrapper.and(i -> i.last("total > total_usage"));
        queryWrapper.orderByAsc(KnowledgeSpacePO::getExpirationTime);
        List<KnowledgeSpacePO> userDDRecordPOS = knowledgeSpaceMapper.selectList(queryWrapper);
        log.info("用户空间余额= {}",userDDRecordPOS);
        if (userDDRecordPOS== null  || userDDRecordPOS.isEmpty()){
            return Result.SUCCESS(false);
//            throw new DzBalanceE("剩余点子数不足");
        }
        //装载是否存在回退的余额记录
        KnowledgeSpacePO userDDRecordPOReturn = null;
        List<KnowledgeSpacePO> userDDRecordPOActivityList = new ArrayList<>();
        List<KnowledgeSpacePO> userDDRecordPOTaskList = new ArrayList<>();
        List<KnowledgeSpacePO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (KnowledgeSpacePO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct){
            // return Result.ERROR("点子余额不足");
            return Result.SUCCESS(false);
        }
        //点子余额充足，扣除
        //1优先扣除回退点子数量
        if (userDDRecordPOReturn!= null){
            Result<Double> result = getUserKnowledgeRecordPODeduct(userDDRecordPOReturn, deduct);
            if (result.getData() == null){
                throw new E("更新点点余额失败");
                // return Result.ERROR("更新点点余额失败");
            }else if (result.getData() > 0){
                deduct = result.getData();
            }else {
                return Result.SUCCESS(true);
            }
        }

        //活动余额不为空
        if (!userDDRecordPOActivityList.isEmpty() && deduct > 0){
            for (KnowledgeSpacePO userDDRecordPO : userDDRecordPOActivityList) {
                Result<Double> result = getUserKnowledgeRecordPODeduct(userDDRecordPO, deduct);
                log.info("====result= {}", result);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //任务余额不为空
        if (!userDDRecordPOTaskList.isEmpty() && deduct > 0){
            for (KnowledgeSpacePO userDDRecordPO : userDDRecordPOTaskList){
                Result<Double> result = getUserKnowledgeRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //支付余额不为空
        if (!userDDRecordPOPayList.isEmpty() && deduct > 0){
            for (KnowledgeSpacePO userDDRecordPO : userDDRecordPOPayList){
                Result<Double> result = getUserKnowledgeRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null){
                    throw new E("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                }else if (result.getData() > 0){
                    deduct = result.getData();
                }else {
                    return Result.SUCCESS(true);
                }
            }
        }
        throw new DzBalanceE("点点余额不足");
    }

    public Result<Double> getUserDDRecordPODeduct(UserDDRecordPO userDDRecordPO, Double deduct) {
        double returnDeduct = new BigDecimal(userDDRecordPO.getTotalUsage().toString()).add(new BigDecimal(deduct.toString())).doubleValue();
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userDDRecordPO.getUserId());
        updateWrapper.eq(UserDDRecordPO::getId,userDDRecordPO.getId());

        //足额被扣除：直接返回
        if (returnDeduct <= userDDRecordPO.getTotal()){
            updateWrapper.set(UserDDRecordPO::getTotalUsage,returnDeduct);
            if (userDDRecordMapper.update(null,updateWrapper) > 0){
                return Result.SUCCESS((double) 0);
            }
            return Result.ERROR("更新点点余额失败");
        }
        //非足额扣除
        updateWrapper.set(UserDDRecordPO::getTotalUsage,userDDRecordPO.getTotal());
        if (userDDRecordMapper.update(null,updateWrapper) < 1){
            return Result.ERROR("更新点点余额失败");
        }
        return Result.SUCCESS(new BigDecimal(String.valueOf(returnDeduct)).subtract(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue());
    }

    public Result<Double> getUserKnowledgeRecordPODeduct(KnowledgeSpacePO userDDRecordPO, Double deduct) {
        double returnDeduct = new BigDecimal(userDDRecordPO.getTotalUsage().toString()).add(new BigDecimal(deduct.toString())).doubleValue();
        LambdaUpdateWrapper<KnowledgeSpacePO> updateWrapper = Wrappers.lambdaUpdate(KnowledgeSpacePO.class);
        updateWrapper.eq(KnowledgeSpacePO::getUserId, userDDRecordPO.getUserId());
        updateWrapper.eq(KnowledgeSpacePO::getId,userDDRecordPO.getId());

        //足额被扣除：直接返回
        if (returnDeduct <= userDDRecordPO.getTotal()){
            updateWrapper.set(KnowledgeSpacePO::getTotalUsage,returnDeduct);
            if (knowledgeSpaceMapper.update(null,updateWrapper) > 0){
                return Result.SUCCESS((double) 0);
            }
            return Result.ERROR("更新点点余额失败");
        }
        //非足额扣除
        updateWrapper.set(KnowledgeSpacePO::getTotalUsage,userDDRecordPO.getTotal());
        if (knowledgeSpaceMapper.update(null,updateWrapper) < 1){
            return Result.ERROR("更新点点余额失败");
        }
        return Result.SUCCESS(new BigDecimal(String.valueOf(returnDeduct)).subtract(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue());
    }

    @Override
    public boolean setReturnDD(Long userId, Double ddQuantity) {
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper =  new LambdaQueryWrapper<UserDDRecordPO>()
                .eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
                ;
        UserDDRecordPO userDDRecordPO = userDDRecordMapper.selectOne(queryWrapper);
        if (userDDRecordPO == null){
            return userDDRecordMapper.insert(new UserDDRecordPO(
                    userId,
                    Long.valueOf(UserDDrecordEnum.SOURCE_ID_INVALID.getIntValue()),
                    UserDDrecordEnum.TYPE_RETURN.getIntValue(),
                    UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue(),
                    ddQuantity,
                    DateUtil.getDateAddMonth(DateUtil.getDateNowShanghai(),600)
            )) > 0;
        }
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getId,userDDRecordPO.getId())
               .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
               .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
               .set(UserDDRecordPO::getTotal,new BigDecimal(userDDRecordPO.getTotal().toString()).add(new BigDecimal(ddQuantity.toString())).doubleValue())
               .set(UserDDRecordPO::getTotalUsage,userDDRecordPO.getTotalUsage());
        return userDDRecordMapper.update(null,updateWrapper) > 0;
    }

    @Override
    public boolean setReturnSpace(Long userId, Double ddQuantity) {
        LambdaQueryWrapper<KnowledgeSpacePO> queryWrapper =  new LambdaQueryWrapper<KnowledgeSpacePO>()
                .eq(KnowledgeSpacePO::getUserId, userId)
                .eq(KnowledgeSpacePO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(KnowledgeSpacePO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
                ;
        KnowledgeSpacePO userDDRecordPO = knowledgeSpaceMapper.selectOne(queryWrapper);
        if (userDDRecordPO == null){
            return knowledgeSpaceMapper.insert(new KnowledgeSpacePO(
                    userId,
                    Long.valueOf(UserDDrecordEnum.SOURCE_ID_INVALID.getIntValue()),
                    UserDDrecordEnum.TYPE_RETURN.getIntValue(),
                    UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue(),
                    ddQuantity,
                    DateUtil.getDateAddMonth(DateUtil.getDateNowShanghai(),600)
            )) > 0;
        }
        LambdaUpdateWrapper<KnowledgeSpacePO> updateWrapper = Wrappers.lambdaUpdate(KnowledgeSpacePO.class);
        updateWrapper.eq(KnowledgeSpacePO::getUserId, userId)
                .eq(KnowledgeSpacePO::getId,userDDRecordPO.getId())
                .eq(KnowledgeSpacePO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(KnowledgeSpacePO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
                .set(KnowledgeSpacePO::getTotal,new BigDecimal(userDDRecordPO.getTotal().toString()).add(new BigDecimal(ddQuantity.toString())).doubleValue())
                .set(KnowledgeSpacePO::getTotalUsage,userDDRecordPO.getTotalUsage());
        return knowledgeSpaceMapper.update(null,updateWrapper) > 0;
    }

    @Override
    public Result<Boolean> checkYue(Long userId, double deduct) {
        log.info("扣点子参数 userId= {}, deduct= {}", userId, deduct);
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper =  Wrappers.lambdaQuery();
        queryWrapper.eq(UserDDRecordPO::getUserId, userId);
        queryWrapper.ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai());
        queryWrapper.and(i -> i.last("total > total_usage"));
        queryWrapper.orderByAsc(UserDDRecordPO::getExpirationTime);
        List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(queryWrapper);
        log.info("用户点子余额= {}",userDDRecordPOS);
        if (userDDRecordPOS== null  || userDDRecordPOS.isEmpty()){
             return Result.SUCCESS(false);
//            throw new DzBalanceE("剩余点子数不足");
        }
        //装载是否存在回退的余额记录
        UserDDRecordPO userDDRecordPOReturn = null;
        List<UserDDRecordPO> userDDRecordPOActivityList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOTaskList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct){
            // return Result.ERROR("点子余额不足");
            return Result.SUCCESS(false);
        }
        return Result.SUCCESS(true);
    }


    @Override
    public Result<Boolean> checkKownledgeSpace(Long userId, Double deduct) {
        log.info("扣空间参数 userId= {}, deduct= {}", userId, deduct);
        LambdaQueryWrapper<KnowledgeSpacePO> queryWrapper =  Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeSpacePO::getUserId, userId);
        queryWrapper.ge(KnowledgeSpacePO::getExpirationTime, DateUtil.getDateNowShanghai());
        queryWrapper.and(i -> i.last("total > total_usage"));
        queryWrapper.orderByAsc(KnowledgeSpacePO::getExpirationTime);
        List<KnowledgeSpacePO> userDDRecordPOS = knowledgeSpaceMapper.selectList(queryWrapper);
        log.info("用户空间余额= {}",userDDRecordPOS);
        if (userDDRecordPOS== null  || userDDRecordPOS.isEmpty()){
            return Result.SUCCESS(false);
//            throw new DzBalanceE("剩余点子数不足");
        }
        //装载是否存在回退的余额记录
        KnowledgeSpacePO userDDRecordPOReturn = null;
        List<KnowledgeSpacePO> userDDRecordPOActivityList = new ArrayList<>();
        List<KnowledgeSpacePO> userDDRecordPOTaskList = new ArrayList<>();
        List<KnowledgeSpacePO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (KnowledgeSpacePO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()){
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct){
            // return Result.ERROR("点子余额不足");
            return Result.SUCCESS(false);
        }
        return Result.SUCCESS(true);
    }

}
