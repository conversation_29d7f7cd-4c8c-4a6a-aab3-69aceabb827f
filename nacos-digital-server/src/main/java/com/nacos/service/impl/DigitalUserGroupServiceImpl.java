package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.entity.vo.DigitalAvatarGroupListVO;
import com.nacos.entity.vo.PageResultVO;
import com.nacos.entity.po.DigitalUserGroupPO;
import com.nacos.entity.po.DigitalSystemGroupPO;
import com.nacos.entity.po.DigitalAvatarCategoryPO;
import com.nacos.entity.po.DigitalGroupCategoryRelationPO;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.po.DigitalVoiceUserClonePO;
import com.nacos.mapper.DigitalUserGroupMapper;
import com.nacos.mapper.DigitalSystemGroupMapper;
import com.nacos.mapper.DigitalAvatarCategoryMapper;
import com.nacos.mapper.DigitalGroupCategoryRelationMapper;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.mapper.DigitalSystemAvatarMapper;
import com.nacos.mapper.DigitalVoiceUserCloneMapper;
import com.nacos.service.DigitalUserGroupService;
import com.nacos.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 数字人用户组服务实现类
 * 处理数字人用户组的增删改查等基础操作
 */
@Slf4j
@Service
public class DigitalUserGroupServiceImpl extends ServiceImpl<DigitalUserGroupMapper, DigitalUserGroupPO> implements DigitalUserGroupService {

    @Autowired
    private DigitalSystemGroupMapper digitalSystemGroupMapper;

    @Autowired
    private DigitalAvatarCategoryMapper digitalAvatarCategoryMapper;

    @Autowired
    private DigitalGroupCategoryRelationMapper digitalGroupCategoryRelationMapper;

    @Autowired
    private DigitalUserAvatarMapper digitalUserAvatarMapper;

    @Autowired
    private DigitalSystemAvatarMapper digitalSystemAvatarMapper;

    @Autowired
    private DigitalVoiceUserCloneMapper digitalVoiceUserCloneMapper;

    /**
     * 创建数字人用户组
     * 创建新的数字人用户组记录，包含参数验证
     * @param groupDTO 数字人用户组信息
     * @return 创建的数字人用户组ID，失败返回错误信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<DigitalUserGroupVO> createGroup(DigitalUserGroupDTO groupDTO) {
        String methodName = "createGroup";
        log.info("[{}] 开始创建数字人用户组，参数：{}", methodName, groupDTO);
        
        try {
            if (!StringUtils.hasText(groupDTO.getGroupName())) {
                log.error("[{}] 组名称为空", methodName);
                return Result.ERROR("组名称不能为空");
            }
            if (!StringUtils.hasText(groupDTO.getUserId())) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            DigitalUserGroupPO po = new DigitalUserGroupPO();
            po.setGroupName(groupDTO.getGroupName());
            po.setUserId(groupDTO.getUserId());
            po.setDescription(groupDTO.getDescription());
            po.setCoverUrl(groupDTO.getCoverUrl());
            
            // 不需要手动设置这些值，会由MyMetaObjectHandler自动填充
            // groupId会自动生成
            // status会自动设置默认值1
            // sort会自动设置默认值0
            // createdTime会自动填充
            // updateTime会自动填充
            // isDeleted会自动设置默认值0

            log.info("[{}] 保存数字人用户组信息：{}", methodName, po);
            boolean success = this.save(po);
            
            if (!success) {
                log.error("[{}] 保存数字人用户组失败", methodName);
                return Result.ERROR("创建组失败");
            }
            // 返回创建的数字人VO对象
            DigitalUserGroupVO vo = convertToVO(po);
            
            log.info("[{}] 创建数字人用户组成功，groupId：{}", methodName, po.getGroupId());
            return Result.SUCCESS(vo);
        } catch (Exception e) {
            log.error("[{}] 创建组失败", methodName, e);
            return Result.ERROR("创建组失败：" + e.getMessage());
        }
    }

    /**
     * 更新数字人用户组
     * 更新现有数字人用户组记录，包含参数验证
     * @param groupDTO 数字人用户组信息
     * @return 操作结果，失败返回错误信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateGroup(DigitalUserGroupDTO groupDTO) {
        String methodName = "updateGroup";
        try {
            if (!StringUtils.hasText(groupDTO.getGroupId())) {
                log.error("[{}] 组ID不能为空", methodName);
                return Result.ERROR("组ID不能为空");
            }
            if (!StringUtils.hasText(groupDTO.getUserId())) {
                log.error("[{}] 用户ID不能为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            // 根据用户ID和组名称查找现有记录
            LambdaQueryWrapper<DigitalUserGroupPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserGroupPO::getUserId, groupDTO.getUserId())
                   .eq(DigitalUserGroupPO::getGroupId, groupDTO.getGroupId())
                   .eq(DigitalUserGroupPO::getIsDeleted, 0);
            
            DigitalUserGroupPO existingGroup = this.getOne(wrapper);
            if (existingGroup == null) {
                return Result.ERROR("数字人用户组不存在");
            }

            DigitalUserGroupPO po = convertToPO(groupDTO);
            po.setId(existingGroup.getId());
            
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新数字人用户组失败");
        } catch (Exception e) {
            log.error("更新数字人用户组失败", e);
            return Result.ERROR("更新数字人用户组失败：" + e.getMessage());
        }
    }

    /**
     * 删除数字人用户组
     * 逻辑删除指定ID的数字人用户组记录
     * @param id 数字人用户组数据ID
     * @return 操作结果，失败返回错误信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteGroup(Long id) {
        try {
            if (id == null) {
                return Result.ERROR("组ID不能为空");
            }

            DigitalUserGroupPO group = this.getById(id);
            if (group == null || group.getIsDeleted() == 1) {
                return Result.ERROR("数字人用户组不存在");
            }

            // 逻辑删除，会自动设置isDeleted=1
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.removeById(id);
            return success ? Result.SUCCESS(true) : Result.ERROR("删除数字人用户组失败");
        } catch (Exception e) {
            log.error("删除数字人用户组失败", e);
            return Result.ERROR("删除数字人用户组失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的所有用户数字人组
     * 获取指定用户ID的所有未删除的用户数字人组
     * @param userId 用户ID
     * @return 数字人用户组列表，失败返回错误信息
     */
    @Override
    public Result<List<DigitalUserGroupVO>> listUserGroups(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            LambdaQueryWrapper<DigitalUserGroupPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserGroupPO::getUserId, userId)
                   .eq(DigitalUserGroupPO::getIsDeleted, 0)
                   .orderByAsc(DigitalUserGroupPO::getSort);
            
            List<DigitalUserGroupPO> list = this.list(wrapper);
            List<DigitalUserGroupVO> voList = list.stream()
                                                   .map(this::convertToVO)
                                                   .collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取用户数字人用户组列表失败，用户ID：{}", userId, e);
            return Result.ERROR("获取用户数字人用户组列表失败");
        }
    }

    /**
     * 获取数字人用户组详情
     * 获取指定ID的数字人用户组详细信息
     * @param groupId 数字人用户组ID
     * @return 数字人用户组详情，失败返回错误信息
     */
    @Override
    public Result<DigitalUserGroupVO> getGroupDetail(Long groupId) {
        try {
            if (groupId == null) {
                return Result.ERROR("组ID不能为空");
            }

            LambdaQueryWrapper<DigitalUserGroupPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserGroupPO::getGroupId, groupId)
                   .eq(DigitalUserGroupPO::getIsDeleted, 0);
            DigitalUserGroupPO group = this.getOne(wrapper);
            if (group == null || group.getIsDeleted() == 1) {
                return Result.ERROR("数字人用户组不存在");
            }

            return Result.SUCCESS(convertToVO(group));
        } catch (Exception e) {
            log.error("获取数字人用户组详情失败，组ID：{}", groupId, e);
            return Result.ERROR("获取数字人用户组详情失败");
        }
    }

    /**
     * 将DTO对象转换为PO对象
     * @param dto DTO对象
     * @return PO对象
     */
    private DigitalUserGroupPO convertToPO(DigitalUserGroupDTO dto) {
        if (dto == null) {
            return null;
        }
        DigitalUserGroupPO po = new DigitalUserGroupPO();
        po.setGroupId(dto.getGroupId());
        po.setGroupName(dto.getGroupName());
        po.setDescription(dto.getDescription());
        po.setCoverUrl(dto.getCoverUrl());
        po.setSort(dto.getSort());
        po.setStatus(dto.getStatus());
        po.setIsDeleted(dto.getIsDeleted());
        return po;
    }

    /**
     * 将PO对象转换为DTO对象
     * @param po PO对象
     * @return DTO对象
     */
    private DigitalUserGroupDTO convertToDTO(DigitalUserGroupPO po) {
        if (po == null) {
            return null;
        }
        DigitalUserGroupDTO dto = new DigitalUserGroupDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 将PO对象转换为VO对象
     * @param po PO对象
     * @return VO对象
     */
    private DigitalUserGroupVO convertToVO(DigitalUserGroupPO po) {
        if (po == null) {
            return null;
        }
        DigitalUserGroupVO vo = new DigitalUserGroupVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 分页查询所有数字人组列表（通用接口）
     * 支持按组类型和分类筛选的通用分页查询接口
     *
     * @param userId 用户ID，用于权限验证和标识用户组所有权
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @param groupType 组类型筛选：USER-用户组，SYSTEM-系统组，ALL-全部
     * @param categoryCode 分类编码，可选，用于筛选特定分类下的组
     * @return 分页的数字人组列表，包含统计信息，失败返回错误信息
     */
    @Override
    public Result<PageResultVO<DigitalAvatarGroupListVO>> queryAllGroupsPage(String userId, Integer pageNum, Integer pageSize, String groupType, String categoryCode) {
        String methodName = "queryAllGroupsPage";
        log.info("[{}] 开始分页查询数字人组列表 - userId: {}, pageNum: {}, pageSize: {}, groupType: {}, categoryCode: {}",
                methodName, userId, pageNum, pageSize, groupType, categoryCode);

        try {
            // 参数验证
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            // 构建查询结果列表
            List<DigitalAvatarGroupListVO> allGroups = new ArrayList<>();

            // 根据组类型查询
            if ("USER".equals(groupType) || "ALL".equals(groupType)) {
                // 查询用户组
                List<DigitalAvatarGroupListVO> userGroups = queryUserGroups(userId, categoryCode);
                allGroups.addAll(userGroups);
            }

            if ("SYSTEM".equals(groupType) || "ALL".equals(groupType)) {
                // 查询系统组
                List<DigitalAvatarGroupListVO> systemGroups = querySystemGroups(userId, categoryCode);
                allGroups.addAll(systemGroups);
            }

            // 按排序字段排序
            allGroups.sort((a, b) -> {
                if (a.getSort() == null && b.getSort() == null) return 0;
                if (a.getSort() == null) return 1;
                if (b.getSort() == null) return -1;
                return a.getSort().compareTo(b.getSort());
            });

            // 手动分页
            int total = allGroups.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<DigitalAvatarGroupListVO> pageData = new ArrayList<>();
            if (startIndex < total) {
                pageData = allGroups.subList(startIndex, endIndex);
            }

            // 构建分页结果
            PageResultVO<DigitalAvatarGroupListVO> pageResult = PageResultVO.<DigitalAvatarGroupListVO>builder()
                    .total((long) total)
                    .pages((long) Math.ceil((double) total / pageSize))
                    .current((long) pageNum)
                    .size((long) pageSize)
                    .records(pageData)
                    .build();

            log.info("[{}] 分页查询数字人组列表成功 - userId: {}, 总数: {}, 当前页数据量: {}",
                    methodName, userId, total, pageData.size());
            return Result.SUCCESS(pageResult);

        } catch (Exception e) {
            log.error("[{}] 分页查询数字人组列表失败 - userId: {}", methodName, userId, e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询用户组列表
     *
     * @param userId 用户ID
     * @param categoryCode 分类编码，可选
     * @return 用户组列表
     */
    private List<DigitalAvatarGroupListVO> queryUserGroups(String userId, String categoryCode) {
        List<DigitalAvatarGroupListVO> result = new ArrayList<>();

        try {
            // 构建查询条件
            LambdaQueryWrapper<DigitalUserGroupPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserGroupPO::getUserId, userId)
                   .eq(DigitalUserGroupPO::getStatus, 1)
                   .eq(DigitalUserGroupPO::getIsDeleted, 0);

            // 如果指定了分类，需要通过关联表筛选
            if (StringUtils.hasText(categoryCode)) {
                // 先获取分类ID
                Long categoryId = getCategoryIdByCode(categoryCode);
                if (categoryId != null) {
                    // 获取该分类下的用户组ID列表
                    List<String> groupIds = getGroupIdsByCategory(categoryId, 1); // 1-用户组
                    if (!groupIds.isEmpty()) {
                        wrapper.in(DigitalUserGroupPO::getGroupId, groupIds);
                    } else {
                        // 如果该分类下没有用户组，返回空列表
                        return result;
                    }
                } else {
                    // 分类不存在，返回空列表
                    return result;
                }
            }

            wrapper.orderByAsc(DigitalUserGroupPO::getSort);

            List<DigitalUserGroupPO> userGroups = this.list(wrapper);

            // 转换为VO并添加统计信息
            for (DigitalUserGroupPO group : userGroups) {
                DigitalAvatarGroupListVO vo = convertUserGroupToListVO(group, userId);
                result.add(vo);
            }

        } catch (Exception e) {
            log.error("查询用户组列表失败 - userId: {}, categoryCode: {}", userId, categoryCode, e);
        }

        return result;
    }

    /**
     * 查询系统组列表
     *
     * @param userId 用户ID，用于标识所有权
     * @param categoryCode 分类编码，可选
     * @return 系统组列表
     */
    private List<DigitalAvatarGroupListVO> querySystemGroups(String userId, String categoryCode) {
        List<DigitalAvatarGroupListVO> result = new ArrayList<>();

        try {
            // 构建查询条件
            LambdaQueryWrapper<DigitalSystemGroupPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalSystemGroupPO::getStatus, 1)
                   .eq(DigitalSystemGroupPO::getIsDeleted, 0);

            // 如果指定了分类，需要通过关联表筛选
            if (StringUtils.hasText(categoryCode)) {
                // 先获取分类ID
                Long categoryId = getCategoryIdByCode(categoryCode);
                if (categoryId != null) {
                    // 获取该分类下的系统组ID列表
                    List<String> groupIds = getGroupIdsByCategory(categoryId, 2); // 2-系统组
                    if (!groupIds.isEmpty()) {
                        wrapper.in(DigitalSystemGroupPO::getGroupId, groupIds);
                    } else {
                        // 如果该分类下没有系统组，返回空列表
                        return result;
                    }
                } else {
                    // 分类不存在，返回空列表
                    return result;
                }
            }

            wrapper.orderByAsc(DigitalSystemGroupPO::getSort);

            List<DigitalSystemGroupPO> systemGroups = digitalSystemGroupMapper.selectList(wrapper);

            // 转换为VO并添加统计信息
            for (DigitalSystemGroupPO group : systemGroups) {
                DigitalAvatarGroupListVO vo = convertSystemGroupToListVO(group, userId);
                result.add(vo);
            }

        } catch (Exception e) {
            log.error("查询系统组列表失败 - userId: {}, categoryCode: {}", userId, categoryCode, e);
        }

        return result;
    }

    /**
     * 根据分类编码获取分类ID
     *
     * @param categoryCode 分类编码
     * @return 分类ID，如果不存在则返回null
     */
    private Long getCategoryIdByCode(String categoryCode) {
        if (!StringUtils.hasText(categoryCode)) {
            return null;
        }

        DigitalAvatarCategoryPO category = digitalAvatarCategoryMapper.selectOne(
            new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
                .eq(DigitalAvatarCategoryPO::getCategoryCode, categoryCode)
                .eq(DigitalAvatarCategoryPO::getStatus, 1)
                .eq(DigitalAvatarCategoryPO::getIsDeleted, 0)
        );

        return category != null ? category.getId() : null;
    }

    /**
     * 根据分类ID获取关联的组ID列表
     *
     * @param categoryId 分类ID
     * @param groupType 组类型（1-用户组，2-系统组）
     * @return 组ID列表
     */
    private List<String> getGroupIdsByCategory(Long categoryId, Integer groupType) {
        List<DigitalGroupCategoryRelationPO> relations = digitalGroupCategoryRelationMapper.selectList(
            new LambdaQueryWrapper<DigitalGroupCategoryRelationPO>()
                .eq(DigitalGroupCategoryRelationPO::getCategoryId, categoryId)
                .eq(DigitalGroupCategoryRelationPO::getGroupType, groupType)
        );

        return relations.stream()
            .map(DigitalGroupCategoryRelationPO::getGroupId)
            .collect(Collectors.toList());
    }

    /**
     * 将用户组PO转换为列表VO
     *
     * @param group 用户组PO
     * @param userId 用户ID
     * @return 列表VO
     */
    private DigitalAvatarGroupListVO convertUserGroupToListVO(DigitalUserGroupPO group, String userId) {
        DigitalAvatarGroupListVO vo = new DigitalAvatarGroupListVO();
        vo.setGroupId(group.getGroupId());
        vo.setGroupName(group.getGroupName());
        vo.setDescription(group.getDescription());
        vo.setCoverUrl(group.getCoverUrl());
        vo.setSort(group.getSort());
        vo.setStatus(group.getStatus());
        vo.setGroupType(1); // 用户组
        vo.setIsOwner(userId.equals(group.getUserId())); // 是否为当前用户拥有

        // 统计该组下的分身数量
        Long instanceCount = digitalUserAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalUserAvatarPO>()
                .eq(DigitalUserAvatarPO::getGroupId, group.getGroupId())
                .eq(DigitalUserAvatarPO::getStatus, 1)
                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
        );
        vo.setInstanceCount(instanceCount.intValue());

        // 统计有音色的分身数量
        Long voiceInstanceCount = digitalVoiceUserCloneMapper.selectCount(
            new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                .exists("SELECT 1 FROM digital_user_avatar WHERE avatar_id = digital_voice_user_clone.avatar_id " +
                       "AND group_id = '" + group.getGroupId() + "' AND status = 1 AND is_deleted = 0")
                .eq(DigitalVoiceUserClonePO::getStatus, 1)
                .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
        );
        vo.setVoiceInstanceCount(voiceInstanceCount.intValue());

        return vo;
    }

    /**
     * 将系统组PO转换为列表VO
     *
     * @param group 系统组PO
     * @param userId 用户ID
     * @return 列表VO
     */
    private DigitalAvatarGroupListVO convertSystemGroupToListVO(DigitalSystemGroupPO group, String userId) {
        DigitalAvatarGroupListVO vo = new DigitalAvatarGroupListVO();
        vo.setGroupId(group.getGroupId());
        vo.setGroupName(group.getGroupName());
        vo.setDescription(group.getDescription());
        vo.setCoverUrl(group.getCoverUrl());
        vo.setSort(group.getSort());
        vo.setStatus(group.getStatus());
        vo.setGroupType(2); // 系统组
        vo.setIsOwner(false); // 系统组不属于任何用户

        // 统计该组下的分身数量
        Long instanceCount = digitalSystemAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                .eq(DigitalSystemAvatarPO::getGroupId, group.getGroupId())
                .eq(DigitalSystemAvatarPO::getStatus, 1)
                .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
        );
        vo.setInstanceCount(instanceCount.intValue());

        // 系统组暂时不统计音色数量，设为0
        vo.setVoiceInstanceCount(0);

        return vo;
    }
}