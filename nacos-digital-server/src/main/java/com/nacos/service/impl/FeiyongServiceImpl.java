package com.nacos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.business.db.model.po.FlowRecordPO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.enums.GlobalRedisKeyEnum;
import com.nacos.redis.RedisUtil;
import com.nacos.service.CheckBalanService;
import com.nacos.service.CheckService;
import com.nacos.service.FeiyongService;
import com.nacos.service.IFlowRecordService;
import com.nacos.utils.BFeiShuUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class FeiyongServiceImpl implements FeiyongService {

    @Resource
    private CheckBalanService checkBalanService;
    @Resource(name = "digitalCheckService")
    private CheckService checkService;
    @Resource(name = "digitalFlowRecordService")
    private IFlowRecordService flowRecordService;

    @Override
    public boolean checkYue(Long userId, String feiyongType) {

        //查询扣除点子数量
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConstant.SYS_RULE_CONFIG);
        String redisValue = RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), feiyongType));
        if (redisValue == null) {
            log.error("获取扣点子配置失败，Redis中不存在key: {}", GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), feiyongType));
            return false;
        }
        double koufeiValue = Double.parseDouble(redisValue);
        //扣除用户点子数量
        log.info("获取扣点子配置= {}", koufeiValue);
        //
        return checkBalanService.checkYue(Long.valueOf(userId), koufeiValue);
    }

    @Override
    public boolean checkKownledgeSpace(long userId, String type, Double fileSize) {
        return checkBalanService.checkKownledgeSpace(Long.valueOf(userId), fileSize);
    }

    @Override
    public boolean koufei(Long userId, Long logId,String remark,String feiyongType) {
        try{
            double koufeiValue = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), feiyongType)));
            //扣除用户点子数量
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey()).remark(remark).build();
            log.info("获取扣点子配置= {}", koufeiValue);
            //实际扣费
            checkBalanService.checkUser(Long.valueOf(userId), koufeiValue, flowRecordPO);
            return true;
        } catch (Exception e){
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "知识空间占用失败", "ERRORInfo=" + e.getMessage());
        }
        //查询扣除点子数量
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConstant.SYS_RULE_CONFIG);
        return false ;
    }

    @Override
    public boolean kouKownledgeSpace(String userId, Long id, String type, Double fileSize) {
        try{
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_SPACE.getDtoKey()).remark("知识空间占用").build();
            return checkBalanService.kouKownledgeSpace(Long.valueOf(userId), fileSize, flowRecordPO);
        } catch (Exception e){
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "知识空间占用失败", "ERRORInfo=" + e.getMessage());
        }
        return false;
    }

    /**
     * 按乘数退费multiplier
     *
     * @param userId
     * @param logId
     * @param remark
     * @param feiyongType
     * @param multiplier
     */
    @Override
    public void tuifei(Long userId, Long logId, String remark, String feiyongType, Integer multiplier) {
        try {
            double chatSceneWriterDeductQua = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(),feiyongType)));
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey()).remark(remark).build();
            log.info("退点每分钟{} ,退点分钟{}，退点总数{}", chatSceneWriterDeductQua, multiplier, chatSceneWriterDeductQua * multiplier);
            updateRemainingTimes(userId,  chatSceneWriterDeductQua * multiplier, flowRecordPO);
        } catch (Exception e){
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "退费mul失败", "ERRORInfo=" + e.getMessage());
        }

    }

    /**
     * 按乘数扣费multiplier
     *
     * @param userId
     * @param logId
     * @param remark
     * @param feiyongType
     * @param multiplier
     * @return
     */
    @Override
    public boolean koufei(Long userId, Long logId, String remark, String feiyongType, Integer multiplier) {
        try{
            double koufeiValue = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), feiyongType)));
            //扣除用户点子数量
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey()).remark(remark).build();
            log.info("获取扣点子配置= {}", koufeiValue);
            //实际扣费
            log.info("扣点每分钟{} ,扣点分钟{}，扣点总数{}", koufeiValue, multiplier, koufeiValue * multiplier);
            checkBalanService.checkUser(Long.valueOf(userId), koufeiValue * multiplier, flowRecordPO);
            return true;
        } catch (Exception e) {
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "扣费mul失败", "ERRORInfo=" + e.getMessage());
        }
        return false;
    }

    @Override
    public boolean checkYueMultiplier(Long userId, long id, String mark, String feiyongType, int multiplier) {
        double koufeiValue = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), feiyongType)));
        //扣除用户点子数量=koufeiValue* multiplier
        log.info("checkYueMultiplier= {}", koufeiValue* multiplier);
        //实际扣费
        return checkBalanService.checkYue(userId, koufeiValue* multiplier);
    }

    @Override
    public void tuifei(Long userId, Long logId, String remark,String feiyongType) {
        //删除记录
//        sceneRecordMapper.deleteById(logId);
        //将用户使用点子数返回
        try{
            double chatSceneWriterDeductQua = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(),feiyongType)));
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey()).remark(remark).build();
            updateRemainingTimes(userId,  chatSceneWriterDeductQua, flowRecordPO);
        } catch (Exception e){
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "退费失败", "ERRORInfo=" + e.getMessage());
        }

    }

    @Override
    public void tuiKownledgeSpace(Long userId, Long id, String remark, Double fileSize) {
        try{
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey()).remark(remark).build();
            updateTuiSpace(userId,  fileSize, flowRecordPO);
        } catch (Exception e){
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "退空间失败", "ERRORInfo=" + e.getMessage());
        }
    }

    private void updateTuiSpace(Long userId, Double ddQuantity, FlowRecordPO flowRecordPO) {
        if(ObjectUtil.isNull(ddQuantity) || 0 == ddQuantity.intValue()){
            return;
        }
        boolean result = checkService.setReturnSpace(userId, ddQuantity);
        if (result) {
            flowRecordPO.setUserId(userId);
            flowRecordPO.setNum(ddQuantity);
            flowRecordService.save(flowRecordPO);
        }
        log.info("退还空间数："+ddQuantity);
    }


    @Async
    public void updateRemainingTimes(Long userId, Double ddQuantity, FlowRecordPO flowRecordPO){
        if(ObjectUtil.isNull(ddQuantity) || 0 == ddQuantity.intValue()){
            return;
        }
        boolean result = checkService.setReturnDD(userId, ddQuantity);
        if (result) {
            flowRecordPO.setUserId(userId);
            flowRecordPO.setNum(ddQuantity);
            flowRecordService.save(flowRecordPO);
        }
        log.info("退还点子数："+ddQuantity);
    }


}
