package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.config.TencentCloudConfig;
import com.nacos.constant.CommonConst;
import com.nacos.entity.AvatarRecord;
import com.nacos.entity.bo.SubTaskUpdateBO;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.ChanJingAvatarRequestDTO;
import com.nacos.entity.dto.CreateVideoRequestDTO;
import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.enums.TaskStatusEnum;
import com.nacos.entity.enums.VideoTaskItemStatusEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.DigitalSystemAvatarMapper;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.mapper.DigitalVideoTaskItemMapper;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.model.ChanJing.model.CustomisedPersonResponse;
import com.nacos.model.ChanJing.model.GetVideoDetailResponse;
import com.nacos.model.ChanJing.model.GetVideoDetailResponseData;
import com.nacos.model.ChanJing.model.config.VideoAudioConfig;
import com.nacos.model.ChanJing.model.config.VideoPersonConfig;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadRequestBO;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.utils.BFeiShuUtil;
import com.nacos.utils.DigitalFileUtil;
import com.nacos.utils.MessageSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

import static com.nacos.constant.CommonConst.*;
import static com.nacos.entity.enums.DigitalNotificationEnum.AVATAR_CREATE_PROCESSING;
import static com.nacos.entity.enums.DigitalNotificationEnum.AVATAR_CREATE_QUEUING;

/**
 * 数字人视频服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChangjinMiniServiceImpl implements ChangjinMiniService {


    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVideoTaskService digitalVideoTaskService;
    private final DigitalVideoAsyncService digitalVideoAsyncService;
    private final TXDigitalApisUtil txDigitalApisUtil;
    private final DigitalVideoTaskItemMapper digitalVideoTaskItemMapper;
    private final DigitalNotificationService digitalNotificationService;
    @Autowired
    private TencentCloudConfig tencentCloudConfig;
    private final DigitalAvatarService digitalAvatarService;
    private final DigitalAudioService digitalAudioService;
    private final FeiyongService feiyongService;

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            // 查询任务队列中有几个排队任务
            long queueingCount = digitalVideoTaskMapper.selectCount(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            if (queueingCount == 0) {
//                log.info("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 当前任务队列中排队任务数量：{}", methodName, queueingCount);
            // 1. 首先检查是否有进行中的任务
            long inProgressCount = digitalVideoTaskMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            // 如果已经有进行中的任务，则不处理排队任务
            if (inProgressCount > 0) {
                log.info("[{}] 当前有{}个进行中的任务，暂不处理排队任务", methodName, inProgressCount);
                return;
            }

            // 2. 查询排队中的任务（按创建时间升序，最早创建的任务优先处理）
            List<DigitalVideoTaskPO> queueingTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByAsc(DigitalVideoTaskPO::getCreatedTime)
                            .last("LIMIT 1") // 只取一个任务处理
            );

            if (queueingTasks.isEmpty()) {
//                log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            // 3. 处理队列中的第一个任务
            DigitalVideoTaskPO task = queueingTasks.getFirst();
            log.info("[{}] 开始处理排队任务：taskId={}, userId={}", methodName, task.getTaskId(), task.getUserId());

            try {
                // 4. 查询子任务详情
                List<DigitalVideoTaskItemPO> taskItems = digitalVideoTaskItemMapper.selectList(
                        new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                                .eq(DigitalVideoTaskItemPO::getTaskId, task.getTaskId())
                                .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                                .orderByAsc(DigitalVideoTaskItemPO::getSequence)
                );

                if (taskItems.isEmpty()) {
                    String errorMsg = "该任务没有子任务";
                    log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId());
                    digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
                    return;
                }
                // 更新任务状态为进行中
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.IN_PROGRESS.getValue())
                    .build()
                );
                // 提交到异步服务处理
                digitalVideoAsyncService.processVideoTasks(
                        task.getTaskId(),
                        task.getUserId(),
                        taskItems
                );
                log.info("[{}] 任务已提交到异步服务处理：taskId={}", methodName, task.getTaskId());
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");
            } catch (Exception e) {
                String errorMsg = "处理任务异常：" + e.getMessage();
                log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId(), e);
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .errorMsg(errorMsg)
                    .build()
                );
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
            }
        } catch (Exception e) {
            log.error("[{}] 处理排队中的任务异常", methodName, e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        log.info("[{}] 开始处理超时任务", methodName);

        try {
            // 获取配置的超时时间（分钟）
            int timeoutMinutes = 30; // 默认30分钟

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询可能超时的任务（进行中状态且更新时间超过阈值）
            List<DigitalVideoTaskPO> potentialTimeoutTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .lt(DigitalVideoTaskPO::getUpdateTime, timeoutThreshold)
            );

            if (potentialTimeoutTasks.isEmpty()) {
                log.info("[{}] 没有超时的任务", methodName);
                return;
            }

            log.info("[{}] 发现{}个可能超时的任务", methodName, potentialTimeoutTasks.size());

            // 处理每个可能超时的任务
            for (DigitalVideoTaskPO task : potentialTimeoutTasks) {
                try {
                    log.info("[{}] 处理超时任务：taskId={}, createdTime={}, updateTime={}",
                            methodName, task.getTaskId(), task.getCreatedTime(), task.getUpdateTime());

                    // 更新任务状态为超时
                    task.setStatus(VideoTaskStatusEnum.TIMEOUT.getValue());
                    task.setErrorMsg("任务处理超时，请重试");
                    task.setUpdateTime(new Date());
                    digitalVideoTaskMapper.updateById(task);

                    // 推送任务状态更新
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.TIMEOUT.getValue(), "任务处理超时，请重试");

                    log.info("[{}] 任务{}已标记为超时", methodName, task.getTaskId());

                } catch (Exception e) {
                    log.error("[{}] 处理超时任务{}异常", methodName, task.getTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常", methodName, e);
        }
    }

    @Override
    public void processVideo() {
        String methodName = "processVideo";
        try {
            // 1. 从digital_user_avatar表获取video_task_status=0的记录并更新为3（处理中）
            LambdaQueryWrapper<DigitalUserAvatarPO> queryWrapper = new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getVideoTaskStatus, CommonConst.VIDEO_TASK_STATUS_0)
                    .last("LIMIT 1 FOR UPDATE");
            // 加锁防止并发处理

            DigitalUserAvatarPO avatarRecord = digitalUserAvatarMapper.selectOne(queryWrapper);
            if (avatarRecord == null) {
//                log.info("[{}] 没有待处理的视频任务", methodName);
                return;
            }

            // 更新状态为3（处理中）
            avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_3);
            avatarRecord.setTaskStatus(TaskStatusEnum.IN_PROGRESS.getValue());
            digitalUserAvatarMapper.updateById(avatarRecord);
            pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");

            // 2. 构造avatarVO对象
            ChanJingAvatarRequestDTO avatarVO = new ChanJingAvatarRequestDTO();
            avatarVO.setName(avatarRecord.getUserId() + "_" + avatarRecord.getAvatarName() + "_" + avatarRecord.getAvatarId());
            avatarVO.setMaterialVideo(avatarRecord.getAvatarVideoUrl());
            avatarVO.setTrainType("figure"); // 默认传入"figure"
            avatarVO.setCallback(""); // 回调地址为空

            // 3. 调用禅镜服务创建形象
            log.info("[{}] 开始创建禅镜形象，taskId={}, userId={}", methodName, avatarRecord.getId(), avatarRecord.getUserId());
            Result<String> response = digitalAvatarService.createChanjingAvatar(avatarVO);

            // 4. 处理结果
            if (response.isSuccess()) {
                // 成功：更新状态为1
                avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_1);
                avatarRecord.setAvatarVideoId(response.getData());
                avatarRecord.setTaskStatus(TaskStatusEnum.VIDEO_SUBMIT_SUCCESS.getValue());
                digitalUserAvatarMapper.updateById(avatarRecord);
                log.info("[{}] 禅镜形象创建成功，taskId={}", methodName, avatarRecord.getId());
                pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.VIDEO_SUBMIT_SUCCESS.getValue(), "任务已提交到异步服务处理");
            } else {
                // 失败：更新状态为2，记录错误信息
                avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_2);
                avatarRecord.setVideoErrorMsg(response.getMessage()+"--"+response.getData());
                avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());
                digitalUserAvatarMapper.updateById(avatarRecord);

                // 飞书报警
                String errorMsg = "ERRORInfo=" + response.getMessage()+"--"+response.getData();
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "禅镜生成形象失败", errorMsg);
                log.error("[{}] 禅镜形象创建失败，taskId={}, error={}", methodName, avatarRecord.getId(), errorMsg);
                pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.FAILED.getValue(), "失败，请稍后再试");
            }
        } catch (Exception e) {
            log.error("[{}] 处理视频任务异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "处理视频任务异常", "ERRORInfo=" + e.getMessage());
        }
    }


    @Override
    public void processVoiceUpload() {
        String methodName = "processVoiceUpload";
        try {
            // 1. 获取并锁定待处理的语音任务记录（状态为0）
            LambdaQueryWrapper<DigitalUserAvatarPO> queryWrapper = new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getVoiceTaskStatus, VOICE_TASK_STATUS_0) // 状态为0（待处理）
                    .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

            DigitalUserAvatarPO avatarRecord = digitalUserAvatarMapper.selectOne(queryWrapper);
            if (avatarRecord == null) {
//                log.info("[{}] 没有待处理的语音任务", methodName);
                return;
            }

            // 更新状态为3（处理中）
            avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_3);
            digitalUserAvatarMapper.updateById(avatarRecord);

            // 2. 获取语音文件并构造上传请求
            // 假设voice_file是从avatarRecord.getVoiceUrl()获取的文件
            MultipartFile voiceFile = getVoiceFileFromUrl(avatarRecord.getVoiceUrl());

            MiniMaxFileUploadRequestBO uploadRequestBO = new MiniMaxFileUploadRequestBO();
            uploadRequestBO.setPurpose("voice_clone");
            uploadRequestBO.setFile(voiceFile);

            // 3. 调用MiniMax接口上传语音
            log.info("[{}] 开始上传语音到MiniMax，userId={}, voiceUrl={}",
                    methodName, avatarRecord.getUserId(), avatarRecord.getVoiceUrl());

            Result<String> response = MiniMaxApiUtil.uploadFileToMiniMaxNew(uploadRequestBO);

            // 4. 处理结果
            if (response.isSuccess()) {
                // 成功：更新状态为1
                avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_1);
                avatarRecord.setVoiceErrorMsg("语音上传成功");
                avatarRecord.setAvatarVoiceId(response.getData());
                avatarRecord.setTaskStatus(TaskStatusEnum.VOICE_UPLOAD_SUCCESS.getValue());
                pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.VOICE_UPLOAD_SUCCESS.getValue(), "处理中");
                log.info("[{}] 语音上传成功，fileId={}", methodName, response.getData());
            } else {
                // 失败：更新状态为2，记录错误信息
                avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_2);
                avatarRecord.setVoiceErrorMsg(response.getMessage());
                avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());
                // 飞书报警
                String errorMsg = "ERRORInfo=" + response.getMessage();
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "minimax语音失败", errorMsg);
                log.error("[{}] 语音上传失败，error={}", methodName, errorMsg);
            }

            digitalUserAvatarMapper.updateById(avatarRecord);
        } catch (Exception e) {
            log.error("[{}] 处理语音上传异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "处理语音上传异常", "ERRORInfo=" + e.getMessage());
        }
    }

    // 辅助方法：从URL获取语音文件
    private MultipartFile getVoiceFileFromUrl(String voiceUrl) throws IOException {
        String methodName = "getVoiceFileFromUrl";
        log.info("[{}] 开始下载语音文件，URL: {}", methodName, voiceUrl);

        // 1. 创建临时文件
        Path tempFile = Files.createTempFile("voice_", ".tmp");
        tempFile.toFile().deleteOnExit(); // JVM退出时删除临时文件

        try {
            // 2. 从URL下载文件
            URL url = new URL(voiceUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(30000);   // 30秒读取超时

            // 3. 检查HTTP响应状态
            if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP请求失败，状态码: " + connection.getResponseCode());
            }

            // 4. 下载文件到临时目录
            try (InputStream in = connection.getInputStream();
                 OutputStream out = Files.newOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 5. 获取原始文件名
            String originalFilename = getFilenameFromUrl(voiceUrl);

            // 6. 转换为MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(
                    originalFilename,
                    originalFilename,
                    connection.getContentType(),
                    Files.readAllBytes(tempFile)
            );

            log.info("[{}] 语音文件下载成功，保存路径: {}", methodName, tempFile);
            return multipartFile;

        } catch (Exception e) {
            // 清理失败的临时文件
            Files.deleteIfExists(tempFile);
            throw new IOException("下载语音文件失败: " + e.getMessage(), e);
        }
    }

    // 辅助方法：从URL提取文件名
    private String getFilenameFromUrl(String url) {
        try {
            String path = new URL(url).getPath();
            return path.substring(path.lastIndexOf('/') + 1);
        } catch (MalformedURLException e) {
            return "voice_" + System.currentTimeMillis() + ".tmp";
        }
    }



    @Override
    public void processFinish() {
        String methodName = "processFinish";
        try {
            // 1. 从digital_user_avatar表获取已完成的任务记录
            LambdaQueryWrapper<DigitalUserAvatarPO> queryWrapper = new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getVoiceTaskStatus, VOICE_TASK_STATUS_4)
                    .eq(DigitalUserAvatarPO::getVideoTaskStatus, VIDEO_TASK_STATUS_4)
                    .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                    .ne(DigitalUserAvatarPO::getTaskStatus, TaskStatusEnum.SUCCESS.getValue())
                    .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

            DigitalUserAvatarPO avatarRecord = digitalUserAvatarMapper.selectOne(queryWrapper);
            if (avatarRecord == null) {
//                log.info("[{}] 没有已完成的任务", methodName);
                return;
            }

            // 2. 更新任务状态为成功
            avatarRecord.setTaskStatus(TaskStatusEnum.SUCCESS.getValue());
            digitalUserAvatarMapper.updateById(avatarRecord);
            log.info("[{}] 任务完成状态已更新：userId={}, avatarId={}",
                    methodName, avatarRecord.getUserId(), avatarRecord.getAvatarId());

            // 3. 推送任务完成通知
            pushTaskStatus(
                    avatarRecord.getUserId(),
                    avatarRecord.getAvatarId(),
                    TaskStatusEnum.SUCCESS.getValue(),
                    "任务成功"
            );

        } catch (Exception e) {
            log.error("[{}] 处理任务完成状态异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理任务完成状态异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }


    @Override
    public void processVideoTrainStatus() {
        String methodName = "processVideoTrainStatus";
        try {
            // 1. 获取并锁定待处理的任务记录（状态为1）
            LambdaQueryWrapper<DigitalUserAvatarPO> queryWrapper = new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getVideoTaskStatus, VIDEO_TASK_STATUS_1)
                    .last("LIMIT 1 FOR UPDATE");

            DigitalUserAvatarPO avatarRecord = digitalUserAvatarMapper.selectOne(queryWrapper);
            if (avatarRecord == null) {
//                log.info("[{}] 没有待处理的训练状态检查任务", methodName);
                return;
            }

            // 更新状态为6（检查中）
            avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_6);
            digitalUserAvatarMapper.updateById(avatarRecord);

            // 2. 调用禅镜服务查询训练状态
            log.info("[{}] 开始检查禅镜形象训练状态，avatarVideoId={}", methodName, avatarRecord.getAvatarVideoId());
            Result<CustomisedPersonResponse> response = digitalAvatarService.customisedPerson(avatarRecord.getAvatarVideoId());

            // 3. 处理查询结果
            if (response.isSuccess()) {
                CustomisedPersonResponse result = response.getData();
                int trainStatus = result.getData().getStatus();
                Integer progress = result.getData().getProgress(); // 假设有获取进度的方法

                switch (trainStatus) {
                    case 1: // 制作中
                        avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_1);
                        avatarRecord.setVideoErrorMsg("训练中，进度：" + progress);
                        log.info("[{}] 训练中，进度：{}", methodName, progress);
                        break;

                    case 2: // 成功
                        avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_4);
                        avatarRecord.setVideoErrorMsg("训练成功完成");
                        avatarRecord.setTaskStatus(TaskStatusEnum.VIDEO_TRAIN_SUCCESS.getValue());
                        avatarRecord.setWidth(result.getData().getWidth());
                        avatarRecord.setHeight(result.getData().getHeight());
                        pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.VIDEO_TRAIN_SUCCESS.getValue(), "任务已提交到异步服务处理");
                        log.info("[{}] 训练成功完成", methodName);
                        break;

                    case 4: // 失败
                    case 5: // 系统错误
                        avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_5);
                        String errorMsg = "训练失败，原因：" + result.getData().getErrReason() +
                                "，详细：" + result.getData().getReason();
                        avatarRecord.setVideoErrorMsg(errorMsg);
                        avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());

                        // 飞书报警
                        BFeiShuUtil.sedCardWarnFromText(
                                BFeiShuUtil.P1,
                                "禅镜形象训练失败",
                                "avatarVideoId=" + avatarRecord.getAvatarVideoId() +
                                        "，错误原因：" + errorMsg
                        );
                        log.error("[{}] 训练失败：{}", methodName, errorMsg);
                        break;

                    default:
                        String unknownMsg = "未知训练状态：" + trainStatus;
                        avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_5);
                        avatarRecord.setVideoErrorMsg(unknownMsg);
                        avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());
                        // 飞书报警
                        BFeiShuUtil.sedCardWarnFromText(
                                BFeiShuUtil.P1,
                                "禅镜形象训练状态异常",
                                "avatarVideoId=" + avatarRecord.getAvatarVideoId() +
                                        "，未知状态：" + trainStatus
                        );
                        log.error("[{}] {}", methodName, unknownMsg);
                }

                digitalUserAvatarMapper.updateById(avatarRecord);
            } else {
                // 查询服务失败处理
                avatarRecord.setVideoTaskStatus(VIDEO_TASK_STATUS_5);
                avatarRecord.setVideoErrorMsg("查询训练状态失败：" + response.getMessage());
                avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());
                digitalUserAvatarMapper.updateById(avatarRecord);

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "禅镜训练状态查询失败",
                        "avatarVideoId=" + avatarRecord.getAvatarVideoId() +
                                "，错误信息：" + response.getMessage()
                );
                log.error("[{}] 查询训练状态失败：{}", methodName, response.getMessage());
            }
        } catch (Exception e) {
            log.error("[{}] 处理训练状态异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理训练状态异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }

    @Override
    public void processVoiceClone() {
        String methodName = "processVoiceClone";
        try {
            // 1. 获取并锁定待处理的任务记录（状态为1）
            LambdaQueryWrapper<DigitalUserAvatarPO> queryWrapper = new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getVoiceTaskStatus, VOICE_TASK_STATUS_1) // 状态为1（待克隆）
                    .last("LIMIT 1 FOR UPDATE");

            DigitalUserAvatarPO avatarRecord = digitalUserAvatarMapper.selectOne(queryWrapper);
            if (avatarRecord == null) {
//                log.info("[{}] 没有待处理的语音克隆任务", methodName);
                return;
            }

            // 更新状态为6（处理中）
            avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_6);
            digitalUserAvatarMapper.updateById(avatarRecord);

            // 2. 准备请求参数
            DigitalVoiceCloningDTO audioDTO = new DigitalVoiceCloningDTO();
            audioDTO.setFileId(Long.parseLong(avatarRecord.getAvatarVoiceId()));
            audioDTO.setVoiceName(avatarRecord.getAvatarName());
            audioDTO.setAvatarId(avatarRecord.getAvatarId());

            // 3. 调用语音克隆服务
            log.info("[{}] 开始语音克隆，userId={}, voiceId={}",
                    methodName, avatarRecord.getUserId(), avatarRecord.getAvatarVoiceId());

            Result<String> response = digitalAudioService.voiceCloning(audioDTO, avatarRecord.getUserId());

            // 4. 处理结果
            if (response.isSuccess()) {
                avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_4); // 4表示克隆成功
                avatarRecord.setVoiceErrorMsg("克隆成功");
                log.info("[{}] 语音克隆成功", methodName);
                avatarRecord.setTaskStatus(TaskStatusEnum.VOICE_CLONE_SUCCESS.getValue());
                pushTaskStatus(avatarRecord.getUserId(), avatarRecord.getAvatarId(), TaskStatusEnum.VOICE_CLONE_SUCCESS.getValue(), "处理中");
            } else {
                // 服务调用失败处理
                avatarRecord.setVoiceTaskStatus(VOICE_TASK_STATUS_5);
                avatarRecord.setVoiceErrorMsg("语音克隆服务调用失败：" + response.getMessage());
                avatarRecord.setTaskStatus(TaskStatusEnum.FAILED.getValue());
                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "语音克隆服务调用失败",
                        "avatarVoiceId=" + avatarRecord.getAvatarVoiceId() +
                                "，错误信息：" + response.getMessage()
                );
                log.error("[{}] 语音克隆服务调用失败：{}", methodName, response.getMessage());
            }

            digitalUserAvatarMapper.updateById(avatarRecord);
        } catch (Exception e) {
            log.error("[{}] 处理语音克隆异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理语音克隆异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }

    @Override
    public void processVideoGenerationTaskStatus() {
        // 用digitalVideoTaskMapper查询待处理的任务，并更新为IN_PROGRESS中的状态，用for update锁住防止并发
        List<DigitalVideoTaskPO> tasks = digitalVideoTaskMapper.selectList(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                        .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                        .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                        .last("LIMIT 1 FOR UPDATE") // 加锁防止并发
        );

        if (!tasks.isEmpty()) {
            for (DigitalVideoTaskPO task : tasks) {
                task.setStatus(VideoTaskStatusEnum.IN_PROGRESS.getValue());
                digitalVideoTaskMapper.updateById(task); // 更新任务状态为进行中
            }
        }
    }

    @Override
    public void processVideoGeneration() {
        String methodName = "processVideoGeneration";
        try {
            // 1. 查询并锁定待处理的视频生成任务（状态为VIDEO_GENERNA_STATUS_0，audio_url非空）
            LambdaQueryWrapper<DigitalVideoTaskItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_0)
                    .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                    .isNotNull(DigitalVideoTaskItemPO::getAudioUrl)
                    .gt(DigitalVideoTaskItemPO::getAudioUrl, "")  // 确保audio_url长度大于0
                    .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发

            DigitalVideoTaskItemPO subTask = digitalVideoTaskItemMapper.selectOne(queryWrapper);
            if (subTask == null) {
//                log.info("[{}] 没有待处理的视频生成任务", methodName);
                return;
            }

            // 2. 更新子任务状态为处理中（VIDEO_GENERNA_STATUS_3）
            subTask.setVideoStatus(CommonConst.VIDEO_GENERNA_STATUS_3);
            digitalVideoTaskItemMapper.updateById(subTask);

            // 3. 查询关联的digital_user_avatar记录
            DigitalUserAvatarPO avatarUser = digitalUserAvatarMapper.selectOne(
                    new LambdaQueryWrapper<DigitalUserAvatarPO>()
                            .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                            .eq(DigitalUserAvatarPO::getAvatarId, subTask.getAvatarId())
            );
            // 查询系统数字人表
            DigitalSystemAvatarPO avatarSystem = digitalSystemAvatarMapper.selectOne(
                    new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                            .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                            .eq(DigitalSystemAvatarPO::getAvatarId, subTask.getAvatarId())
            );
            if (avatarUser == null && avatarSystem == null) {
                String errorMsg = "未找到对应的数字人形象记录";
                log.error("[{}] {}: avatarId={}", methodName, errorMsg, subTask.getAvatarId());

                // 更新子任务状态为失败
                digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                        .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_5)
                        .set(DigitalVideoTaskItemPO::getErrorMsg, errorMsg)
                        .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "未找到对应的数字人形象记录",
                        "ERRORInfo=" + errorMsg+"--"+subTask.getAvatarId()
                );
                return;
            }

            // 4. 构建视频合成请求参数
            CreateVideoRequestDTO requestDTO = new CreateVideoRequestDTO();

            AvatarRecord avatarRecord = (avatarUser != null) ? avatarUser : avatarSystem;
            // 设置人物配置
            VideoPersonConfig person = new VideoPersonConfig();
            person.setId(avatarRecord.getAvatarVideoId());  // 使用digital_user_avatar表中的avatar_video_id
            person.setWidth(avatarRecord.getWidth());       // 使用digital_user_avatar表中的width
            person.setHeight(avatarRecord.getHeight());     // 使用digital_user_avatar表中的height
            if (StringUtils.isNotEmpty(avatarRecord.getFiguresType())){
                person.setFigureType(avatarRecord.getFiguresType());
            }
            requestDTO.setPerson(person);

            // 设置音频配置
            VideoAudioConfig audio = new VideoAudioConfig();
            audio.setWavUrl(subTask.getAudioUrl());  // 使用DigitalVideoTaskItemPO中的audio_url
            requestDTO.setAudio(audio);

            // 设置屏幕尺寸
            requestDTO.setScreenHeight(avatarRecord.getHeight());
            requestDTO.setScreenWidth(avatarRecord.getWidth());

            int multiplier = digitalVideoAsyncService.countAudioLength(subTask.getTaskId());

            boolean checkValue = feiyongService.checkYueMultiplier(
                    Long.valueOf(subTask.getUserId()),
                    IdWorker.getId(),
                    "萤火虫生成数字人视频扣费校验",
                    DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey(),
                    multiplier
            );
            if (!checkValue){
                String errorMsg = "视频合成服务余额校验失败userId：" + subTask.getUserId();
                log.error("[{}] {}", methodName, errorMsg);

                digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                        .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_2)
                        .set(DigitalVideoTaskItemPO::getVideoMsg, errorMsg)
                        .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));
            }


            // 5. 调用视频合成服务
            log.info("[{}] 开始调用视频合成服务，taskId={}, subTaskId={}", methodName, subTask.getTaskId(), subTask.getSubTaskId());
            Result<String> response = digitalAvatarService.createChanjingVideo(requestDTO);

            // 6. 处理视频合成结果
            if (response.isSuccess()) {
                // 视频生成成功：更新子任务状态为成功，并保存视频URL
                digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                        .set(DigitalVideoTaskItemPO::getStatus, VideoTaskItemStatusEnum.VIDEO_SUCCESS.getValue())
                        .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_1)
                        .set(DigitalVideoTaskItemPO::getVideoId, response.getData())  // 假设response包含生成的视频URL
                        .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));

                log.info("[{}] 视频生成成功，videoUrl={}", methodName, response.getData());
            } else {
                // 视频生成失败：更新子任务状态，并发送飞书报警
                String errorMsg = "视频合成服务调用失败：" + response.getMessage();
                log.error("[{}] {}", methodName, errorMsg);

                digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                        .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_2)
                        .set(DigitalVideoTaskItemPO::getVideoMsg, errorMsg)
                        .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "视频合成服务调用失败",
                        "ERRORInfo=" + errorMsg
                );
            }
        } catch (Exception e) {
            log.error("[{}] 处理视频生成异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理视频生成异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }


    @Override
    public void processVoiceGeneration() {
        String methodName = "processVoiceGeneration";
        try {
            // 1. 查询并锁定待处理的语音生成任务（状态为VOICE_GENERNA_STATUS_0）
            LambdaQueryWrapper<DigitalVideoTaskItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DigitalVideoTaskItemPO::getVoiceStatus, CommonConst.VOICE_GENERNA_STATUS_0)
                         .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                         .isNull(DigitalVideoTaskItemPO::getAudioUrl)
                         .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发

            DigitalVideoTaskItemPO subTask = digitalVideoTaskItemMapper.selectOne(queryWrapper);
            if (subTask == null) {
//                log.info("[{}] 没有待处理的语音生成任务", methodName);
                return;
            }

            // 2. 获取关联的用户ID和任务信息
            String taskId = subTask.getTaskId();
            String userId = subTask.getUserId();

            // 3. 更新子任务状态为处理中（VOICE_GENERNA_STATUS_3）
            subTask.setVoiceStatus(CommonConst.VOICE_GENERNA_STATUS_3);
            digitalVideoTaskItemMapper.updateById(subTask);

            // 4. 调用音频生成服务
            log.info("[{}] 开始生成音频，taskId={}, subTaskId={}", methodName, taskId, subTask.getSubTaskId());
            Result<DigitalAudioVO> audioResponse = digitalVideoAsyncService.submitAudioTask(subTask, userId);

            // 5. 处理音频生成结果
            if (audioResponse == null || !audioResponse.isSuccess()) {
                String errorMsg = "音频生成失败：" + (audioResponse != null ? audioResponse.getMessage() : "未知错误");
                log.error("[{}] {}", methodName, errorMsg);

                // 更新子任务状态为失败
                digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                        .set(DigitalVideoTaskItemPO::getStatus, VideoTaskItemStatusEnum.AUDIO_FAILED.getValue())
                        .set(DigitalVideoTaskItemPO::getVoiceStatus, CommonConst.VOICE_GENERNA_STATUS_5)
                        .set(DigitalVideoTaskItemPO::getVoiceMsg, errorMsg)
                        .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "音频生成失败", "ERRORInfo=" + errorMsg);
                return;
            }

            // 6. 成功生成音频，更新子任务状态为成功，并保存音频URL和时长
            DigitalAudioVO audioData = audioResponse.getData();
            digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                    .set(DigitalVideoTaskItemPO::getStatus, VideoTaskItemStatusEnum.AUDIO_SUCCESS.getValue())
                    .set(DigitalVideoTaskItemPO::getVoiceStatus, CommonConst.VOICE_GENERNA_STATUS_4)
                    .set(DigitalVideoTaskItemPO::getAudioUrl, audioData.getAudioUrl())
                    .set(DigitalVideoTaskItemPO::getAudioLength, audioData.getAudioLength())
                    .eq(DigitalVideoTaskItemPO::getSubTaskId, subTask.getSubTaskId()));

            log.info("[{}] 音频生成成功，audioUrl={}", methodName, audioData.getAudioUrl());

        } catch (Exception e) {
            log.error("[{}] 处理语音生成异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "处理语音生成异常", "ERRORInfo=" + e.getMessage());
        }
    }


    @Override
    public void processVideoTaskStatusFinish() {
        String methodName = "processVideoTaskStatusFinish";

        // 1. 查询并锁定待处理的任务（状态为VIDEO_TASK_STATUS_4和VOICE_TASK_STATUS_4）
        LambdaQueryWrapper<DigitalVideoTaskItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_4)
                .eq(DigitalVideoTaskItemPO::getVoiceStatus, CommonConst.VOICE_GENERNA_STATUS_4)
                .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                .isNull(DigitalVideoTaskItemPO::getTotalStatus)
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发

        DigitalVideoTaskItemPO subTask = digitalVideoTaskItemMapper.selectOne(queryWrapper);
        if (subTask == null) {
//                log.info("[{}] 没有待处理的完成任务", methodName);
            return;
        }

        //更新taskITemtotalstatus =ITEM_TASK_TOTAL_STATUS_1
        digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                .set(DigitalVideoTaskItemPO::getTotalStatus, CommonConst.ITEM_TASK_TOTAL_STATUS_2)
                .eq(DigitalVideoTaskItemPO::getId, subTask.getId()));

        // 2. 查询主任务
        DigitalVideoTaskPO mainTask = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                        .eq(DigitalVideoTaskPO::getTaskId, subTask.getTaskId())
                        .eq(DigitalVideoTaskPO::getIsDeleted, 0)
        );

        if (mainTask == null || mainTask.getStatus() == VideoTaskStatusEnum.SUCCESS.getValue()) {
            log.warn("[{}] 主任务不存在或已处理完成: taskId={}", methodName, subTask.getTaskId());
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "视频状态顺序异常",
                    "ERRORInfo=" + methodName+"--"+subTask.getTaskId()
            );
            return;
        }

        // 3. 查询该主任务下的所有子任务，检查是否都已完成
        List<DigitalVideoTaskItemPO> allSubTasks = digitalVideoTaskItemMapper.selectList(
                new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                        .eq(DigitalVideoTaskItemPO::getTaskId, subTask.getTaskId())
                        .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
        );

        boolean allCompleted = allSubTasks.stream()
                .allMatch(task -> task.getTotalStatus().equals(ITEM_TASK_TOTAL_STATUS_2));

        if (!allCompleted) {
            log.info("[{}] 主任务{}下并非所有子任务都已完成", methodName, subTask.getTaskId());
            return;
        }
        boolean koufeiResult = false;
        Long logId = IdWorker.getId(); // 生成日志ID
        String userId = mainTask.getUserId();
        int multiplier = 1;
        try {
            // 4. 执行合并操作
            log.info("[{}] 开始合并视频：taskId={}", methodName, subTask.getTaskId());
//            feiyongService.checkYueMulti( userId, logId, "视频合成", "视频合成");

            Result<String> mergeResult = digitalVideoAsyncService.checkAndMergeVideos(mainTask);

            if (!mergeResult.isSuccess()) {
                String errorMsg = "视频合并失败";
                log.error("[{}] {}", methodName, errorMsg);

                // 更新任务状态为失败
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(subTask.getTaskId())
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                );

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "视频合成失败",
                        "ERRORInfo=" + errorMsg
                );

                pushTaskStatus(mainTask.getUserId(), subTask.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
                return;
            }

            // 5. 计算费用
            multiplier = digitalVideoAsyncService.countAudioLength(subTask.getTaskId()); // 计算音频长度

            // 6. 扣费


            koufeiResult = feiyongService.koufei(
                    Long.valueOf(userId),
                    logId,
                    "萤火虫生成数字人视频扣费",
                    DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey(),
                    multiplier
            );

            if (!koufeiResult) {
                String errorMsg = "扣费失败";
                log.error("[{}] {}", methodName, errorMsg);

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "视频生成扣费失败",
                        "ERRORInfo=" + errorMsg
                );
            }

            // 7. 更新任务状态为成功
            digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(subTask.getTaskId())
                    .status(VideoTaskStatusEnum.SUCCESS.getValue())
                    .build()
            );

            // 推送任务完成通知
            pushTaskStatus(userId, subTask.getTaskId(), VideoTaskStatusEnum.SUCCESS.getValue(), VideoTaskStatusEnum.SUCCESS.getDesc());

            log.info("[{}] 视频任务完成：taskId={}, userId={}", methodName, subTask.getTaskId(), userId);
        } catch (Exception e) {
            log.error("[{}] 处理视频任务完成状态异常", methodName, e);
            if (koufeiResult){
                feiyongService.tuifei(Long.valueOf(userId),logId,"萤火虫生成数字人视频退费",DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey(),multiplier);
            }
            // 7. 更新任务状态为成功
            digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(subTask.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .build()
            );
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理视频任务完成状态异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }

    @Override
    public void processVideoStatus() {
        String methodName = "processVideoStatus";
        try {
            // 1. 查询并锁定待处理的视频任务记录（状态为VIDEO_GENERNA_STATUS_1）
            LambdaQueryWrapper<DigitalVideoTaskItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_1) // 状态为1（待检查）
                    .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                    .last("LIMIT 1 FOR UPDATE");

            DigitalVideoTaskItemPO item = digitalVideoTaskItemMapper.selectOne(queryWrapper);
            if (item == null) {
//                log.info("[{}] 没有待处理的视频状态任务", methodName);
                return;
            }

            // 更新子任务状态为6（处理中）
            item.setVideoStatus(CommonConst.VIDEO_GENERNA_STATUS_6);
            digitalVideoTaskItemMapper.updateById(item);

            // 2. 调用禅镜服务查询视频生成状态
            log.info("[{}] 开始检查视频生成状态，videoId={}", methodName, item.getVideoId());
            Result<GetVideoDetailResponse> response = digitalAvatarService.getVideoDetail(item.getVideoId());

            // 3. 处理结果
            if (response.isSuccess()) {
                GetVideoDetailResponse result = response.getData();
                int status = result.getData().getStatus(); // 获取视频生成状态：1制作中，2成功，4失败，5系统错误
                String userId = item.getUserId();

                switch (status) {
                    case 30: // 成功
                        // 上传到阿里云
                        String path = DigitalFileUtil.uploadDigitalResource(
                                result.getData().getVideoUrl(),
                                UUID.randomUUID().toString(),
                                userId,
                                null,
                                2,
                                false
                        );

                        // 更新子任务状态为4（成功）并保存视频URL
                        digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                                .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_4)
                                .set(DigitalVideoTaskItemPO::getVideoUrl, path)
                                .set(DigitalVideoTaskItemPO::getVideoMsg, "视频上传成功")
                                .eq(DigitalVideoTaskItemPO::getId, item.getId()));

                        log.info("[{}] 视频生成成功，videoUrl={}", methodName, path);
                        break;

//                    case 4: // 失败
//                    case 5: // 系统错误
//                        String errorMsg = "视频生成失败，原因：" + result.getData().getMsg() +
//                                "，详细：" + result.getData().getId();
//                        // 更新子任务状态为5（失败）并记录错误信息
//                        digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
//                                .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_5)
//                                .set(DigitalVideoTaskItemPO::getVideoMsg, errorMsg)
//                                .eq(DigitalVideoTaskItemPO::getId, item.getId()));
//
//                        // 飞书报警
//                        BFeiShuUtil.sedCardWarnFromText(
//                                BFeiShuUtil.P1,
//                                "视频生成失败",
//                                "videoId=" + item.getVideoId() +
//                                        "，错误原因：" + errorMsg
//                        );
//                        log.error("[{}] {}", methodName, errorMsg);
//                        break;

                    case 10: // 制作中
                        String progressMsg = "视频生成中，进度：" + result.getData().getProgress();
                        // 更新子任务状态为1（制作中）并记录进度
                        digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                                .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_1)
                                .set(DigitalVideoTaskItemPO::getVideoMsg, progressMsg)
                                .eq(DigitalVideoTaskItemPO::getId, item.getId()));

                        log.info("[{}] {}", methodName, progressMsg);
                        break;

                    default:
                        String unknownMsg = "未知视频生成状态：" + status;
                        // 更新子任务状态为5（失败）并记录未知状态
                        digitalVideoTaskItemMapper.update(new LambdaUpdateWrapper<DigitalVideoTaskItemPO>()
                                .set(DigitalVideoTaskItemPO::getVideoStatus, CommonConst.VIDEO_GENERNA_STATUS_5)
                                .set(DigitalVideoTaskItemPO::getVideoMsg, unknownMsg)
                                .eq(DigitalVideoTaskItemPO::getId, item.getId()));

                        // 飞书报警
                        BFeiShuUtil.sedCardWarnFromText(
                                BFeiShuUtil.P1,
                                "视频生成状态异常",
                                "videoId=" + item.getVideoId() +
                                        "，未知状态：" + status
                        );
                        log.error("[{}] {}", methodName, unknownMsg);
                }
            } else {
                // 服务调用失败处理
                String errorMsg = "查询视频生成状态失败：" + response.getMessage();
                // 更新digital_user_avatar的记录为5
                digitalUserAvatarMapper.update(new LambdaUpdateWrapper<DigitalUserAvatarPO>()
                        .set(DigitalUserAvatarPO::getVideoTaskStatus, VIDEO_TASK_STATUS_5)
                        .set(DigitalUserAvatarPO::getVideoErrorMsg, errorMsg)
                        .eq(DigitalUserAvatarPO::getAvatarId, item.getAvatarId())); // 假设item中有avatar_id字段

                // 飞书报警
                BFeiShuUtil.sedCardWarnFromText(
                        BFeiShuUtil.P1,
                        "查询视频生成状态失败",
                        "ERRORInfo=" + errorMsg
                );
                log.error("[{}] {}", methodName, errorMsg);
            }
        } catch (Exception e) {
            log.error("[{}] 处理视频状态异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(
                    BFeiShuUtil.P1,
                    "处理视频状态异常",
                    "ERRORInfo=" + e.getMessage()
            );
        }
    }



    /**
     * 推送任务状态更新
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String message) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 开始推送任务状态：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
        
        try {
            // 1. 查询任务信息
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            
            if (task != null) {
                // 2. 如果任务完成或失败，发送通知消息
                if (status != null && (status == VideoTaskStatusEnum.SUCCESS.getValue() || status == VideoTaskStatusEnum.FAILED.getValue())) {
                    log.debug("[{}] 构建通知消息：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
                    
                    // 使用新的数字人专用通知服务
                    int notifType = status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue() : 
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue();
                    
                    String notifContent = message != null ? message : 
                        (status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                            "您的数字人视频已生成完成，点击查看" : 
                            "很抱歉，您的数字人视频生成失败，请重试");
                    
                    // 1. 创建通知记录
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        notifType,
                        notifContent
                    );
                    
                    // 2. 发送通知消息
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("taskId", taskId);
                    dataMap.put("notifType", notifType);
                    dataMap.put("notifTitle", DigitalNotificationEnum.getDesc(notifType));
                    dataMap.put("notifContent", notifContent);
                    
                    String jsonMessage = MessageSendUtil.getJSONStr(
                        userId,
                        BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                        dataMap
                    );
                    BRedisServiceUtil.sendMessageDigital(jsonMessage);
                }
            } else {
                log.warn("[{}] 未找到任务信息，无法推送状态: userId={}, taskId={}", methodName, userId, taskId);
            }
        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
