package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nacos.entity.dto.DigitalVoiceStyleUploadDTO;
import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.mapper.DigitalVoiceStylesMapper;
import com.nacos.result.Result;
import com.nacos.service.DigitalUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.utils.DigitalFileUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalUploadServiceImpl implements DigitalUploadService {

    private final DigitalVoiceStylesMapper digitalVoiceStylesMapper;


    private static final long MAX_FILE_SIZE = 500 * 1024 * 1024L; // 500MB
    private static final String[] ALLOWED_VIDEO_TYPES = {".mp4", ".mov", ".avi"};
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY = 1000; // 1秒

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUploadVoiceStyles(List<DigitalVoiceStyleUploadDTO> uploadDTOList, String userId) {
        if (uploadDTOList == null || uploadDTOList.isEmpty()) {
            return Result.ERROR("上传列表不能为空");
        }

        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();

        for (DigitalVoiceStyleUploadDTO uploadDTO : uploadDTOList) {
            try {
                // 1. 检查音色是否存在
                DigitalVoiceStylesPO voiceStyle = digitalVoiceStylesMapper.selectOne(
                        new LambdaUpdateWrapper<DigitalVoiceStylesPO>()
                                .eq(DigitalVoiceStylesPO::getVoiceId, uploadDTO.getVoiceId())
                                .eq(DigitalVoiceStylesPO::getVoiceType, 1)  // 系统音色
                                .eq(DigitalVoiceStylesPO::getIsDeleted, 0)
                );

                if (voiceStyle == null) {
                    log.error("音色不存在，voiceId：{}", uploadDTO.getVoiceId());
                    failList.add(uploadDTO.getVoiceId());
                    continue;
                }

                // 2. 上传音频文件到OSS
                String fileName = UUID.randomUUID().toString();
                String ossUrl = DigitalFileUtil.uploadDigitalResource(
                        uploadDTO.getAudioFile().getBytes(),
                        fileName,
                        userId,
                        null,
                        10,  // 系统音色类型
                        true  // 是系统文件
                );

                if (ossUrl == null) {
                    log.error("音频文件上传OSS失败，voiceId：{}", uploadDTO.getVoiceId());
                    failList.add(uploadDTO.getVoiceId());
                    continue;
                }

                // 3. 更新数据库
                voiceStyle.setDemoAudio(ossUrl);
                int updated = digitalVoiceStylesMapper.updateById(voiceStyle);

                if (updated > 0) {
                    successList.add(uploadDTO.getVoiceId());
                    log.info("音色音频更新成功，voiceId：{}，ossUrl：{}", uploadDTO.getVoiceId(), ossUrl);
                } else {
                    failList.add(uploadDTO.getVoiceId());
                    log.error("音色音频更新失败，voiceId：{}", uploadDTO.getVoiceId());
                }

            } catch (Exception e) {
                failList.add(uploadDTO.getVoiceId());
                log.error("处理音色音频上传异常，voiceId：{}", uploadDTO.getVoiceId(), e);
            }
        }

        // 4. 汇总处理结果
        if (!failList.isEmpty()) {
            log.error("部分音色音频上传失败，成功：{}个，失败：{}个", successList.size(), failList.size());
            return Result.ERROR(String.format("部分音色音频上传失败，成功：%d个，失败：%d个，失败ID：%s",
                    successList.size(), failList.size(), String.join(",", failList)));
        }

        log.info("所有音色音频上传成功，共：{}个", successList.size());
        return Result.SUCCESS();
    }

    @Override
    public Result<String> uploadVideo(MultipartFile file, String userId, String groupId, Integer isAuth) {
        try {
            // 1. 基础参数验证
            if (file == null || file.isEmpty()) {
                return Result.ERROR("文件不能为空");
            }
            if (userId == null || userId.trim().isEmpty()) {
                return Result.ERROR("用户ID不能为空");
            }
            if (isAuth == null) {
                return Result.ERROR("上传类型不能为空");
            }

            // 2. 文件格式验证
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return Result.ERROR("文件名不能为空");
            }

            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
            boolean isValidFormat = false;
            for (String type : ALLOWED_VIDEO_TYPES) {
                if (type.equals(fileExtension)) {
                    isValidFormat = true;
                    break;
                }
            }
            if (!isValidFormat) {
                return Result.ERROR("不支持的文件格式，仅支持: " + String.join(", ", ALLOWED_VIDEO_TYPES));
            }

            // 3. 文件大小验证
            if (file.getSize() > MAX_FILE_SIZE) {
                return Result.ERROR("文件大小超过限制，最大支持: " + (MAX_FILE_SIZE / 1024 / 1024) + "MB");
            }

            // 4. 生成文件名
            String fileName = UUID.randomUUID().toString();
            String path = null;
            int retryCount = 0;

            // 5. 根据上传类型处理
            while (retryCount < MAX_RETRIES && path == null) {
                try {
                    switch (isAuth) {
                        case 0 -> {
                            // 上传授权文件，类型为0
                            path = DigitalFileUtil.uploadDigitalResource(
                                    file.getBytes(),
                                    fileName,
                                    userId,
                                    null,
                                    4,
                                    false
                            );
                        }
                        case 1 -> {
                            // 上传训练视频，类型为1
                            path = DigitalFileUtil.uploadDigitalResource(
                                    file.getBytes(),
                                    fileName,
                                    userId,
                                    groupId,
                                    1,
                                    false
                            );
                        }
                        case 2 -> {
                            // 上传生成的数字人视频，类型为2
                            path = DigitalFileUtil.uploadDigitalResource(
                                    file.getBytes(),
                                    fileName,
                                    userId,
                                    null,
                                    2,
                                    false
                            );
                        }
                        default -> {
                            return Result.ERROR("无效的上传类型，支持的类型：0-授权文件，1-训练视频，2-生成的数字人视频");
                        }
                    }

                    if (path == null && retryCount < MAX_RETRIES - 1) {
                        log.warn("上传失败，准备第{}次重试", retryCount + 1);
                        Thread.sleep(RETRY_DELAY);
                    }
                } catch (Exception e) {
                    log.error("第{}次上传尝试失败", retryCount + 1, e);
                    if (retryCount < MAX_RETRIES - 1) {
                        Thread.sleep(RETRY_DELAY);
                    }
                }
                retryCount++;
            }

            if (path != null) {
                log.info("文件上传成功，路径：{}", path);
                return Result.SUCCESS("上传成功", path);
            }

            String errorMsg = String.format("上传失败，已重试%d次，请稍后重试", retryCount);
            log.error(errorMsg);
            return Result.ERROR(errorMsg);

        } catch (Exception e) {
            String errorMsg = "系统异常：" + e.getMessage();
            log.error(errorMsg, e);
            return Result.ERROR(errorMsg);
        }
    }


    /**
     * 上传系统音频
     * @param file 音频文件
     * @param userId 用户ID
     * @return
     */
    @Override
    public Result<String> uploadAudio(MultipartFile file, String userId) {
        try {
            // 生成文件名
            String fileName = UUID.randomUUID().toString();
            String path = null;
            // 上传系统音频文件，类型为10
            path = DigitalFileUtil.uploadDigitalResource(
                    file.getBytes(),
                    fileName,
                    userId,
                    null,
                    10,
                    true
            );
            if (path != null) {
                return Result.SUCCESS("上传成功", path);
            }
            return Result.ERROR("上传失败，请稍后重试");
        } catch (Exception e) {
            log.error("上传音频发生未知异常：", e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    @Override
    public Result<String> uploadDigitalVideo(MultipartFile file, String userId) {
        try {
            // 生成文件名
            String fileName = UUID.randomUUID().toString();
            // 上传形象视频，类型为1
            String path = DigitalFileUtil.uploadDigitalResource(
                    file.getBytes(),
                    fileName,
                    userId,
                    null,
                    1,
                    false
            );
            if (path != null) {
                return Result.SUCCESS("上传成功", path);
            }
            return Result.ERROR("上传失败，请稍后重试");
        } catch (IOException e) {
            log.error("上传视频失败：", e);
            return Result.ERROR("文件处理失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传视频发生未知异常：", e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }
} 