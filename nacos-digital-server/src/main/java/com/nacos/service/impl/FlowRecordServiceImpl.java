package com.nacos.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.FlowRecordMapper;
import com.business.db.model.po.FlowRecordPO;
import com.nacos.service.IFlowRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @className: FlowRecordServiceImpl
 * @description: 点子流水记录
 * @author: Admin
 * @createDate: 2023-08-14 14:44
 * @version: 1.0
 */
@Service("digitalFlowRecordService")
@Slf4j
public class FlowRecordServiceImpl extends ServiceImpl<FlowRecordMapper, FlowRecordPO> implements IFlowRecordService {

}
