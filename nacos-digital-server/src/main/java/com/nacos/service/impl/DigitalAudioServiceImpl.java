package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.dto.AudioGenerationResponseDTO;
import com.nacos.entity.dto.TextToSpeechFileRequestDTO;
import com.nacos.entity.dto.TextToSpeechFileResponseDTO;
import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.entity.po.DigitalVoiceUserClonePO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.entity.vo.VoiceListResponseVO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.DigitalVoiceStylesMapper;
import com.nacos.mapper.DigitalVoiceUserCloneMapper;
import com.nacos.result.Result;
import com.nacos.entity.dto.DigitalAudioGenerationDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.model.AzureAudio.AzureAudioApiUtil;
import com.nacos.model.AzureAudio.model.TextToSpeechRequestBO;
import com.nacos.model.AzureAudio.model.TextToSpeechResponseBO;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.enums.MiniMaxVoiceCloneEnum;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceCloneRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationResponseBO;
import com.nacos.service.DigitalAudioService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.service.FeiyongService;
import com.nacos.service.IDigitalAudioTaskService;
import com.nacos.service.AsyncDigitalAudioService;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.nacos.utils.IdGeneratorUtil;
import com.nacos.utils.DigitalFileUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.service.IDigitalAudioTaskService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import com.nacos.entity.dto.TtsParams;
import com.nacos.service.DigitalNotificationService;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.component.AudioGenerationRateLimiter;
import com.nacos.config.SyncAudioConfig;
import com.nacos.service.processor.AudioProviderProcessorFactory;
import com.nacos.service.processor.AudioProviderProcessor;
import com.nacos.entity.enums.AudioTaskStatusEnum;

/**
 * 数字音频服务实现类
 * 处理音频上传、声音克隆、音色管理等功能
 */
@Slf4j
@Service
public class DigitalAudioServiceImpl implements DigitalAudioService {

    private final DigitalVoiceUserCloneMapper userVoiceStylesMapper;
    private final DigitalVoiceStylesMapper digitalVoiceStylesMapper;
    private final IDigitalAudioTaskService digitalAudioTaskService;
    private final AsyncDigitalAudioService asyncDigitalAudioService;
    private final ObjectMapper objectMapper;
    private final DigitalNotificationService digitalNotificationService;
    private final AudioGenerationRateLimiter rateLimiter;
    private final SyncAudioConfig syncAudioConfig;
    private final AudioProviderProcessorFactory processorFactory;

    @Resource
    FeiyongService feiyongService;
    
    // Azure语音服务配置
    @Value("${azure.speech.subscription-key:}")
    private String azureSubscriptionKey;
    
    // Azure语音服务区域
    @Value("${azure.speech.region:}")
    private String azureRegion;

    public DigitalAudioServiceImpl(DigitalVoiceUserCloneMapper userVoiceStylesMapper,
            DigitalVoiceStylesMapper digitalVoiceStylesMapper,
            IDigitalAudioTaskService digitalAudioTaskService,
            @Lazy AsyncDigitalAudioService asyncDigitalAudioService,
            ObjectMapper objectMapper,
            DigitalNotificationService digitalNotificationService,
            AudioGenerationRateLimiter rateLimiter,
            SyncAudioConfig syncAudioConfig,
            AudioProviderProcessorFactory processorFactory) {
        this.userVoiceStylesMapper = userVoiceStylesMapper;
        this.digitalVoiceStylesMapper = digitalVoiceStylesMapper;
        this.digitalAudioTaskService = digitalAudioTaskService;
        this.asyncDigitalAudioService = asyncDigitalAudioService;
        this.objectMapper = objectMapper;
        this.digitalNotificationService = digitalNotificationService;
        this.rateLimiter = rateLimiter;
        this.syncAudioConfig = syncAudioConfig;
        this.processorFactory = processorFactory;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitAudioGenerationTask(AudioGenerationRequestDTO requestDTO, String userId) {
        String methodName = "submitAudioGenerationTask";
        try {
            if (requestDTO == null) {
                return Result.ERROR("请求参数不能为空");
            }
            if (userId == null || userId.trim().isEmpty()) {
                return Result.ERROR("用户ID不能为空");
            }
            
            // 确保TtsParams存在，如果不存在则创建
            if (requestDTO.getTtsParams() == null) {
                requestDTO.setTtsParams(new TtsParams());
            }

            // 验证基础参数
            TtsParams ttsParams = requestDTO.getTtsParams();
            if (ttsParams.getVoiceId() == null || ttsParams.getVoiceId().trim().isEmpty()) {
                return Result.ERROR("音色ID不能为空");
            }
            if (ttsParams.getText() == null || ttsParams.getText().trim().isEmpty()) {
                return Result.ERROR("待合成的文本不能为空");
            }
            if (ttsParams.getSpeed() == null || ttsParams.getSpeed() <= 0) {
                return Result.ERROR("语速参数无效");
            }

            String taskId = "au-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                    + UUID.randomUUID().toString().substring(0, 4);
            String requestParamsJson;
            try {
                // 直接序列化TtsParams而不是转换后的参数
                requestParamsJson = objectMapper.writeValueAsString(ttsParams);
            } catch (JsonProcessingException e) {
                log.error("[{}] 序列化TTS参数失败: taskId={}，error={}", methodName, taskId, e.getMessage());
                return Result.ERROR("参数序列化失败");
            }
            DigitalAudioTaskPO taskPO = DigitalAudioTaskPO.builder()
                    .taskId(taskId)
                    .userId(userId)
                    .taskName(requestDTO.getTaskName())
                    .provider(requestDTO.getProvider().toUpperCase())
                    .status(0) // 排队中
                    .requestParamsJson(requestParamsJson)
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            Result<String> result = digitalAudioTaskService.createTask(taskPO);
            if (!result.isSuccess()) {
                log.error("[{}] 创建任务失败: taskId={}", methodName, taskId);
                return Result.ERROR(result.getMessage());
            }
            log.info("[{}] 创建任务成功，已加入处理队列: taskId={}, userId={}, provider={}", 
                    methodName, taskId, userId, requestDTO.getProvider());
            // 发送"任务已接收"的通知
            try {
                // 使用通知服务发送任务已接收通知
                digitalNotificationService.sendAudioGenerationQueuedNotification(taskPO);
            } catch (Exception e) {
                // 通知发送失败不影响主流程
                log.warn("[{}] 发送任务接收通知失败: taskId={}, error={}", 
                        methodName, taskId, e.getMessage());
            }
            return Result.SUCCESS("任务已提交，正在排队等待处理", taskId);
        } catch (Exception e) {
            log.error("[{}] 提交音频生成任务异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 音频上传功能
     *
     * @param file    音频文件
     * @param purpose 上传目的
     * @param userId  用户ID
     * @return 上传结果，成功返回文件路径
     */
    @Override
    public Result<String> uploadAudio(MultipartFile file, String purpose, String userId) {
        String methodName = "uploadAudio";
        try {
            String path = null;
            // 测试调用Util类
            MiniMaxFileUploadRequestBO miniMaxFileUploadRequestBO = new MiniMaxFileUploadRequestBO();
            miniMaxFileUploadRequestBO.setPurpose(purpose);
            miniMaxFileUploadRequestBO.setFile(file);
            path = MiniMaxApiUtil.uploadFileToMiniMax(miniMaxFileUploadRequestBO);
            if (path != null) {
                return Result.SUCCESS("上传成功", path);
            }
            return Result.ERROR("上传失败，请稍后重试");
        } catch (Exception e) {
            log.error("[{}]上传音频发生未知异常：", methodName, e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 声音克隆功能
     * 将用户上传的音频克隆成新的音色
     *
     * @param digitalVoiceCloningDTO 声音克隆参数
     * @param userId                 用户ID
     * @return 克隆结果，成功返回示例音频URL
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> voiceCloning(DigitalVoiceCloningDTO digitalVoiceCloningDTO, String userId) {
        String methodName = "voiceCloning";
        long logId = IdWorker.getId();
        boolean koufeiResult = false;
        try {
            // 1. 参数校验
            if (digitalVoiceCloningDTO == null || userId == null || digitalVoiceCloningDTO.getFileId() == null) {
                return Result.ERROR("参数不完整，请检查输入");
            }

            // 2. 创建PO对象 - 使用构建器模式
            DigitalVoiceUserClonePO userVoiceStylesPO = DigitalVoiceUserClonePO.builder()
                    .userId(userId)
                    .voiceId(IdGeneratorUtil.generateVoiceIdWithUser(userId))
                    .avatarId(digitalVoiceCloningDTO.getAvatarId())
                    .voiceName(digitalVoiceCloningDTO.getVoiceName())
                    .voiceType(2) // 克隆音色
                    .fileId(digitalVoiceCloningDTO.getFileId())
                    .status(0) // 处理中
                    .isDeleted(0) // 未删除（显示）
                    .provider("MINIMAX") // 设置供应商标识
                    .build();

            log.info("[{}]创建声音克隆任务：userId={}, voiceId={}", methodName, userId, userVoiceStylesPO.getVoiceId());

            // 3. 构建声音克隆请求参数
            MiniMaxVoiceCloneRequestBO requestBO = buildVoiceCloneRequestBO(digitalVoiceCloningDTO,
                    userVoiceStylesPO.getVoiceId());

            // 4. 调用声音克隆API
            Result<String> demoAudioRespone = MiniMaxApiUtil.voiceCloningNew(requestBO, userId);

            // 4.1扣费
            koufeiResult = feiyongService.koufei(Long.valueOf(userId), logId, "萤火虫克隆音频扣费",
                    DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey());

            // 5. 处理结果
            if (demoAudioRespone.isSuccess()) {
                userVoiceStylesPO.setDemoAudio(demoAudioRespone.getData());
                userVoiceStylesPO.setStatus(1); // 成功
                userVoiceStylesMapper.insert(userVoiceStylesPO);
                log.info("[{}]声音克隆成功：userId={}, voiceId={}", methodName, userId, userVoiceStylesPO.getVoiceId());
                return Result.SUCCESS("声音克隆成功", demoAudioRespone.getData());
            } else {
                feiyongService.tuifei(Long.valueOf(userId), logId, "萤火虫声音克隆退费", DDUseRuleEnum.VOICE_COPY.getRedisKey());
                // 克隆失败的数据
                userVoiceStylesPO.setStatus(2); // 失败
                userVoiceStylesPO.setIsDeleted(1); // 删除(不显示)
                userVoiceStylesMapper.insert(userVoiceStylesPO);
                log.warn("[{}]声音克隆失败：userId={}, fileId={}", methodName, userId, digitalVoiceCloningDTO.getFileId());
                return Result.ERROR("声音克隆失败，请稍后重试" + demoAudioRespone.getMessage());
            }
        } catch (Exception e) {
            if (koufeiResult) {
                feiyongService.tuifei(Long.valueOf(userId), logId, "萤火虫声音克隆退费", DDUseRuleEnum.VOICE_COPY.getRedisKey());
            }
            log.error("[{}]声音克隆异常：userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 构建声音克隆请求参数
     */
    private MiniMaxVoiceCloneRequestBO buildVoiceCloneRequestBO(DigitalVoiceCloningDTO dto, String voiceId) {
        String methodName = "buildVoiceCloneRequestBO";
        MiniMaxVoiceCloneRequestBO requestBO = new MiniMaxVoiceCloneRequestBO();
        requestBO.setFileId(dto.getFileId());
        requestBO.setVoiceId(voiceId);
        requestBO.setTextValidation(dto.getTextValidation());
        requestBO.setText(dto.getText() != null ? dto.getText() : MiniMaxVoiceCloneEnum.Text.GREETING.getContent());
        requestBO.setModel(
                dto.getModel() != null ? dto.getModel() : MiniMaxVoiceCloneEnum.Model.SPEECH_01_TURBO.getCode());
        requestBO.setAccuracy(
                dto.getAccuracy() != null ? dto.getAccuracy() : MiniMaxVoiceCloneEnum.Accuracy.HIGH.getValue());
        requestBO.setNeedNoiseReduction(dto.getNeedNoiseReduction() != null ? dto.getNeedNoiseReduction() : false);
        requestBO.setNeedVolumeNormalization(
                dto.getNeedVolumeNormalization() != null ? dto.getNeedVolumeNormalization() : false);

        if (dto.getClonePrompt() != null) {
            MiniMaxVoiceCloneRequestBO.ClonePrompt clonePrompt = new MiniMaxVoiceCloneRequestBO.ClonePrompt();
            clonePrompt.setPromptAudio(dto.getClonePrompt().getPromptAudio());
            clonePrompt.setPromptText(dto.getClonePrompt().getPromptText());
            requestBO.setClonePrompt(clonePrompt);
        }

        return requestBO;
    }

    /**
     * 获取系统音色列表
     *
     * @return 系统预设的音色列表
     */
    @Override
    public Result<List<DigitalVoiceStyleVO>> getSystemVoiceStyles() {
        try {
            // 查询系统音色
            List<DigitalVoiceStylesPO> systemVoiceStyles = digitalVoiceStylesMapper.selectList(
                    new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceType, 1) // 系统音色
                            .eq(DigitalVoiceStylesPO::getIsDeleted, 0));

            if (systemVoiceStyles == null || systemVoiceStyles.isEmpty()) {
                return Result.SUCCESS("暂无系统音色数据", new ArrayList<>());
            }

            List<DigitalVoiceStyleVO> voiceStyleVOs = new ArrayList<>();
            for (DigitalVoiceStylesPO style : systemVoiceStyles) {
                DigitalVoiceStyleVO vo = new DigitalVoiceStyleVO();
                vo.setVoiceId(style.getVoiceId());
                vo.setVoiceName(style.getVoiceName());
                vo.setDescription(style.getDescription());
                vo.setDemoAudio(style.getDemoAudio());
                voiceStyleVOs.add(vo);
            }

            return Result.SUCCESS("获取系统音色成功", voiceStyleVOs);
        } catch (Exception e) {
            log.error("获取系统音色发生未知异常：", e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 获取用户音色列表
     *
     * @param userId 用户ID
     * @return 用户克隆的音色列表
     */
    @Override
    public Result<List<DigitalVoiceUserCloneVO>> getUserVoiceStyles(String userId) {
        try {
            // 查询用户克隆音色
            List<DigitalVoiceUserClonePO> userVoiceStyles = userVoiceStylesMapper.selectList(
                    new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                            .eq(DigitalVoiceUserClonePO::getVoiceType, 2) // 克隆音色
                            .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                            .eq(DigitalVoiceUserClonePO::getStatus, 1) // 只查询成功的记录
                            .eq(DigitalVoiceUserClonePO::getUserId, userId));

            if (userVoiceStyles == null || userVoiceStyles.isEmpty()) {
                return Result.SUCCESS("暂无用户自定义音色数据", new ArrayList<>());
            }

            List<DigitalVoiceUserCloneVO> voiceStyleVOs = new ArrayList<>();
            for (DigitalVoiceUserClonePO style : userVoiceStyles) {
                DigitalVoiceUserCloneVO vo = new DigitalVoiceUserCloneVO();
                vo.setVoiceId(style.getVoiceId());
                vo.setVoiceName(style.getVoiceName());
                vo.setDemoAudio(style.getDemoAudio());
                vo.setUserId(style.getUserId());
                vo.setFileId(style.getFileId());
                vo.setStatus(style.getStatus());
                vo.setProvider(style.getProvider());
                voiceStyleVOs.add(vo);
            }

            return Result.SUCCESS("获取用户自定义音色成功", voiceStyleVOs);
        } catch (Exception e) {
            log.error("获取用户音色发生未知异常：userId={}, error={}", userId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 语音生成功能
     * 使用指定音色生成语音
     *
     * @param digitalAudioGenerationDTO 语音生成参数
     * @param userId                    用户ID
     * @return 生成的语音信息
     */
    @Override
    public Result<DigitalAudioVO> voiceGeneration(DigitalAudioGenerationDTO digitalAudioGenerationDTO, String userId) {
        String methodName = "voiceGeneration";
        try {
            // 构建语音生成请求参数
            MiniMaxVoiceGenerationRequestBO requestBO = buildVoiceGenerationRequestBO(digitalAudioGenerationDTO);

            // 调用MiniMaxApiUtil的一站式服务，使用类型4(音频临时文件目录)
            Result<MiniMaxVoiceGenerationResponseBO> result = MiniMaxApiUtil.generateAndProcessAudio(requestBO, userId, null, 4);
            if (!result.isSuccess() || result.getData() == null) {
                log.error("[{}]语音生成失败：{}", methodName, result.getMessage());
                return Result.ERROR("语音生成失败：" + result.getMessage());
            }
            MiniMaxVoiceGenerationResponseBO responseBO = result.getData();
            DigitalAudioVO audioVO = DigitalAudioVO.builder()
                    .audioName("语音生成")
                    .provider("MINIMAX")
                    .audioUrl(responseBO.getAudioUrl())
                    .audioLength(responseBO.getExtraInfo().getAudioLength())
                    .audioFormat(responseBO.getExtraInfo().getAudioFormat())
                    .build();
            return Result.SUCCESS("语音生成成功", audioVO);
            
        } catch (Exception e) {
            log.error("语音生成发生未知异常：", e);
            return Result.ERROR("系统异常，请稍后重试" + e.getMessage());
        }
    }

    /**
     * 构建语音生成请求参数(MiniMax)
     *
     * @param digitalAudioGenerationDTO 语音生成参数DTO
     * @return 构建好的请求参数对象
     */
    private MiniMaxVoiceGenerationRequestBO buildVoiceGenerationRequestBO(
            DigitalAudioGenerationDTO digitalAudioGenerationDTO) {
        String methodName = "buildVoiceGenerationRequestBO";
        MiniMaxVoiceGenerationRequestBO requestBO = new MiniMaxVoiceGenerationRequestBO();
        requestBO.setModel(digitalAudioGenerationDTO.getModel());
        requestBO.setText(digitalAudioGenerationDTO.getText());
        requestBO.setStream(digitalAudioGenerationDTO.getStream());
        requestBO.setLanguageBoost(digitalAudioGenerationDTO.getLanguageBoost());

        // 设置音色参数
        if (digitalAudioGenerationDTO.getVoiceSetting() != null) {
            MiniMaxVoiceGenerationRequestBO.VoiceSetting voiceSetting = new MiniMaxVoiceGenerationRequestBO.VoiceSetting();
            voiceSetting.setSpeed(digitalAudioGenerationDTO.getVoiceSetting().getSpeed());
            voiceSetting.setVol(digitalAudioGenerationDTO.getVoiceSetting().getVol());
            voiceSetting.setPitch(digitalAudioGenerationDTO.getVoiceSetting().getPitch());
            voiceSetting.setVoiceId(digitalAudioGenerationDTO.getVoiceSetting().getVoiceId());
            voiceSetting.setEmotion(digitalAudioGenerationDTO.getVoiceSetting().getEmotion());
            voiceSetting.setLatexRead(digitalAudioGenerationDTO.getVoiceSetting().getLatexRead());
            requestBO.setVoiceSetting(voiceSetting);
        }

        // 设置音频参数
        if (digitalAudioGenerationDTO.getAudioSetting() != null) {
            MiniMaxVoiceGenerationRequestBO.AudioSetting audioSetting = new MiniMaxVoiceGenerationRequestBO.AudioSetting();
            audioSetting.setSampleRate(digitalAudioGenerationDTO.getAudioSetting().getSampleRate());
            audioSetting.setBitrate(digitalAudioGenerationDTO.getAudioSetting().getBitrate());
            audioSetting.setFormat(digitalAudioGenerationDTO.getAudioSetting().getFormat());
            audioSetting.setChannel(digitalAudioGenerationDTO.getAudioSetting().getChannel());
            requestBO.setAudioSetting(audioSetting);
        }

        // 设置音色混合权重
        if (digitalAudioGenerationDTO.getTimberWeights() != null
                && !digitalAudioGenerationDTO.getTimberWeights().isEmpty()) {
            List<MiniMaxVoiceGenerationRequestBO.TimberWeight> timberWeights = digitalAudioGenerationDTO
                    .getTimberWeights().stream()
                    .map(tw -> {
                        MiniMaxVoiceGenerationRequestBO.TimberWeight weight = new MiniMaxVoiceGenerationRequestBO.TimberWeight();
                        weight.setVoiceId(tw.getVoiceId());
                        weight.setWeight(tw.getWeight());
                        return weight;
                    })
                    .toList();
            requestBO.setTimberWeights(timberWeights);
        }

        // 设置发音词典
        if (digitalAudioGenerationDTO.getPronunciationDict() != null
                && digitalAudioGenerationDTO.getPronunciationDict().getTone() != null
                && !digitalAudioGenerationDTO.getPronunciationDict().getTone().isEmpty()) {
            MiniMaxVoiceGenerationRequestBO.PronunciationDict pronunciationDict = new MiniMaxVoiceGenerationRequestBO.PronunciationDict();
            pronunciationDict.setTone(digitalAudioGenerationDTO.getPronunciationDict().getTone());
            requestBO.setPronunciationDict(pronunciationDict);
        }

        return requestBO;
    }


    

    /**
     * 获取支持的语音列表
     * @return 语音列表
     */
    @Override
    public Result<List<VoiceListResponseVO>> getSupportedVoices() {
        String methodName = "getSupportedVoices";
        List<VoiceListResponseVO> result = new ArrayList<>();
        
        try {
            // 获取Microsoft的语音列表
            VoiceListResponseVO microsoftVoices = getMicrosoftVoices();
            if (microsoftVoices != null && microsoftVoices.getVoices() != null && !microsoftVoices.getVoices().isEmpty()) {
                result.add(microsoftVoices);
            }
            
            if (result.isEmpty()) {
                return Result.ERROR("未找到任何支持的语音");
            }
            
            return Result.SUCCESS("获取语音列表成功", result);
        } catch (Exception e) {
            log.error("[{}]获取语音列表失败：{}", methodName, e.getMessage(), e);
            return Result.ERROR("获取语音列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取Microsoft的语音列表
     * @return Microsoft语音列表
     */
    private VoiceListResponseVO getMicrosoftVoices() {
        String methodName = "getMicrosoftVoices";
        try {
            // 验证Azure配置
            if (azureSubscriptionKey == null || azureRegion == null ||
                azureSubscriptionKey.trim().isEmpty() || azureRegion.trim().isEmpty()) {
                log.error("[{}]Azure语音服务配置无效", methodName);
                return null;
            }
            
            // 调用Azure API获取语音列表
            Result<List<com.microsoft.cognitiveservices.speech.VoiceInfo>> voicesResult =
                AzureAudioApiUtil.getVoicesList(azureSubscriptionKey, azureRegion);

            if (!voicesResult.isSuccess() || voicesResult.getData() == null) {
                log.warn("[{}]未获取到Microsoft语音列表：{}", methodName, voicesResult.getMessage());
                return null;
            }

            // 直接处理VoiceInfo列表
            try {
                return processVoicesInfoList(voicesResult.getData());
            } catch (Exception e) {
                log.error("[{}]处理语音列表失败：{}", methodName, e.getMessage(), e);
                return null;
            }
        } catch (Exception e) {
            log.error("[{}]获取Microsoft语音列表失败：{}", methodName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 处理语音列表数据（VoiceInfo版本）
     * @param voicesList VoiceInfo列表
     * @return 处理后的语音列表响应
     */
    private VoiceListResponseVO processVoicesInfoList(List<com.microsoft.cognitiveservices.speech.VoiceInfo> voicesList) {
        String methodName = "processVoicesInfoList";
        List<VoiceListResponseVO.VoiceInfo> voices = new ArrayList<>();

        log.info("[{}]处理语音列表，共{}个语音", methodName, voicesList.size());

        for (com.microsoft.cognitiveservices.speech.VoiceInfo voice : voicesList) {
            try {
                // 获取必要字段
                String voiceId = voice.getShortName();
                String voiceName = voice.getLocalName() != null ? voice.getLocalName() : voice.getName();
                String locale = voice.getLocale();

                // 如果关键字段缺失，跳过此语音
                if (voiceId == null || voiceName == null) {
                    log.warn("[{}]语音数据缺少关键字段，跳过：name={}, shortName={}",
                            methodName, voice.getName(), voice.getShortName());
                    continue;
                }

                // 构建描述信息
                StringBuilder description = new StringBuilder();
                description.append("语言: ").append(locale);
                description.append(", 类型: ").append(voice.getVoiceType().toString());
                if (voice.getStyleList() != null && !voice.getStyleList().isEmpty()) {
                    description.append(", 支持风格: ").append(String.join(", ", voice.getStyleList()));
                }

                VoiceListResponseVO.VoiceInfo voiceInfo = VoiceListResponseVO.VoiceInfo.builder()
                        .voiceId(voiceId)
                        .voiceName(voiceName)
                        .description(description.toString())
                        .voiceType(voice.getVoiceType().toString())
                        .language(locale)
                        .gender(voice.getGender().toString())
                        .build();

                voices.add(voiceInfo);

            } catch (Exception e) {
                log.warn("[{}]处理单个语音数据失败：{}", methodName, e.getMessage());
            }
        }

        log.info("[{}]成功处理{}个语音", methodName, voices.size());

        return VoiceListResponseVO.builder()
                .provider("MICROSOFT")
                .voices(voices)
                .build();
    }

    /**
     * 处理语音列表数据（JSON版本，保留兼容性）
     * @param voicesList 语音列表JSON对象
     * @return 处理后的语音列表响应
     */
    private VoiceListResponseVO processVoicesList(List<JSONObject> voicesList) {
        String methodName = "processVoicesList";
        List<VoiceListResponseVO.VoiceInfo> voices = new ArrayList<>();
        
        log.info("[{}]处理语音列表，共{}个语音", methodName, voicesList.size());
        
        for (JSONObject voice : voicesList) {
            try {
                // 打印单个语音数据
                log.debug("[{}]处理语音数据：{}", methodName, voice.toJSONString());
                
                // 获取必要字段
                String voiceId = voice.getString("shortName");
                if (voiceId == null) {
                    voiceId = voice.getString("Name"); // 尝试替代字段
                }
                
                String voiceName = voice.getString("displayName");
                if (voiceName == null) {
                    voiceName = voice.getString("DisplayName"); // 尝试替代字段
                }
                
                String locale = voice.getString("locale");
                if (locale == null) {
                    locale = voice.getString("Locale"); // 尝试替代字段
                }
                
                // 如果关键字段缺失，跳过此语音
                if (voiceId == null || voiceName == null) {
                    log.warn("[{}]语音数据缺少关键字段，跳过：{}", methodName, voice.toJSONString());
                    continue;
                }
                
                VoiceListResponseVO.VoiceInfo voiceInfo = VoiceListResponseVO.VoiceInfo.builder()
                        .voiceId(voiceId)
                        .voiceName(voiceName)
                        .description(voice.getString("description"))
                        .voiceType(voice.getString("voiceType"))
                        .language(locale)
                        .gender(voice.getString("gender"))
                        .build();
                
                voices.add(voiceInfo);
            } catch (Exception e) {
                log.warn("[{}]处理单个语音数据失败：{}", methodName, e.getMessage());
                // 继续处理下一个语音
            }
        }
        
        log.info("[{}]成功处理{}个语音", methodName, voices.size());
        
        // 构建返回对象
        return VoiceListResponseVO.builder()
                .provider("MICROSOFT")
                .voices(voices)
                .build();
    }

    @Override
    public Result<TextToSpeechFileResponseDTO> textToSpeech(TextToSpeechFileRequestDTO requestDTO, String userId) {
        String methodName = "textToSpeech";
        log.info("[{}]开始处理文本转语音请求，userId={}", methodName, userId);
        
        try {
            // 验证Azure配置
            if (azureSubscriptionKey == null || azureRegion == null ||
                azureSubscriptionKey.trim().isEmpty() || azureRegion.trim().isEmpty()) {
                log.error("[{}]Azure语音服务配置无效", methodName);
                return Result.ERROR("Azure语音服务配置无效");
            }
            // 创建Azure API请求参数
            TextToSpeechRequestBO azureRequest = new TextToSpeechRequestBO();
            azureRequest.setText(requestDTO.getText());
            azureRequest.setVoiceName(requestDTO.getVoiceName());
            azureRequest.setLanguage(requestDTO.getLanguage());
            azureRequest.setOutputFormat(requestDTO.getOutputFormat());
            azureRequest.setUseSsml(requestDTO.isUseSsml());
            if (requestDTO.getRate() != null) {
                azureRequest.setRate(requestDTO.getRate());
            }
            if (requestDTO.getPitch() != null) {
                azureRequest.setPitch(requestDTO.getPitch());
            }
            String fileName = "azure-" + userId + UUID.randomUUID().toString();
            Result<TextToSpeechResponseBO> azureResult = 
                    AzureAudioApiUtil.textToSpeech(azureRequest, fileName, azureSubscriptionKey, azureRegion, userId);
            
            if (!azureResult.isSuccess() || azureResult.getData() == null) {
                log.error("[{}]文本转语音失败：{}", methodName, azureResult.getMessage());
                return Result.ERROR("文本转语音失败：" + azureResult.getMessage());
            }
            TextToSpeechResponseBO azureResponse = azureResult.getData();
            TextToSpeechFileResponseDTO responseDTO = TextToSpeechFileResponseDTO.builder()
                    .status(azureResponse.getStatus())
                    .url(azureResponse.getUrl())
                    .duration(azureResponse.getDuration())
                    .format(azureResponse.getFormat())
                    .build();
            log.info("[{}]文本转语音成功，OSS URL={}", methodName, responseDTO.getUrl());
            return Result.SUCCESS("文本转语音成功", responseDTO);
        } catch (Exception e) {
            log.error("[{}]文本转语音处理异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("文本转语音处理异常：" + e.getMessage());
        }
    }

    /**
     * 音频生成（同步）
     * 支持多服务商的同步音频生成，提供即时响应
     *
     * 正确的任务生命周期管理：
     * 1. 首先创建任务记录（状态：PROCESSING）
     * 2. 执行音频生成操作
     * 3. 根据结果更新任务状态（SUCCESS/FAILED）
     *
     * @param requestDTO 音频生成请求参数，包含服务商信息和TTS参数
     * @param userId 用户ID，用于权限验证、限流控制和扣费处理
     * @return 包含音频URL、时长、文件大小等信息的同步响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<AudioGenerationResponseVO> generateAudioSync(AudioGenerationRequestDTO requestDTO, String userId) {
        String methodName = "generateAudioSync";
        log.info("[{}]开始同步音频生成，userId={}, provider={}", methodName, userId,
                requestDTO != null ? requestDTO.getProvider() : "null");

        String taskId = null;
        DigitalAudioTaskPO taskPO = null;

        try {
            // 1. 参数验证
            if (requestDTO == null || userId == null || userId.trim().isEmpty()) {
                return Result.ERROR("请求参数不能为空");
            }

            if (requestDTO.getTtsParams() == null || requestDTO.getTtsParams().getText() == null
                || requestDTO.getTtsParams().getText().trim().isEmpty()) {
                return Result.ERROR("文本内容不能为空");
            }

            // 2. 文本长度检查
            String text = requestDTO.getTtsParams().getText();
            if (text.length() > syncAudioConfig.getMaxTextLength()) {
                return Result.ERROR("文本长度超过限制，最大支持" + syncAudioConfig.getMaxTextLength() + "字符");
            }

            // 3. 获取音频处理器
            String provider = requestDTO.getProvider();
            if (provider == null || provider.trim().isEmpty()) {
                return Result.ERROR("服务商标识不能为空");
            }

            Optional<AudioProviderProcessor> processorOpt = processorFactory.getProcessor(provider);
            if (!processorOpt.isPresent()) {
                log.error("[{}]不支持的服务商，userId={}, provider={}", methodName, userId, provider);
                return Result.ERROR("不支持的服务商：" + provider);
            }

            // 4. 创建任务记录（初始状态为PROCESSING）
            taskId = generateTaskId();
            taskPO = createInitialTaskRecord(requestDTO, userId, taskId);

            Result<String> createResult = digitalAudioTaskService.createTask(taskPO);
            if (!createResult.isSuccess()) {
                log.error("[{}]创建任务记录失败，userId={}, taskId={}, error={}",
                        methodName, userId, taskId, createResult.getMessage());
                return Result.ERROR("创建任务记录失败：" + createResult.getMessage());
            }

            log.info("[{}]任务记录创建成功，开始音频生成，userId={}, taskId={}, provider={}",
                    methodName, userId, taskId, provider);

            // 5. 执行音频生成
            AudioProviderProcessor processor = processorOpt.get();
            Result<AudioGenerationResponseVO> processorResult = processor.processRequest(requestDTO, userId);

            if (!processorResult.isSuccess() || processorResult.getData() == null) {
                // 音频生成失败，更新任务状态为FAILED
                updateTaskStatusToFailed(taskId, processorResult.getMessage());
                log.error("[{}]音频生成失败，userId={}, taskId={}, error={}",
                        methodName, userId, taskId, processorResult.getMessage());
                return Result.ERROR("音频生成失败：" + processorResult.getMessage());
            }

            AudioGenerationResponseVO responseDTO = processorResult.getData();

            // 6. 音频生成成功，更新任务状态为SUCCESS
            updateTaskStatusToSuccess(taskId, responseDTO);

            log.info("[{}]同步音频生成成功，userId={}, taskId={}, audioUrl={}, duration={}ms",
                methodName, userId, taskId, responseDTO.getAudioUrl(), responseDTO.getDurationMs());

            return Result.SUCCESS("同步音频生成成功", responseDTO);

        } catch (Exception e) {
            // 异常情况下，更新任务状态为FAILED
            if (taskId != null) {
                updateTaskStatusToFailed(taskId, "系统异常：" + e.getMessage());
            }
            log.error("[{}]同步音频生成异常，userId={}, taskId={}, error={}",
                    methodName, userId, taskId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试：" + e.getMessage());
        }
    }

    /**
     * 转换AudioGenerationRequestDTO为DigitalAudioGenerationDTO
     *
     * @param requestDTO 请求DTO
     * @return 转换后的DigitalAudioGenerationDTO
     */
    private DigitalAudioGenerationDTO convertToAudioGenerationDTO(AudioGenerationRequestDTO requestDTO) {
        TtsParams ttsParams = requestDTO.getTtsParams();

        // 构建VoiceSetting
        DigitalAudioGenerationDTO.VoiceSetting voiceSetting = DigitalAudioGenerationDTO.VoiceSetting.builder()
                .speed(ttsParams.getSpeed() != null ? ttsParams.getSpeed() : 1.0f)
                .vol(ttsParams.getExtraParam("vol", 1.0f))
                .pitch(ttsParams.getExtraParam("pitch", 0))
                .voiceId(ttsParams.getVoiceId())
                .emotion(ttsParams.getExtraParam("emotion", "neutral"))
                .latexRead(ttsParams.getExtraParam("latexRead", false))
                .build();

        // 构建AudioSetting
        DigitalAudioGenerationDTO.AudioSetting audioSetting = DigitalAudioGenerationDTO.AudioSetting.builder()
                .sampleRate(ttsParams.getExtraParam("sampleRate", 32000))
                .bitrate(ttsParams.getExtraParam("bitRate", 128000))
                .format(ttsParams.getExtraParam("audioFormat", "mp3"))
                .channel(1)
                .build();

        // 构建主DTO
        return DigitalAudioGenerationDTO.builder()
                .model(ttsParams.getExtraParam("model", "speech-01-turbo"))
                .text(ttsParams.getText())
                .voiceSetting(voiceSetting)
                .audioSetting(audioSetting)
                .build();
    }

    /**
     * 转换DigitalAudioVO为AudioGenerationResponseDTO
     *
     * @param audioVO 音频VO
     * @param taskId 任务ID（可选）
     * @return 转换后的响应DTO
     */
    private AudioGenerationResponseDTO convertToResponseDTO(DigitalAudioVO audioVO, String taskId) {
        return AudioGenerationResponseDTO.builder()
                .audioUrl(audioVO.getAudioUrl())
                .durationMs(audioVO.getAudioLength() != null ? audioVO.getAudioLength().intValue() : null)
                .taskId(taskId)
                .createdTime(new java.util.Date())
                .provider(audioVO.getProvider())
                .audioFormat(audioVO.getAudioFormat())
                .audioName(audioVO.getAudioName())
                .build();
    }

    /**
     * 可选创建任务记录
     *
     * @param requestDTO 请求DTO
     * @param userId 用户ID
     * @param audioVO 音频VO
     * @return 任务ID
     */
    /**
     * 从AudioGenerationResponseDTO创建任务记录
     *
     * @param requestDTO 请求DTO
     * @param userId 用户ID
     * @param responseDTO 响应DTO
     * @return 任务ID
     */
    private String createOptionalTaskRecordFromResponse(AudioGenerationRequestDTO requestDTO, String userId, AudioGenerationResponseVO responseDTO) {
        try {
            String taskId = "sync_" + System.currentTimeMillis() + "_" + java.util.UUID.randomUUID().toString().substring(0, 8);

            // 序列化请求参数
            String requestParamsJson = null;
            try {
                requestParamsJson = objectMapper.writeValueAsString(requestDTO.getTtsParams());
            } catch (JsonProcessingException e) {
                log.warn("序列化请求参数失败，taskId={}, error={}", taskId, e.getMessage());
                requestParamsJson = "{}"; // 使用空JSON作为默认值
            }

            // 序列化字幕信息
            String subtitlesJson = null;
            if (responseDTO.getSubtitles() != null && !responseDTO.getSubtitles().isEmpty()) {
                try {
                    // 先检查字幕对象的内容
                    log.debug("准备序列化字幕信息，taskId={}, subtitlesCount={}, firstSubtitle={}",
                            taskId, responseDTO.getSubtitles().size(), responseDTO.getSubtitles().get(0));

                    // 尝试使用新的ObjectMapper实例
                    ObjectMapper tempMapper = new ObjectMapper();
                    subtitlesJson = tempMapper.writeValueAsString(responseDTO.getSubtitles());

                    if (subtitlesJson != null && !subtitlesJson.trim().isEmpty() && !"null".equals(subtitlesJson)) {
                        log.debug("字幕信息序列化成功，taskId={}, subtitlesJson={}", taskId, subtitlesJson);
                    } else {
                        log.warn("字幕信息序列化结果为空或null，taskId={}, result={}", taskId, subtitlesJson);
                        subtitlesJson = null; // 确保设为null
                    }
                } catch (JsonProcessingException e) {
                    log.warn("序列化字幕信息失败，taskId={}, error={}", taskId, e.getMessage());
                    // 字幕序列化失败时设为null，不影响主流程
                }
            } else {
                log.debug("无字幕信息需要序列化，taskId={}", taskId);
            }

            DigitalAudioTaskPO taskPO = DigitalAudioTaskPO.builder()
                    .taskId(taskId)
                    .userId(userId)
                    .taskName(requestDTO.getTaskName() != null ? requestDTO.getTaskName() : "同步音频生成")
                    .provider(requestDTO.getProvider())
                    .status(2) // 已完成
                    .generatedAudioUrl(responseDTO.getAudioUrl())
                    .durationMs(responseDTO.getDurationMs())
                    .requestParamsJson(requestParamsJson)
                    .subtitlesJson(subtitlesJson)
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .isDeleted(false)
                    .build();

            digitalAudioTaskService.save(taskPO);

            // 记录任务创建成功信息
            if (subtitlesJson != null) {
                log.info("创建同步音频任务记录成功，taskId={}, provider={}, duration={}ms, subtitles={}条, subtitlesStored=true",
                        taskId, requestDTO.getProvider(), responseDTO.getDurationMs(), responseDTO.getSubtitles().size());
            } else {
                log.info("创建同步音频任务记录成功，taskId={}, provider={}, duration={}ms, subtitlesStored=false",
                        taskId, requestDTO.getProvider(), responseDTO.getDurationMs());
            }

            return taskId;

        } catch (Exception e) {
            log.warn("创建任务记录失败，但不影响主流程：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 生成任务ID
     *
     * @return 任务ID
     */
    private String generateTaskId() {
        return "sync_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 创建初始任务记录
     *
     * @param requestDTO 请求DTO
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 任务PO对象
     */
    private DigitalAudioTaskPO createInitialTaskRecord(AudioGenerationRequestDTO requestDTO, String userId, String taskId) {
        try {
            // 序列化请求参数
            String requestParamsJson = objectMapper.writeValueAsString(requestDTO.getTtsParams());

            return DigitalAudioTaskPO.builder()
                    .taskId(taskId)
                    .userId(userId)
                    .taskName(requestDTO.getTaskName() != null ? requestDTO.getTaskName() : "同步音频生成")
                    .provider(requestDTO.getProvider())
                    .status(AudioTaskStatusEnum.IN_PROGRESS.getValue()) // 初始状态为进行中
                    .requestParamsJson(requestParamsJson)
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .isDeleted(false)
                    .build();
        } catch (JsonProcessingException e) {
            log.warn("序列化请求参数失败，taskId={}, error={}", taskId, e.getMessage());
            // 即使序列化失败，也要创建任务记录
            return DigitalAudioTaskPO.builder()
                    .taskId(taskId)
                    .userId(userId)
                    .taskName(requestDTO.getTaskName() != null ? requestDTO.getTaskName() : "同步音频生成")
                    .provider(requestDTO.getProvider())
                    .status(AudioTaskStatusEnum.IN_PROGRESS.getValue())
                    .requestParamsJson("{}")
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .isDeleted(false)
                    .build();
        }
    }

    /**
     * 更新任务状态为失败
     *
     * @param taskId 任务ID
     * @param errorMsg 错误信息
     */
    private void updateTaskStatusToFailed(String taskId, String errorMsg) {
        try {
            boolean success = digitalAudioTaskService.updateTaskStatus(taskId, AudioTaskStatusEnum.FAILED.getValue(), errorMsg);
            if (success) {
                log.info("任务状态更新为失败，taskId={}, errorMsg={}", taskId, errorMsg);
            } else {
                log.warn("任务状态更新为失败时返回false，taskId={}, errorMsg={}", taskId, errorMsg);
            }
        } catch (Exception e) {
            log.error("更新任务状态为失败时发生异常，taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新任务状态为成功
     *
     * @param taskId 任务ID
     * @param responseDTO 响应DTO
     */
    private void updateTaskStatusToSuccess(String taskId, AudioGenerationResponseVO responseDTO) {
        try {
            // 首先使用completeTask方法更新基本信息
            boolean success = digitalAudioTaskService.completeTask(taskId, responseDTO.getAudioUrl(), responseDTO.getDurationMs());

            if (success) {
                log.info("任务状态更新为成功，taskId={}, audioUrl={}, duration={}ms",
                        taskId, responseDTO.getAudioUrl(), responseDTO.getDurationMs());

                // 如果有字幕信息，需要额外更新字幕字段
                if (responseDTO.getSubtitles() != null && !responseDTO.getSubtitles().isEmpty()) {
                    updateTaskSubtitles(taskId, responseDTO.getSubtitles());
                }
            } else {
                log.warn("任务状态更新为成功时返回false，taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新任务状态为成功时发生异常，taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新任务的字幕信息
     *
     * @param taskId 任务ID
     * @param subtitles 字幕列表
     */
    private void updateTaskSubtitles(String taskId, Object subtitles) {
        try {
            String subtitlesJson = objectMapper.writeValueAsString(subtitles);
            // 这里需要使用MyBatis-Plus的更新方法来更新字幕字段
            // 由于IDigitalAudioTaskService没有专门的更新字幕方法，我们直接使用底层的update方法
            DigitalAudioTaskPO updatePO = new DigitalAudioTaskPO();
            updatePO.setSubtitlesJson(subtitlesJson);
            updatePO.setUpdateTime(LocalDateTime.now());

            // 使用LambdaUpdateWrapper进行条件更新
            digitalAudioTaskService.update(updatePO,
                new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<DigitalAudioTaskPO>()
                    .eq(DigitalAudioTaskPO::getTaskId, taskId)
                    .eq(DigitalAudioTaskPO::getIsDeleted, false));

            log.debug("字幕信息更新成功，taskId={}, subtitlesCount={}", taskId,
                    subtitles instanceof java.util.List ? ((java.util.List<?>) subtitles).size() : "unknown");
        } catch (JsonProcessingException e) {
            log.warn("序列化字幕信息失败，taskId={}, error={}", taskId, e.getMessage());
        } catch (Exception e) {
            log.error("更新字幕信息时发生异常，taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }


}
