package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.po.*;
import com.nacos.entity.vo.DigitalAvatarDefaultVO;
import com.nacos.entity.vo.DigitalSystemAvatarVO;
import com.nacos.mapper.*;
import com.nacos.service.DigitalSystemAvatarService;
import com.nacos.result.Result;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalSystemAvatarServiceImpl extends ServiceImpl<DigitalSystemAvatarMapper, DigitalSystemAvatarPO>
        implements DigitalSystemAvatarService {

    // 默认系统数字人ID
    private static final String DEFAULT_SYSTEM_AVATAR_ID = "1768841687347937283";
    // 默认音色ID
    private static final String DEFAULT_VOICE_ID = "presenter_female";

    private final DigitalVoiceStylesMapper voiceStylesMapper;
    private final DigitalGroupCategoryRelationMapper digitalGroupCategoryRelationMapper;
    private final DigitalAvatarCategoryMapper digitalAvatarCategoryMapper;
    private final DigitalVoiceCategoryRelationMapper digitalVoiceCategoryRelationMapper;
    private final DigitalVoiceCategoryMapper digitalVoiceCategoryMapper;
    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final DigitalVoiceUserCloneMapper digitalVoiceUserCloneMapper;

    @Override
    public Result<List<DigitalSystemAvatarVO>> listSystemAvatars() {
        try {
            LambdaQueryWrapper<DigitalSystemAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                    .orderByAsc(DigitalSystemAvatarPO::getGroupId);
            List<DigitalSystemAvatarPO> list = this.list(wrapper);
            List<DigitalSystemAvatarVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取系统数字人列表失败", e);
            return Result.ERROR("获取系统数字人列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> addSystemAvatar(DigitalSystemAvatarVO avatarVO) {
        try {
            if (!StringUtils.hasText(avatarVO.getAvatarName())) {
                return Result.ERROR("数字人名称不能为空");
            }
            if (avatarVO.getGroupId() == null) {
                return Result.ERROR("所属组ID不能为空");
            }

            // 检查数字人ID是否已存在
            LambdaQueryWrapper<DigitalSystemAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalSystemAvatarPO::getAvatarId, avatarVO.getAvatarId())
                    .eq(DigitalSystemAvatarPO::getIsDeleted, 0);
            if (this.count(wrapper) > 0) {
                return Result.ERROR("数字人ID已存在");
            }

            DigitalSystemAvatarPO po = new DigitalSystemAvatarPO();
            BeanUtils.copyProperties(avatarVO, po);

            // 不需要手动设置这些值，会由MyMetaObjectHandler自动填充
            // avatarId会自动生成
            // status会自动设置默认值1
            // createdTime会自动填充
            // updateTime会自动填充
            // isDeleted会自动设置默认值0

            boolean success = this.save(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("添加系统数字人失败");
        } catch (Exception e) {
            log.error("添加系统数字人失败", e);
            return Result.ERROR("添加系统数字人失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateSystemAvatar(DigitalSystemAvatarVO avatarVO) {
        try {
            if (avatarVO.getId() == null) {
                return Result.ERROR("数字人ID不能为空");
            }
            if (!StringUtils.hasText(avatarVO.getAvatarName())) {
                return Result.ERROR("数字人名称不能为空");
            }

            // 检查数字人是否存在
            DigitalSystemAvatarPO existingAvatar = this.getById(avatarVO.getId());
            if (existingAvatar == null || existingAvatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }

            // 检查数字人ID是否重复（排除自身）
            if (StringUtils.hasText(avatarVO.getAvatarId())) {
                LambdaQueryWrapper<DigitalSystemAvatarPO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DigitalSystemAvatarPO::getAvatarId, avatarVO.getAvatarId())
                        .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                        .ne(DigitalSystemAvatarPO::getId, avatarVO.getId());
                if (this.count(wrapper) > 0) {
                    return Result.ERROR("数字人ID已存在");
                }
            }

            DigitalSystemAvatarPO po = new DigitalSystemAvatarPO();
            BeanUtils.copyProperties(avatarVO, po);
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新系统数字人失败");
        } catch (Exception e) {
            log.error("更新系统数字人失败", e);
            return Result.ERROR("更新系统数字人失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteSystemAvatar(Long id) {
        try {
            if (id == null) {
                return Result.ERROR("数字人ID不能为空");
            }

            DigitalSystemAvatarPO avatar = this.getById(id);
            if (avatar == null || avatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }

            // 逻辑删除，会自动设置isDeleted=1
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.removeById(id);
            return success ? Result.SUCCESS(true) : Result.ERROR("删除系统数字人失败");
        } catch (Exception e) {
            log.error("删除系统数字人失败", e);
            return Result.ERROR("删除系统数字人失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateStatus(Long id, Integer status) {
        try {
            if (id == null) {
                return Result.ERROR("数字人ID不能为空");
            }
            if (status == null || (status != 0 && status != 1)) {
                return Result.ERROR("状态值无效");
            }

            DigitalSystemAvatarPO avatar = this.getById(id);
            if (avatar == null || avatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }

            DigitalSystemAvatarPO po = new DigitalSystemAvatarPO();
            po.setId(id);
            po.setStatus(status);
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新状态失败");
        } catch (Exception e) {
            log.error("更新系统数字人状态失败", e);
            return Result.ERROR("更新系统数字人状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<DigitalSystemAvatarVO>> listSystemAvatarsByGroupId(String groupId) {
        try {
            if (groupId == null) {
                return Result.ERROR("组ID不能为空");
            }

            LambdaQueryWrapper<DigitalSystemAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalSystemAvatarPO::getGroupId, groupId)
                    .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                    .eq(DigitalSystemAvatarPO::getStatus, 1);

            List<DigitalSystemAvatarPO> list = this.list(wrapper);
            List<DigitalSystemAvatarVO> voList = list.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取组系统数字人列表失败，组ID：{}", groupId, e);
            return Result.ERROR("获取组系统数字人列表失败");
        }
    }

    private DigitalSystemAvatarVO convertToVO(DigitalSystemAvatarPO po) {
        if (po == null) {
            return null;
        }
        DigitalSystemAvatarVO vo = new DigitalSystemAvatarVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    @Override
    public Result<DigitalAvatarDefaultVO> getDefaultAvatarAndVoice(String groupId) {
        try {
            DigitalSystemAvatarPO systemAvatar = null;
            DigitalUserAvatarPO userAvatar = null;

            // 根据groupId是否为空，确认使用默认数字人形象还是用户指定数字人形象
            if (!StringUtils.hasText(groupId)) {
                // groupId为空，使用系统默认数字人形象
                systemAvatar = baseMapper.selectOne(
                        new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                                .eq(DigitalSystemAvatarPO::getAvatarId, DEFAULT_SYSTEM_AVATAR_ID)
                                .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                                .last("limit 1"));
            } else {
                // groupId不为空，查询该组下最新的用户数字人形象
                userAvatar = digitalUserAvatarMapper.selectOne(
                        new LambdaQueryWrapper<DigitalUserAvatarPO>()
                                .eq(DigitalUserAvatarPO::getGroupId, groupId)
                                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                                .orderByDesc(DigitalUserAvatarPO::getCreatedTime)
                                .last("limit 1"));
            }

            if (systemAvatar == null && userAvatar == null) {
                log.warn("数字人形象不存在. GroupId: {}, DefaultSystemAvatarId: {}", groupId, DEFAULT_SYSTEM_AVATAR_ID);
                // 根据原始逻辑，如果按groupId找不到userAvatar，或者groupId为空时找不到systemAvatar，则抛出异常
                // 如果希望在groupId不为空但找不到userAvatar时回退到systemAvatar，则需要调整此逻辑
                throw new RuntimeException("数字人形象不存在");
            }

            DigitalAvatarDefaultVO resultVO = new DigitalAvatarDefaultVO();

            // 填充数字人形象信息
            if (systemAvatar != null) {
                populateAvatarDetails(resultVO, systemAvatar.getGroupId(), systemAvatar.getAvatarId(),
                        systemAvatar.getAvatarName(), systemAvatar.getCoverUrl());
            } else { // userAvatar 必然不为 null
                populateAvatarDetails(resultVO, userAvatar.getGroupId(), userAvatar.getAvatarId(),
                        userAvatar.getAvatarName(), userAvatar.getCoverUrl());
            }

            // 填充音色信息
            populateVoiceDetails(resultVO);

            return Result.SUCCESS("获取数字人形象与音色成功", resultVO);
        } catch (Exception e) {
            log.error("获取数字人形象与音色失败. GroupId: {}, Error: {}", groupId, e.getMessage(), e);
            return Result.ERROR("获取数字人形象与音色失败：" + e.getMessage());
        }
    }

    // 填充数字人形象信息
    private void populateAvatarDetails(DigitalAvatarDefaultVO resultVO, String avatarEntityGroupId, String avatarId,
                                       String avatarName, String coverUrl) {
        // 查询数字人组分类关联信息
        DigitalGroupCategoryRelationPO categoryRelationPo = digitalGroupCategoryRelationMapper.selectOne(
                new LambdaQueryWrapper<DigitalGroupCategoryRelationPO>()
                        .eq(DigitalGroupCategoryRelationPO::getGroupId, avatarEntityGroupId)
                        .last("limit 1"));
        // 如果数字人组分类关联信息不存在，则设置分类编码为0
        if (categoryRelationPo != null) {
            // 查询数字人组分类信息
            DigitalAvatarCategoryPO categoryPo = digitalAvatarCategoryMapper.selectOne(
                    new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
                            .eq(DigitalAvatarCategoryPO::getId, categoryRelationPo.getCategoryId())
                            .last("limit 1"));
            if (categoryPo == null) {
                log.error("数字人组分类信息不存在, CategoryId: {}", categoryRelationPo.getCategoryId());
                throw new RuntimeException("数字人形象配置错误：组分类信息缺失");
            }
            resultVO.setAvatarCategoryCode(categoryPo.getCategoryCode());
        } else {
            resultVO.setAvatarCategoryCode("0");
        }

        resultVO.setAvatarId(avatarId);
        resultVO.setAvatarName(avatarName);
        resultVO.setGroupId(avatarEntityGroupId); // 此为形象所属的组ID
        resultVO.setCoverUrl(coverUrl);
    }

    // 填充音色信息
    private void populateVoiceDetails(DigitalAvatarDefaultVO resultVO) {

        // 先根据数字人信息查询是否有音色信息，如果有，则直接返回
        DigitalVoiceUserClonePO voiceUserClonePO = digitalVoiceUserCloneMapper.selectOne(
                new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                        .eq(DigitalVoiceUserClonePO::getAvatarId, resultVO.getAvatarId())
                        .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                        .last("limit 1"));
        if (voiceUserClonePO != null) {
            resultVO.setVoiceId(voiceUserClonePO.getVoiceId());
            resultVO.setProvider(voiceUserClonePO.getProvider());
            resultVO.setVoiceName(voiceUserClonePO.getVoiceName());
            resultVO.setVoiceCategoryCode("0");
        } else {
            // 如果没有音色信息，则使用默认音色
            // 查询获取数字人音色
            DigitalVoiceStylesPO voiceStylePO = voiceStylesMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceId, DEFAULT_VOICE_ID)
                            .eq(DigitalVoiceStylesPO::getIsDeleted, 0)
                            .last("limit 1"));
            if (voiceStylePO == null) {
                log.error("默认音色不存在, VoiceId: {}", DEFAULT_VOICE_ID);
                throw new RuntimeException("音色配置错误：默认音色不存在");
            }

            // 查询音色分类关联信息
            DigitalVoiceCategoryRelationPO voiceCategoryRelationPo = digitalVoiceCategoryRelationMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVoiceCategoryRelationPO>()
                            .eq(DigitalVoiceCategoryRelationPO::getVoiceId, voiceStylePO.getVoiceId())
                            .last("limit 1"));
            if (voiceCategoryRelationPo == null) {
                log.error("音色分类关联信息不存在, VoiceId: {}", voiceStylePO.getVoiceId());
                throw new RuntimeException("音色配置错误：分类关联缺失");
            }

            // 查询音色分类信息
            DigitalVoiceCategoryPO voiceCategoryPo = digitalVoiceCategoryMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVoiceCategoryPO>()
                            .eq(DigitalVoiceCategoryPO::getId, voiceCategoryRelationPo.getCategoryId())
                            .last("limit 1"));
            if (voiceCategoryPo == null) {
                log.error("音色分类信息不存在, CategoryId: {}", voiceCategoryRelationPo.getCategoryId());
                throw new RuntimeException("音色配置错误：分类信息缺失");
            }

            resultVO.setVoiceId(voiceStylePO.getVoiceId());
            resultVO.setProvider(voiceStylePO.getProvider());
            resultVO.setVoiceName(voiceStylePO.getVoiceName());
            resultVO.setVoiceCategoryCode(voiceCategoryPo.getCategoryCode());
        }

    }
}