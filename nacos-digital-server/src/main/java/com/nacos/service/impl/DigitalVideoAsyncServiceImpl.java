package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.entity.bo.SubTaskUpdateBO;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalAudioGenerationDTO;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.enums.VideoTaskItemStatusEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.mapper.DigitalVideoTaskItemMapper;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.model.TXDigital.model.TXVideoTranslateJobRequestBO;
import com.nacos.model.TXDigital.model.TXVideoTranslateJobResponseBO;
import com.nacos.model.TXDigital.model.TXVideoTranslateJobQueryResponseBO;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.utils.DigitalFileUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import com.nacos.config.TencentCloudConfig;
import org.apache.commons.lang3.StringUtils;
import com.business.message.mq.BRedisServiceUtil;
import com.business.message.BMessageSendEnum;
import com.nacos.utils.MessageSendUtil;

import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * 数字人视频异步服务实现类
 * 负责处理视频生成任务的异步执行，包括音频生成、视频合成等流程
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVideoAsyncServiceImpl implements DigitalVideoAsyncService {

    @Autowired
    private TencentCloudConfig tencentCloudConfig;

    // 数据访问层依赖
    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVideoTaskItemMapper digitalVideoTaskItemMapper;
    
    // 服务层依赖
    private final TXDigitalApisUtil txDigitalApisUtil;
    private final DigitalAudioService digitalAudioService;
    private final DigitalVideoTaskService digitalVideoTaskService;
    // 添加通知服务依赖
    private final DigitalNotificationService digitalNotificationService;
    
    // 轮询间隔和最大重试次数配置
    private static final int POLL_INTERVAL = 10000; // 轮询间隔：10秒
    private static final int MAX_RETRIES = 180; // 最大重试次数：180次（约30分钟）

    @Resource
    FeiyongService feiyongService;

    /**
     * 处理视频任务的主入口方法
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param taskItems 任务项列表
     */
    @Async("videoTaskExecutor")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processVideoTasks(String taskId, String userId, List<DigitalVideoTaskItemPO> taskItems) {
        String methodName = "processVideoTasks";
        log.info("[{}] 开始处理视频任务，taskId：{}, userId: {}", methodName, taskId, userId);
        long logId = IdWorker.getId();
        boolean koufeiResult = false;
        int multiplier = 1;
        try {
            // 1. 验证任务状态
            DigitalVideoTaskPO mainTask = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            if (mainTask == null) {
                log.error("[{}] 任务不存在或状态不正确：taskId={}", methodName, taskId);
                return;
            }

            // 2. 处理每个子任务
            boolean hasError = false;
            for (DigitalVideoTaskItemPO subTask : taskItems) {
                try {
                    // 检查子任务状态
                    if (subTask.getStatus() != VideoTaskItemStatusEnum.PENDING.getValue()) {
                        log.error("[{}] 子任务状态不正确：subTaskId={}，子任务状态：{}", methodName, subTask.getSubTaskId(), VideoTaskItemStatusEnum.getDesc(subTask.getStatus()));
                        continue;
                    }
                    // 处理子任务
                    processSubTask(subTask, userId);
                } catch (Exception e) {
                    String errorMsg = "处理子任务异常：" + e.getMessage();
                    log.error("[{}] {}：subTaskId={}", methodName, errorMsg, subTask.getSubTaskId(), e);
                    
                    // 更新子任务状态
                    digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                        .subTaskId(subTask.getSubTaskId())
                        .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    
                    // 创建失败通知
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        subTask.getSubTaskId(),
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue(),
                        errorMsg
                    );
                    
                    hasError = true;
                }
            }

            // 3. 检查并更新主任务
            if (!hasError) {
                log.info("[{}] 所有子任务处理完成，更新主任务状态为视频生成成功：taskId={}", methodName, taskId);
                
                // 更新主任务状态
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(taskId)
                    .status(VideoTaskStatusEnum.VIDEO_SUCCESS.getValue())
                    .build()
                );

                // 创建成功通知
                digitalNotificationService.createNotification(
                    Long.valueOf(userId),
                    taskId,
                    DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue(),
                    "视频生成成功，请查看"
                );
                pushTaskStatus(userId, taskId, VideoTaskStatusEnum.VIDEO_SUCCESS.getValue(), VideoTaskStatusEnum.VIDEO_SUCCESS.getDesc());
            }else {
                // 更新主任务状态
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(taskId)
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .build()
                );
                digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue(),
                        "视频生成失败"
                );
                pushTaskStatus(userId, taskId, VideoTaskStatusEnum.FAILED.getValue(), "联系客服处理");
                return;
            }
            // 4. 检查并合并视频
            checkAndMergeVideos(mainTask);

            //4.1扣费
            multiplier = countAudioLength(taskId);//计算音频长度
            koufeiResult = feiyongService.koufei(Long.valueOf(userId),logId,"萤火虫生成数字人视频扣费", DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey(),multiplier);
            pushTaskStatus(userId, taskId, VideoTaskStatusEnum.SUCCESS.getValue(), VideoTaskStatusEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            if (koufeiResult){
                feiyongService.tuifei(Long.valueOf(userId),logId,"萤火虫生成数字人视频退费",DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey(),multiplier);
            }
            String errorMsg = "处理视频任务异常：" + e.getMessage();
            log.error("[{}] {}：taskId={}", methodName, errorMsg, taskId, e);
            
            // 更新主任务状态
            digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                .taskId(taskId)
                .status(VideoTaskStatusEnum.FAILED.getValue())
                .errorMsg(errorMsg)
                .build()
            );
            
            // 创建失败通知
            digitalNotificationService.createNotification(
                Long.valueOf(userId),
                taskId,
                DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue(),
                errorMsg
            );
            // 推送任务失败通知
            pushTaskStatus(userId, taskId, VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
        }
    }

    @Override
    public Result<DigitalAudioVO> submitAudioTask(DigitalVideoTaskItemPO taskItemPO, String userId) {
        String methodName = "submitAudioTask";
        try {
            String[] apiKeys = getApiKey().split(",");
            if (apiKeys.length != 2) {
                log.error("[{}] API密钥格式错误", methodName);
                return Result.ERROR("API密钥格式错误");
            }
            // 构建音频生成参数
            DigitalAudioGenerationDTO digitalAudioGenerationDTO = new DigitalAudioGenerationDTO();
            digitalAudioGenerationDTO.setText(taskItemPO.getTextContent());
            digitalAudioGenerationDTO.setVoiceSetting(
                    DigitalAudioGenerationDTO.VoiceSetting.builder()
                            .voiceId(taskItemPO.getVoiceId())
                            .speed(taskItemPO.getSpeed())
                            .build()
            );
            // 调用语音生成
            Result<DigitalAudioVO> audioResponse = digitalAudioService.voiceGeneration(digitalAudioGenerationDTO, userId);
            if (audioResponse.isSuccess()) {
                log.info("[{}] 音频生成成功，audioUrl：{}", methodName, audioResponse.getData().getAudioUrl());
                return audioResponse;
            } else {
                log.error("[{}] 音频生成失败，errorMsg：{}", methodName, audioResponse.getMessage());
                return Result.ERROR(audioResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("[{}] 提交音频生成任务异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR(e.getMessage());
        }
    }

    /**
     * 计算音频时长
     * @param taskId
     * @return
     */
    public int countAudioLength(String taskId) {
        List<DigitalVideoTaskItemPO> taskItems = digitalVideoTaskItemMapper.selectList(
                new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                        .eq(DigitalVideoTaskItemPO::getTaskId, taskId)
                        .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
        );
        Long length = taskItems.stream().mapToLong(DigitalVideoTaskItemPO::getAudioLength).sum();
        // length=length/1000/60;
        return (int) Math.ceil(length / 1000.0 / 60);
    }

    /**
     * 处理单个子任务
     * @param subTask 子任务
     * @param userId 用户ID
     */
    private void processSubTask(DigitalVideoTaskItemPO subTask, String userId) {
        // 创建处理中通知
        digitalNotificationService.createNotification(
            Long.valueOf(userId),
            subTask.getSubTaskId(),
            DigitalNotificationEnum.VIDEO_TASK_PROCESSING.getValue(),
            "视频正在生成中，请耐心等待"
        );

        // 更新子任务状态为音频生成中
        digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
            .subTaskId(subTask.getSubTaskId())
            .status(VideoTaskItemStatusEnum.AUDIO_GENERATING.getValue())
            .build()
        );
        // 1. 生成音频
        Result<DigitalAudioVO> audioResponse = submitAudioTask(subTask, userId);
        if (!isAudioResponseSuccess(audioResponse)) {
            digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                .subTaskId(subTask.getSubTaskId())
                .status(VideoTaskItemStatusEnum.AUDIO_FAILED.getValue())
                .errorMsg("音频生成失败")
                .build()
            );
            throw new RuntimeException("音频生成失败");
        }

        // 保存音频URL
        digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
            .subTaskId(subTask.getSubTaskId())
            .status(VideoTaskItemStatusEnum.AUDIO_SUCCESS.getValue())
            .audioUrl(audioResponse.getData().getAudioUrl())
            .audioLength(audioResponse.getData().getAudioLength())
            .build()
        );

        // 2. 提交视频生成任务
        TXVideoTranslateJobResponseBO videoResponse = submitVideoTask(subTask, subTask.getSrcVideoUrl(), audioResponse.getData().getAudioUrl());
        if (!isVideoResponseSuccess(videoResponse)) {
            digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                .subTaskId(subTask.getSubTaskId())
                .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                .errorMsg("提交视频生成任务失败"+videoResponse.getResponse().getError().getMessage())
                .build()
            );
            throw new RuntimeException("视频生成任务失败"+videoResponse.getResponse().getError().getMessage());
        }

        // 3. 更新状态
        String jobId = videoResponse.getResponse().getJobId();
        digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
            .subTaskId(subTask.getSubTaskId())
            .status(VideoTaskItemStatusEnum.VIDEO_GENERATING.getValue())
            .apiJobId(jobId)
            .build()
        );

        // 推送状态视频生成中通知
        pushTaskStatus(userId, subTask.getSubTaskId(), VideoTaskItemStatusEnum.VIDEO_GENERATING.getValue(), null);
        
        // 4. 轮询检查视频任务状态
        pollVideoTaskStatus(jobId, subTask, userId);
    }

    /**
     * 轮询检查视频任务状态
     * @param jobId 腾讯云任务ID
     * @param subTask 子任务实体
     * @param userId 用户ID
     */
    private void pollVideoTaskStatus(String jobId, DigitalVideoTaskItemPO subTask, String userId) {
        String[] apiKeys = getApiKey().split(",");
        if (apiKeys.length != 2) {
            digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                .subTaskId(subTask.getSubTaskId())
                .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                .errorMsg("API密钥配置错误")
                .build()
            );
            throw new RuntimeException("系统内部错误：任务key配置错误");
        }

        int retryCount = 0;
        while (retryCount < MAX_RETRIES) {
            try {
                Thread.sleep(POLL_INTERVAL);
                
                TXVideoTranslateJobQueryResponseBO queryResult = txDigitalApisUtil.queryTXVideoTranslateJobStatus(
                    jobId, apiKeys[0], apiKeys[1]);
                    
                if (queryResult != null && queryResult.getResponse() != null) {
                    Integer jobStatus = queryResult.getResponse().getJobStatus();
                    if (handleJobStatus(jobStatus, queryResult, subTask, userId)) {
                        return;
                    }
                }
                retryCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("查询视频生成任务状态被中断", e);
            } catch (Exception e) {
                digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                    .errorMsg("查询视频生成任务状态异常：" + e.getMessage())
                    .build()
                );
                throw new RuntimeException("系统内部错误：视频生成任务状态异常");
            }
        }
        
        digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
            .subTaskId(subTask.getSubTaskId())
            .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
            .errorMsg("视频生成任务超时")
            .build()
        );
    }

    /**
     * 处理视频任务状态
     * @param jobStatus 任务状态码
     * 1：音频翻译中  
     * 2：音频翻译失败  
     * 3：音频翻译成功  
     * 4：音频结果待确认  
     * 5：音频结果已确认完毕  
     * 6：视频翻译中  
     * 7：视频翻译失败  
     * 8：视频翻译成功  
     * @param queryResult 查询结果
     * @param subTask 子任务实体
     * @param userId 用户ID
     * @return 是否完成处理（true-完成，false-继续轮询）
     */
    private boolean handleJobStatus(Integer jobStatus, TXVideoTranslateJobQueryResponseBO queryResult, 
            DigitalVideoTaskItemPO subTask, String userId) {
        switch (jobStatus) {
            case 8: // 成功
                try {
                    String videoUrl = queryResult.getResponse().getResultVideoUrl();
                    String ossUrl = saveVideoToOSS(videoUrl, userId, subTask.getSubTaskId());
                    
                    // 更新子任务状态
                    digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                        .subTaskId(subTask.getSubTaskId())
                        .status(VideoTaskItemStatusEnum.VIDEO_SUCCESS.getValue())
                        .videoUrl(ossUrl)
                        .build()
                    );
                    
                    // 创建成功通知
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        subTask.getSubTaskId(),
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue(),
                        "视频生成成功，请查看"
                    );
                    
                    return true;
                } catch (Exception e) {
                    log.error("保存视频到OSS失败：{}", e.getMessage(), e);
                    
                    // 更新子任务状态
                    digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                        .subTaskId(subTask.getSubTaskId())
                        .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                        .errorMsg("保存视频到OSS失败：" + e.getMessage())
                        .build()
                    );
                    
                    // 创建失败通知
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        subTask.getSubTaskId(),
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue(),
                        "保存视频到OSS失败：" + e.getMessage()
                    );
                    throw new RuntimeException("保存视频到OSS失败：" + e.getMessage());
                }
            case 7: // 视频翻译失败
                String errorMsg = queryResult.getResponse().getJobErrorMsg();
                
                // 更新子任务状态
                digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue())
                    .errorMsg(errorMsg)
                    .build()
                );
                
                // 创建失败通知
                digitalNotificationService.createNotification(
                    Long.valueOf(userId),
                    subTask.getSubTaskId(),
                    DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue(),
                    errorMsg
                );
                throw new RuntimeException("视频处理失败，状态7");
            case 2: // 音频翻译失败
                digitalVideoTaskService.updateSubTask(SubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoTaskItemStatusEnum.AUDIO_FAILED.getValue())
                    .errorMsg(queryResult.getResponse().getJobErrorMsg())
                    .build()
                );
                throw new RuntimeException("音频处理失败，状态2");
            default: // 处理中或其他状态
                return false;
        }
    }

    /**
     * 检查音频生成响应是否成功
     * @param response 音频生成响应
     * @return 是否成功
     */
    private boolean isAudioResponseSuccess(Result<DigitalAudioVO> response) {
        return response != null && response.isSuccess() 
            && response.getData() != null && response.getData().getAudioUrl() != null;
    }

    /**
     * 检查视频生成响应是否成功
     * @param response 视频生成响应
     * @return 是否成功
     */
    private boolean isVideoResponseSuccess(TXVideoTranslateJobResponseBO response) {
        return response != null && response.getResponse() != null 
            && response.getResponse().getJobId() != null;
    }

    /**
     * 提交视频生成任务到腾讯云
     * @param taskItemPO 任务项数据
     * @param videoUrl 源视频URL
     * @param audioUrl 音频URL
     * @return 视频生成响应
     */
    private TXVideoTranslateJobResponseBO submitVideoTask(DigitalVideoTaskItemPO taskItemPO, String videoUrl, String audioUrl) {
        String methodName = "submitVideoTask";
        try {
            String[] apiKeys = getApiKey().split(",");
            if (apiKeys.length != 2) {
                log.error("[{}] API密钥格式错误", methodName);
                return null;
            }
            
            // 构建视频生成请求参数
            TXVideoTranslateJobRequestBO requestBO = new TXVideoTranslateJobRequestBO();
            requestBO.setVideoUrl(videoUrl);
            requestBO.setAudioUrl(audioUrl);
            requestBO.setSrcLang(taskItemPO.getSrcLang());
            requestBO.setDstLang(taskItemPO.getDstLang());

            // 调用腾讯云接口生成视频
            return txDigitalApisUtil.submitTXVideoTranslateJob(requestBO, apiKeys[0], apiKeys[1]);

        } catch (Exception e) {
            log.error("[{}] 提交视频生成任务异常：{}", methodName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将视频保存到OSS
     * @param videoUrl 源视频URL
     * @param userId 用户ID
     * @param subTaskId 子任务ID
     * @return OSS访问URL
     * @throws RuntimeException 当保存失败时抛出
     */
    private String saveVideoToOSS(String videoUrl, String userId, String subTaskId) {
        String methodName = "saveVideoToOSS";
        try {
            String fileName = String.format("%s_%s.mp4", subTaskId, System.currentTimeMillis());
            String ossUrl = DigitalFileUtil.uploadDigitalResource(videoUrl, fileName, userId, null, 2, false);
            if (ossUrl == null) {
                throw new RuntimeException("OSS上传返回的URL为空");
            }
            return ossUrl;
        } catch (Exception e) {
            log.error("[{}] 保存视频到OSS失败：{}", methodName, e.getMessage(), e);
            throw new RuntimeException("保存视频到OSS失败：" + e.getMessage());
        }
    }

    /**
     * 检查任务完成,合并视频，截取封面，更新主任务
     */
    public Result<String> checkAndMergeVideos(DigitalVideoTaskPO mainTask) {
        String methodName = "checkAndMergeVideos";
        log.info("[{}] 开始检查并合并视频，taskId：{}", methodName, mainTask.getTaskId());
        boolean hasError = false;
        try {
            // 查询所有子任务
            List<DigitalVideoTaskItemPO> subTasks = digitalVideoTaskItemMapper.selectList(
                new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                    .eq(DigitalVideoTaskItemPO::getTaskId, mainTask.getTaskId())
                    .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
            );

            if (subTasks.isEmpty()) {
                log.error("[{}] 没有找到子任务，taskId：{}", methodName, mainTask.getTaskId());
                TaskStatusUpdateBO updateBO = TaskStatusUpdateBO.builder()
                    .taskId(mainTask.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .errorMsg("没有找到子任务")
                    .build();
                digitalVideoTaskService.updateTask(updateBO);
                return Result.ERROR("子任务不存在");
            }

            // 统计子任务状态
            List<DigitalVideoTaskItemPO> successTasks = new ArrayList<>();
            List<DigitalVideoTaskItemPO> failedTasks = new ArrayList<>();
            List<DigitalVideoTaskItemPO> processingTasks = new ArrayList<>();

            for (DigitalVideoTaskItemPO subTask : subTasks) {
                if (subTask.getStatus().equals(VideoTaskItemStatusEnum.VIDEO_SUCCESS.getValue())) {
                    successTasks.add(subTask);
                } else if (subTask.getStatus().equals(VideoTaskItemStatusEnum.VIDEO_FAILED.getValue()) ||
                        subTask.getStatus().equals(VideoTaskItemStatusEnum.AUDIO_FAILED.getValue())) {
                    failedTasks.add(subTask);
                } else {
                    processingTasks.add(subTask);
                }
            }

            int successCount = successTasks.size();
            int failedCount = failedTasks.size();
            int processingCount = processingTasks.size();

            log.info("[{}] 子任务状态统计：成功={}，失败={}，处理中={}",
                    methodName, successCount, failedCount, processingCount);

            // 如果还有处理中的任务，则不进行合并
            if (processingCount > 0 || failedCount > 0) {
                return Result.ERROR("还有处理中的子任务或失败的任务");
            }

            TaskStatusUpdateBO updateBO = TaskStatusUpdateBO.builder()
                    .taskId(mainTask.getTaskId())
                    .build();

//            // 如果所有任务都失败了
//            if (successTasks.isEmpty()) {
//                updateBO.setStatus(VideoTaskStatusEnum.FAILED.getValue());
//                updateBO.setErrorMsg("所有子任务都失败");
//                digitalVideoTaskService.updateTask(updateBO);
//                return;
//            }

            try {
                // 获取所有成功子任务的视频URL
                List<String> videoUrls = successTasks.stream()
                        .map(DigitalVideoTaskItemPO::getVideoUrl)
                        .collect(Collectors.toList());

                // 合并视频
                Result<String> mergedResult = mergeVideos(videoUrls, mainTask.getUserId(), mainTask.getTaskId());
                if (!mergedResult.isSuccess()) {
                    updateBO.setStatus(VideoTaskStatusEnum.FAILED.getValue());
                    updateBO.setErrorMsg(mergedResult.getMessage());
                    digitalVideoTaskService.updateTask(updateBO);
                    return Result.ERROR(mergedResult.getMessage());
                } else {
                    String videoUrl = mergedResult.getData();
                    if (StringUtils.isBlank(videoUrl)) {
                        log.error("[{}] 合并视频成功但URL为空", methodName);
                        updateBO.setStatus(VideoTaskStatusEnum.FAILED.getValue());
                        updateBO.setErrorMsg("合并视频成功但URL为空");
                        digitalVideoTaskService.updateTask(updateBO);
                        return Result.ERROR("合并视频成功但URL为空");
                    }
                    
                    // 截取视频封面
                    String coverUrl = DigitalFileUtil.extractVideoCover(videoUrl, mainTask.getTaskId(), mainTask.getUserId(), null, 8, false);
                    if (StringUtils.isBlank(coverUrl)) {
                        log.error("[{}] 截取视频封面失败", methodName);
                    }
                    
                    // 更新主任务状态
                    updateBO.setStatus(VideoTaskStatusEnum.SUCCESS.getValue());
                    updateBO.setVideoUrl(videoUrl);
                    updateBO.setCoverUrl(coverUrl);
                    digitalVideoTaskService.updateTask(updateBO);
                    return Result.SUCCESS(videoUrl);
                }
                
            } catch (Exception e) {
                hasError = true;
                log.error("[{}] 合并视频异常：taskId={}, error={}",
                        methodName, mainTask.getTaskId(), e.getMessage(), e);

                updateBO.setStatus(VideoTaskStatusEnum.FAILED.getValue());
                updateBO.setErrorMsg("合并视频失败：" + e.getMessage());
                digitalVideoTaskService.updateTask(updateBO);
            }
        } catch (Exception e) {
            hasError = true;
            log.error("[{}] 检查并合并视频异常：taskId={}, error={}",
                    methodName, mainTask.getTaskId(), e.getMessage(), e);
            return Result.ERROR("检查并合并视频异常：" + e.getMessage());
        } finally {
            // 推送状态更新通知
            if (!hasError) {
                pushTaskStatus(mainTask.getUserId(), mainTask.getTaskId(),
                        VideoTaskStatusEnum.SUCCESS.getValue(),VideoTaskStatusEnum.SUCCESS.getDesc());
            } else {
                pushTaskStatus(mainTask.getUserId(), mainTask.getTaskId(),
                        VideoTaskStatusEnum.FAILED.getValue(),VideoTaskStatusEnum.FAILED.getDesc());
            }
        }
        return Result.ERROR("检查并合并视频错误");
    }

    /**
     * 合并视频
     */
    private Result<String> mergeVideos(List<String> videoUrls, String userId, String taskId) {
        String methodName = "mergeVideos";
        log.info("[{}] 开始合并视频，userId：{}，taskId：{}", methodName, userId, taskId);

        try {
            // 检查视频列表
            if (videoUrls == null || videoUrls.isEmpty()) {
                log.error("[{}] 视频列表为空", methodName);
                return Result.ERROR("视频列表不能为空");
            }

            // 过滤掉null或空字符串的URL
            List<String> validVideoUrls = videoUrls.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            
            if (validVideoUrls.isEmpty()) {
                log.error("[{}] 没有有效的视频URL", methodName);
                return Result.ERROR("没有有效的视频URL");
            }

            // 检查视频格式
            for (String url : validVideoUrls) {
                if (!url.endsWith(".mp4")) {
                    log.error("[{}] 视频格式不是MP4：{}", methodName, url);
                    return Result.ERROR("视频格式必须是MP4");
                }
            }

            // 如果只有一个视频，直接返回该视频URL
            if (validVideoUrls.size() == 1) {
                log.info("[{}] 只有一个视频，直接返回：{}", methodName, validVideoUrls.getFirst());
                return Result.SUCCESS(validVideoUrls.getFirst());
            }

            // 调用DigitalFileUtil的视频合并方法
            log.info("[{}] 开始合并视频，视频列表：{}", methodName, validVideoUrls);
            String mergedVideoUrl = DigitalFileUtil.mergeVideos(validVideoUrls, userId, null);

            if (StringUtils.isBlank(mergedVideoUrl)) {
                log.error("[{}] 视频合并失败，返回URL为空", methodName);
                return Result.ERROR("合并视频失败，返回URL为空");
            }

            log.info("[{}] 视频合并成功，合并后的URL：{}", methodName, mergedVideoUrl);
            return Result.SUCCESS(mergedVideoUrl);

        } catch (Exception e) {
            String errorMsg = "合并视频失败：" + e.getMessage();
            log.error("[{}] {}", methodName, errorMsg, e);
            return Result.ERROR(errorMsg);
        }
    }

    
    /**
     * 获取腾讯云API密钥
     */
    private String getApiKey() {
        return tencentCloudConfig.getApiKeyConfig();
    }

    /**
     * 推送任务状态到WebSocket
     * 使用统一的消息推送机制，通过Redis发送消息，由nacos-user-server模块的RedisMessageListenerAdapter处理
     * 
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String message) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 开始推送任务状态：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
        
        try {
            // 1. 查询任务信息
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            
            if (task != null) {
                // 2. 如果任务完成或失败，发送通知消息
                if (status != null && (status == VideoTaskStatusEnum.SUCCESS.getValue() || status == VideoTaskStatusEnum.FAILED.getValue())) {
                    log.debug("[{}] 构建通知消息：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
                    
                    // 使用新的数字人专用通知服务
                    int notifType = status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue() : 
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue();
                    
                    String notifContent = message != null ? message : 
                        (status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                            "您的数字人视频已生成完成，点击查看" : 
                            "很抱歉，您的数字人视频生成失败，请重试");
                    
                    // 1. 创建通知记录
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        notifType,
                        notifContent
                    );
                    
                    // 2. 发送通知消息
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("taskId", taskId);
                    dataMap.put("notifType", notifType);
                    dataMap.put("notifTitle", DigitalNotificationEnum.getDesc(notifType));
                    dataMap.put("notifContent", notifContent);
                    
                    String jsonMessage = MessageSendUtil.getJSONStr(
                        userId,
                        BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                        dataMap
                    );
                    BRedisServiceUtil.sendMessageDigital(jsonMessage);
                }
            } else {
                log.warn("[{}] 未找到任务信息，无法推送状态: userId={}, taskId={}", methodName, userId, taskId);
            }
        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}