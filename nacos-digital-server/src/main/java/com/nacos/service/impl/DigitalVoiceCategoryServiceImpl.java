package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.DigitalVoiceCategoryPO;
import com.nacos.entity.po.DigitalVoiceCategoryRelationPO;
import com.nacos.entity.vo.DigitalVoiceCategoryVO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.mapper.DigitalVoiceCategoryMapper;
import com.nacos.mapper.DigitalVoiceCategoryRelationMapper;
import com.nacos.service.DigitalVoiceCategoryService;
import com.nacos.service.DigitalVoiceStyleService;
import com.nacos.service.VoiceDualWriteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音色分类服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVoiceCategoryServiceImpl implements DigitalVoiceCategoryService {

    private final DigitalVoiceCategoryMapper digitalVoiceCategoryMapper;
    private final DigitalVoiceCategoryRelationMapper relationMapper;
    private final DigitalVoiceStyleService digitalVoiceStyleService;
    private final VoiceDualWriteService voiceDualWriteService;

    /**
     * 获取所有启用的分类列表
     * 按照排序号升序排列
     */
    @Override
    public List<DigitalVoiceCategoryPO> listEnabledCategories() {
        return digitalVoiceCategoryMapper.selectList(
            new LambdaQueryWrapper<DigitalVoiceCategoryPO>()
                .eq(DigitalVoiceCategoryPO::getStatus, 1)        // 启用状态
                .eq(DigitalVoiceCategoryPO::getIsDeleted, 0)     // 未删除
                .orderByAsc(DigitalVoiceCategoryPO::getSortOrder) // 按排序号升序
        );
    }

    /**
     * 根据分类编码获取音色列表
     * 
     * @param categoryCode 分类编码
     * @return 该分类下的音色ID列表，如果分类不存在则返回空列表
     */
    @Override
    public List<String> listVoiceIdsByCategory(String categoryCode) {
        // 1. 获取分类ID
        DigitalVoiceCategoryPO category = digitalVoiceCategoryMapper.selectOne(
            new LambdaQueryWrapper<DigitalVoiceCategoryPO>()
                .eq(DigitalVoiceCategoryPO::getCategoryCode, categoryCode)
                .eq(DigitalVoiceCategoryPO::getStatus, 1)        // 启用状态
                .eq(DigitalVoiceCategoryPO::getIsDeleted, 0)     // 未删除
        );
        
        if (category == null) {
            return List.of();
        }

        // 2. 查询该分类下的所有音色ID
        List<DigitalVoiceCategoryRelationPO> relations = relationMapper.selectList(
            new LambdaQueryWrapper<DigitalVoiceCategoryRelationPO>()
                .eq(DigitalVoiceCategoryRelationPO::getCategoryId, category.getId())
        );

        return relations.stream()
            .map(DigitalVoiceCategoryRelationPO::getVoiceId)
            .collect(Collectors.toList());
    }

    /**
     * 更新音色分类关联
     * 先删除原有关联，再添加新的关联
     * 
     * @param voiceId 音色ID
     * @param categoryIds 分类ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVoiceCategoryRelation(String voiceId, List<Long> categoryIds) {
        // 1. 删除原有关联
        relationMapper.delete(
            new LambdaQueryWrapper<DigitalVoiceCategoryRelationPO>()
                .eq(DigitalVoiceCategoryRelationPO::getVoiceId, voiceId)
        );

        // 2. 添加新的关联
        if (categoryIds != null && !categoryIds.isEmpty()) {
            List<DigitalVoiceCategoryRelationPO> relations = categoryIds.stream()
                .map(categoryId -> {
                    DigitalVoiceCategoryRelationPO relation = new DigitalVoiceCategoryRelationPO();
                    relation.setVoiceId(voiceId);
                    relation.setCategoryId(categoryId);
                    return relation;
                })
                .collect(Collectors.toList());
            
            relations.forEach(relationMapper::insert);

            // 双写分类关联到新表
            try {
                voiceDualWriteService.syncVoiceCategoryRelations(voiceId, categoryIds);
            } catch (Exception e) {
                log.warn("双写分类关联失败，voiceId: {}", voiceId, e);
            }
        }
    }

    /**
     * 获取所有分类及其下的音色列表
     * 包含每个分类下的完整音色信息
     */
    @Override
    public List<DigitalVoiceCategoryVO> listCategoriesWithVoices() {
        // 1. 获取所有启用的分类
        List<DigitalVoiceCategoryPO> categories = listEnabledCategories();
        
        // 2. 转换为VO并查询每个分类下的音色
        return categories.stream().map(category -> {
            DigitalVoiceCategoryVO vo = new DigitalVoiceCategoryVO();
            BeanUtils.copyProperties(category, vo);
            
            // 获取该分类下的音色列表
            List<String> voiceIds = listVoiceIdsByCategory(category.getCategoryCode());
            if (!voiceIds.isEmpty()) {
                List<DigitalVoiceStyleVO> voiceList = digitalVoiceStyleService.getVoiceStylesByIds(voiceIds);
                vo.setVoiceList(voiceList);
            } else {
                vo.setVoiceList(new ArrayList<>());
            }
            
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据分类编码获取分类信息及其下的音色列表
     * 
     * @param categoryCode 分类编码
     * @return 包含音色列表的分类信息，如果分类不存在则返回null
     */
    @Override
    public DigitalVoiceCategoryVO getCategoryWithVoices(String categoryCode) {
        // 1. 获取分类信息
        DigitalVoiceCategoryPO category = digitalVoiceCategoryMapper.selectOne(
            new LambdaQueryWrapper<DigitalVoiceCategoryPO>()
                .eq(DigitalVoiceCategoryPO::getCategoryCode, categoryCode)
                .eq(DigitalVoiceCategoryPO::getStatus, 1)        // 启用状态
                .eq(DigitalVoiceCategoryPO::getIsDeleted, 0)     // 未删除
        );
        
        if (category == null) {
            return null;
        }
        // 2. 转换为VO并查询分类下的音色
        DigitalVoiceCategoryVO vo = new DigitalVoiceCategoryVO();
        BeanUtils.copyProperties(category, vo);
        
        // 获取该分类下的音色列表
        List<String> voiceIds = listVoiceIdsByCategory(categoryCode);
        if (!voiceIds.isEmpty()) {
            List<DigitalVoiceStyleVO> voiceList = digitalVoiceStyleService.getVoiceStylesByIds(voiceIds);
            vo.setVoiceList(voiceList);
        } else {
            vo.setVoiceList(new ArrayList<>());
        }
        
        return vo;
    }

    /**
     * 获取所有分类（不包含音色列表）
     * 只返回分类基本信息，音色列表为空
     */
    @Override
    public List<DigitalVoiceCategoryVO> listAllCategories() {
        // 获取所有启用的分类
        List<DigitalVoiceCategoryPO> categories = listEnabledCategories();
        
        // 转换为VO（不包含音色列表）
        return categories.stream().map(category -> {
            DigitalVoiceCategoryVO vo = new DigitalVoiceCategoryVO();
            BeanUtils.copyProperties(category, vo);
            vo.setVoiceList(new ArrayList<>()); // 设置空列表
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSystemVoiceCategories(List<String> voiceIds, String categoryCode) {
        // 1. 获取分类信息
        DigitalVoiceCategoryPO category = digitalVoiceCategoryMapper.selectOne(
            new LambdaQueryWrapper<DigitalVoiceCategoryPO>()
                .eq(DigitalVoiceCategoryPO::getCategoryCode, categoryCode)
//                .eq(DigitalVoiceCategoryPO::getStatus, 1)        // 启用状态
//                .eq(DigitalVoiceCategoryPO::getIsDeleted, 0)     // 未删除
        );
        
        if (category == null) {
            throw new RuntimeException("分类不存在或已禁用：" + categoryCode);
        }

        // 2. 删除这些音色的原有分类关联
        relationMapper.delete(
            new LambdaQueryWrapper<DigitalVoiceCategoryRelationPO>()
                .in(DigitalVoiceCategoryRelationPO::getVoiceId, voiceIds)
        );

        // 3. 批量插入新的关联关系
        List<DigitalVoiceCategoryRelationPO> relations = voiceIds.stream()
            .map(voiceId -> {
                DigitalVoiceCategoryRelationPO relation = new DigitalVoiceCategoryRelationPO();
                relation.setVoiceId(voiceId);
                relation.setCategoryId(category.getId());
                return relation;
            })
            .collect(Collectors.toList());
        
        relations.forEach(relationMapper::insert);

        // 批量双写分类关联到新表
        try {
            for (String voiceId : voiceIds) {
                voiceDualWriteService.syncVoiceCategoryRelations(voiceId, List.of(category.getId()));
            }
        } catch (Exception e) {
            log.warn("批量双写分类关联失败，categoryCode: {}", categoryCode, e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initializeSystemVoiceCategories() {
        // 英文音色列表
        List<String> englishVoices = List.of(
            "Arnold", "Attractive_Girl", "Charming_Lady", "Charming_Santa",
            "Cute_Elf", "Grinch", "Rudolph", "Santa_Claus", "Serene_Woman", "Sweet_Girl"
        );

        // 中文音色列表
        List<String> chineseVoices = List.of(
            "audiobook_female_1", "audiobook_female_2", "audiobook_male_1", "audiobook_male_2",
            "badao_shaoye", "bingjiao_didi", "cartoon_pig", "chunzhen_xuedi", "clever_boy",
            "cute_boy", "danya_xuejie", "diadia_xuemei", "female-chengshu", "female-shaonv",
            "female-tianmei", "female-yujie", "junlang_nanyou", "lengdan_xiongzhang",
            "lovely_girl", "male-qn-badao", "male-qn-daxuesheng", "male-qn-jingying",
            "male-qn-qingse", "presenter_female", "presenter_male", "qiaopi_mengmei",
            "tianxin_xiaoling", "wumei_yujie"
        );

        // 更新英文音色分类
        batchUpdateSystemVoiceCategories(englishVoices, "ENGLISH");

        // 更新中文音色分类
        batchUpdateSystemVoiceCategories(chineseVoices, "CHINESE");
    }

} 