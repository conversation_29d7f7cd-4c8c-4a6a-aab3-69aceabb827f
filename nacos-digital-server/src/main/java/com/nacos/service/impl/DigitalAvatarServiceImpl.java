package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.utils.BThirdPartyKey;
import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.enums.TaskStatusEnum;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.vo.DigitalUserAvatarVO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.entity.dto.ChanJingAvatarRequestDTO;
import com.nacos.entity.dto.CreateVideoRequestDTO;
import com.nacos.entity.dto.DigitalAvatarTrainDTO;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.enums.DictConfigEnum;
import com.nacos.mapper.DigitalVoiceUserCloneMapper;
import com.nacos.model.ChanJing.model.*;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadRequestBO;
import com.nacos.service.DigitalAvatarService;
import com.nacos.service.DigitalAudioService;
import com.nacos.result.Result;
import com.nacos.service.DigitalUserGroupService;
import com.nacos.service.FeiyongService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.nacos.utils.DigitalFileUtil;
import com.nacos.model.ChanJing.ChanJingApiUtil;
import com.nacos.model.ChanJing.ChanJingApiUtil.ChanJingApiException;
import com.nacos.model.ChanJing.ChanJingApi;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nacos.constant.CommonConst.*;

/**
 * 数字人服务实现类
 * 处理数字人的增删改查等基础操作
 */
@Slf4j
@Service
public class DigitalAvatarServiceImpl extends ServiceImpl<DigitalUserAvatarMapper, DigitalUserAvatarPO> implements DigitalAvatarService {

    @Autowired
    private DigitalAudioService digitalAudioService;
    
    @Autowired
    private DigitalVoiceUserCloneMapper digitalVoiceUserCloneMapper;

    @Autowired
    private DigitalUserGroupService digitalUserGroupService;

    @Resource
    FeiyongService feiyongService;

    @Autowired
    private ChanJingApi chanJingApi;

    @Autowired
    private ObjectMapper objectMapper;

    @Value(value = "${changjin_secret_key}")
    String secretKey;

    /**
     * 获取数字人列表
     * 获取所有未删除的数字人，按组ID升序排序
     * @return 数字人列表
     */
    @Override
    public Result<List<DigitalUserAvatarVO>> listAvatars() {
        try {
            LambdaQueryWrapper<DigitalUserAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserAvatarPO::getIsDeleted, 0)
                   .orderByAsc(DigitalUserAvatarPO::getGroupId);
            List<DigitalUserAvatarPO> list = this.list(wrapper);
            List<DigitalUserAvatarVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取数字人列表失败", e);
            return Result.ERROR("获取数字人列表失败");
        }
    }

    /**
     * 添加数字人
     * 添加新的数字人记录，包含参数验证和唯一性检查
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> addAvatar(DigitalUserAvatarVO avatarVO) {
        try {
            if (!StringUtils.hasText(avatarVO.getAvatarName())) {
                return Result.ERROR("数字人名称不能为空");
            }
            if (!StringUtils.hasText(avatarVO.getGroupId())) {
                return Result.ERROR("所属组ID不能为空");
            }
            if (!StringUtils.hasText(avatarVO.getUserId())) {
                return Result.ERROR("用户ID不能为空");
            }

            // 检查数字人ID是否已存在
            LambdaQueryWrapper<DigitalUserAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserAvatarPO::getAvatarId, avatarVO.getAvatarId())
                   .eq(DigitalUserAvatarPO::getIsDeleted, 0);
            if (this.count(wrapper) > 0) {
                return Result.ERROR("数字人ID已存在");
            }

            DigitalUserAvatarPO po = new DigitalUserAvatarPO();
            BeanUtils.copyProperties(avatarVO, po);
            
            // 不需要手动设置这些值，会由MyMetaObjectHandler自动填充
            // avatarId会自动生成
            // status会自动设置默认值1
            // groupType会自动设置默认值1
            // createdTime会自动填充
            // updateTime会自动填充
            // isDeleted会自动设置默认值0
            
            boolean success = this.save(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("添加数字人失败");
        } catch (Exception e) {
            log.error("添加数字人失败", e);
            return Result.ERROR("添加数字人失败：" + e.getMessage());
        }
    }

    /**
     * 更新数字人
     * 更新现有数字人记录，包含参数验证和唯一性检查
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateAvatar(DigitalUserAvatarVO avatarVO) {
        try {
            if (avatarVO.getId() == null) {
                return Result.ERROR("数字人ID不能为空");
            }
            if (!StringUtils.hasText(avatarVO.getAvatarName())) {
                return Result.ERROR("数字人名称不能为空");
            }

            // 检查数字人是否存在
            DigitalUserAvatarPO existingAvatar = this.getById(avatarVO.getId());
            if (existingAvatar == null || existingAvatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }

            // 检查数字人ID是否重复（排除自身）
            if (StringUtils.hasText(avatarVO.getAvatarId())) {
                LambdaQueryWrapper<DigitalUserAvatarPO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DigitalUserAvatarPO::getAvatarId, avatarVO.getAvatarId())
                       .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                       .ne(DigitalUserAvatarPO::getId, avatarVO.getId());
                if (this.count(wrapper) > 0) {
                    return Result.ERROR("数字人ID已存在");
                }
            }

            DigitalUserAvatarPO po = new DigitalUserAvatarPO();
            BeanUtils.copyProperties(avatarVO, po);
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新数字人失败");
        } catch (Exception e) {
            log.error("更新数字人失败", e);
            return Result.ERROR("更新数字人失败：" + e.getMessage());
        }
    }

    /**
     * 删除数字人
     * 逻辑删除指定ID的数字人记录
     * @param id 数字人ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteAvatar(Long id) {
        try {
            if (id == null) {
                return Result.ERROR("数字人ID不能为空");
            }

            DigitalUserAvatarPO avatar = this.getById(id);
            if (avatar == null || avatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }

            // 逻辑删除，会自动设置isDeleted=1
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.removeById(id);
            return success ? Result.SUCCESS(true) : Result.ERROR("删除数字人失败");
        } catch (Exception e) {
            log.error("删除数字人失败", e);
            return Result.ERROR("删除数字人失败：" + e.getMessage());
        }
    }

    /**
     * 更新数字人状态
     * 更新指定ID的数字人状态
     * @param id 数字人ID
     * @param status 状态：0-禁用 1-启用
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateStatus(Long id, Integer status) {
        try {
            if (id == null) {
                return Result.ERROR("数字人ID不能为空");
            }
            if (status == null || (status != 0 && status != 1)) {
                return Result.ERROR("状态值无效");
            }

            DigitalUserAvatarPO avatar = this.getById(id);
            if (avatar == null || avatar.getIsDeleted() == 1) {
                return Result.ERROR("数字人不存在");
            }
            
            DigitalUserAvatarPO po = new DigitalUserAvatarPO();
            po.setId(id);
            po.setStatus(status);
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新状态失败");
        } catch (Exception e) {
            log.error("更新数字人状态失败", e);
            return Result.ERROR("更新数字人状态失败：" + e.getMessage());
        }
    }

    /**
     * 根据组ID和用户ID获取数字人列表
     * 获取指定组ID和用户ID的所有启用的数字人
     * @param groupId 组ID
     * @param userId 用户ID
     * @return 数字人列表
     */
    @Override
    public Result<List<DigitalUserAvatarVO>> listAvatarsByGroupId(Long groupId, String userId) {
        try {
            if (groupId == null) {
                return Result.ERROR("组ID不能为空");
            }
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            LambdaQueryWrapper<DigitalUserAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserAvatarPO::getGroupId, groupId)
                   .eq(DigitalUserAvatarPO::getUserId, userId)
                   .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                   .eq(DigitalUserAvatarPO::getStatus, 1);
            
            List<DigitalUserAvatarPO> list = this.list(wrapper);
            List<DigitalUserAvatarVO> voList = list.stream()
                                              .map(this::convertToVO)
                                              .collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取组数字人列表失败，组ID：{}，用户ID：{}", groupId, userId, e);
            return Result.ERROR("获取组数字人列表失败");
        }
    }

    /**
     * 根据用户ID获取数字人列表
     * 获取指定用户ID的所有启用的数字人
     * @param userId 用户ID
     * @return 数字人列表
     */
    @Override
    public Result<List<DigitalUserAvatarVO>> listAvatarsByUserId(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            LambdaQueryWrapper<DigitalUserAvatarPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalUserAvatarPO::getUserId, userId)
                   .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                   .eq(DigitalUserAvatarPO::getStatus, 1);
            
            List<DigitalUserAvatarPO> list = this.list(wrapper);
            List<DigitalUserAvatarVO> voList = list.stream()
                                              .map(this::convertToVO)
                                              .collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取用户数字人列表失败，用户ID：{}", userId, e);
            return Result.ERROR("获取用户数字人列表失败");
        }
    }

    /**
     * 将PO对象转换为VO对象
     * @param po PO对象
     * @return VO对象
     */
    private DigitalUserAvatarVO convertToVO(DigitalUserAvatarPO po) {
        if (po == null) {
            return null;
        }
        DigitalUserAvatarVO vo = new DigitalUserAvatarVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 开始训练数字人
     * 上传训练视频、授权视频、声音文件，并进行声音克隆
     * 支持两种功能：
     * 1. 创建新组并添加数字人：上传训练视频、授权视频、声音文件，创建新组，并将数字人添加到新组中
     * 2. 使用现有组添加数字人：上传训练视频、授权视频、声音文件，将数字人添加到现有组中
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> trainAvatar(DigitalAvatarTrainDTO trainDTO) {
        String methodName = "trainAvatar";
        log.info("[{}] 开始训练数字人，参数：{}", methodName, trainDTO);
        long logId = IdWorker.getId();
        boolean koufeiResult = false;
        try {
            // 1. 参数校验
            if (!StringUtils.hasText(trainDTO.getAvatarName())) {
                log.error("[{}] 数字人名称为空", methodName);
                return Result.ERROR("数字人名称不能为空");
            }
            if (!StringUtils.hasText(trainDTO.getUserId())) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }
            if (trainDTO.getAvatarVideo() == null) {
                log.error("[{}] 训练视频为空", methodName);
                return Result.ERROR("训练视频不能为空");
            }
            if (trainDTO.getAuthVideo() == null) {
                log.error("[{}] 授权视频为空", methodName);
                return Result.ERROR("授权视频不能为空");
            }
            if (trainDTO.getVoiceFile() == null) {
                log.error("[{}] 声音文件为空", methodName);
                return Result.ERROR("声音文件不能为空");
            }
            
            // 根据createNewGroup字段判断是否需要创建新组
            Long userGroupId = null;
            String groupId = null;
            
            // 2. 处理组信息
            if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup())) {
                // 2.1创建新组，创建数字人形象
                if (!StringUtils.hasText(trainDTO.getGroupName())) {
                    log.error("[{}] 创建新组时，组名称为空", methodName);
                    return Result.ERROR("创建新组时，组名称不能为空");
                }
                
                log.info("[{}] 开始创建数字人组", methodName);
                DigitalUserGroupDTO groupDTO = new DigitalUserGroupDTO();
                groupDTO.setGroupName(trainDTO.getGroupName());
                groupDTO.setUserId(trainDTO.getUserId());
                
                Result<DigitalUserGroupVO> userGroupVOResult = digitalUserGroupService.createGroup(groupDTO);
                if (!userGroupVOResult.isSuccess()) {
                    log.error("[{}] 创建数字人组失败：{}", methodName, userGroupVOResult.getMessage());
                    return Result.ERROR("创建数字人组失败：" + userGroupVOResult.getMessage());
                }
                groupId = userGroupVOResult.getData().getGroupId();
                userGroupId = userGroupVOResult.getData().getId();
                log.info("[{}] 创建数字人组成功，groupId：{}", methodName, groupId);
            } else {
                // 2.2使用现有组，创建分身
                if (trainDTO.getGroupId() != null && trainDTO.getGroupId() > 0) {
                    // 验证组是否存在
                    Result<DigitalUserGroupVO> groupDetailResult = digitalUserGroupService.getGroupDetail(trainDTO.getGroupId());
                    if (!groupDetailResult.isSuccess() || groupDetailResult.getData() == null) {
                        log.error("[{}] 组不存在，groupId：{}", methodName, trainDTO.getGroupId());
                        return Result.ERROR("组不存在");
                    }
                    
                    groupId = groupDetailResult.getData().getGroupId();
                    userGroupId = groupDetailResult.getData().getId();
                    log.info("[{}] 使用现有组，groupId：{}", methodName, groupId);
                }
            }

            // 3. 创建数字人PO对象
            DigitalUserAvatarPO avatarPO = new DigitalUserAvatarPO();
            avatarPO.setAvatarName(trainDTO.getAvatarName());
            avatarPO.setUserId(trainDTO.getUserId());
            avatarPO.setGroupId(groupId);
            avatarPO.setCoverUrl("暂无");
            avatarPO.setAvatarType(trainDTO.getAvatarType() != null ? trainDTO.getAvatarType() : 1);
            log.info("[{}] 创建数字人PO对象：{}", methodName, avatarPO);

            // 4. 上传训练视频（数字人形象视频）
            log.info("[{}] 开始上传训练视频", methodName);
            String avatarVideoUrl = DigitalFileUtil.uploadDigitalResource(
                trainDTO.getAvatarVideo(),
                null, // 使用自动生成的文件名
                trainDTO.getUserId(),
                groupId,
                1,
                false
            );
            if (avatarVideoUrl == null) {
                log.error("[{}] 上传训练视频失败", methodName);
                // 如果是新创建的组，则删除
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                    digitalUserGroupService.deleteGroup(userGroupId);
                    log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
                }
                return Result.ERROR("上传训练视频失败");
            }
            avatarPO.setAvatarVideoUrl(avatarVideoUrl);
            log.info("[{}] 上传训练视频成功，URL：{}", methodName, avatarVideoUrl);

            // 5. 生成数字人封面
            log.info("[{}] 开始生成数字人封面", methodName);
            String coverUrl = DigitalFileUtil.extractVideoCover(avatarVideoUrl, trainDTO.getAvatarName(), trainDTO.getUserId(), groupId, 6, false);
            if (coverUrl != null) {
                // 更新数字人形象的封面
                avatarPO.setCoverUrl(coverUrl);
                // 如果是新创建的组，则更新组的封面
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup())) {
                    DigitalUserGroupDTO updateGroupDTO = new DigitalUserGroupDTO();
                    updateGroupDTO.setGroupId(groupId);
                    updateGroupDTO.setCoverUrl(coverUrl);
                    updateGroupDTO.setUserId(trainDTO.getUserId());
                    Result<Boolean> updateGroupResult = digitalUserGroupService.updateGroup(updateGroupDTO);
                    if (!updateGroupResult.isSuccess()) {
                        log.error("[{}] 更新数字人组封面失败：{}", methodName, updateGroupResult.getMessage());
                    } else {
                        log.info("[{}] 更新数字人组封面成功，groupId：{}", methodName, groupId);
                    }
                }
                log.info("[{}] 生成数字人封面成功，URL：{}", methodName, coverUrl);
            } else {
                log.error("[{}] 生成数字人封面失败", methodName);
            }

            // 6. 上传授权视频
            log.info("[{}] 开始上传授权视频", methodName);
            String authVideoUrl = DigitalFileUtil.uploadDigitalResource(
                trainDTO.getAuthVideo(),
                null, // 使用自动生成的文件名
                trainDTO.getUserId(),
                groupId,
                0,
                false
            );
            if (authVideoUrl == null) {
                log.error("[{}] 上传授权视频失败", methodName);
                // 如果是新创建的组，则删除
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                    digitalUserGroupService.deleteGroup(userGroupId);
                    log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
                }
                return Result.ERROR("上传授权视频失败");
            }
            avatarPO.setAuthVideoUrl(authVideoUrl);
            log.info("[{}] 上传授权视频成功，URL：{}", methodName, authVideoUrl);

            // 7. 上传声音文件到MiniMax
            log.info("[{}] 开始上传声音文件到MiniMax", methodName);
            MiniMaxFileUploadRequestBO uploadRequestBO = new MiniMaxFileUploadRequestBO();
            uploadRequestBO.setPurpose("voice_clone");
            uploadRequestBO.setFile(trainDTO.getVoiceFile());
            String fileId = MiniMaxApiUtil.uploadFileToMiniMax(uploadRequestBO);
            if (fileId == null) {
                log.error("[{}] 上传声音文件失败", methodName);
                // 如果是新创建的组，则删除
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                    digitalUserGroupService.deleteGroup(userGroupId);
                    log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
                }
                return Result.ERROR("上传声音文件失败");
            }
            log.info("[{}] 上传声音文件成功，fileId：{}", methodName, fileId);

            // 8. 调用声音克隆接口
            log.info("[{}] 开始调用声音克隆接口", methodName);
            DigitalVoiceCloningDTO audioDTO = new DigitalVoiceCloningDTO();
            audioDTO.setFileId(Long.parseLong(fileId));
            audioDTO.setVoiceName(trainDTO.getAvatarName());
            audioDTO.setAvatarId(avatarPO.getAvatarId());
            Result<String> cloneResult = digitalAudioService.voiceCloning(audioDTO, trainDTO.getUserId());
            if (!cloneResult.isSuccess()) {
                log.error("[{}] 声音克隆失败：{}", methodName, cloneResult.getMessage());
                // 如果是新创建的组，则删除
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                    digitalUserGroupService.deleteGroup(userGroupId);
                    log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
                }
                return Result.ERROR("声音克隆失败：" + cloneResult.getMessage());
            }
            log.info("[{}] 声音克隆成功", methodName);

            // 9. 保存数字人信息到数据库
            log.info("[{}] 开始保存数字人信息到数据库", methodName);
            boolean success = this.save(avatarPO);
            if (!success) {
                log.error("[{}] 保存数字人信息失败", methodName);
                // 如果是新创建的组，则删除
                if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                    digitalUserGroupService.deleteGroup(userGroupId);
                    log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
                }
                return Result.ERROR("保存数字人信息失败");
            }
            log.info("[{}] 保存数字人信息成功，avatarId：{}", methodName, avatarPO.getAvatarId());

            //4.1扣费
            koufeiResult = feiyongService.koufei(Long.valueOf(trainDTO.getUserId()),logId,"萤火虫定制形象扣费", DDUseRuleEnum.VIDEO_COPY.getRedisKey());
            return Result.SUCCESS(avatarPO.toString());
        } catch (Exception e) {
            if (koufeiResult){
                feiyongService.tuifei(Long.valueOf(trainDTO.getUserId()),logId,"萤火虫定制形象退费",DDUseRuleEnum.VIDEO_COPY.getRedisKey());
            }
            log.error("[{}] 训练数字人过程发生异常", methodName, e);
            return Result.ERROR("训练数字人失败：" + e.getMessage());
        }
    }

    /**
     * 开始训练数字人
     * 上传训练视频、授权视频、声音文件，并进行声音克隆
     *
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    /**
     * 新的训练数字人方法
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> trainAvatarNew(DigitalAvatarTrainDTO trainDTO) {
        String methodName = "trainAvatarNew";
        log.info("[{}] 开始训练数字人，参数：{}", methodName, trainDTO);
        long logId = IdWorker.getId();
        boolean koufeiResult = false;

        try {
            // 1. 参数校验
            Result<String> validateResult = validateParameters(trainDTO, methodName);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }

            if (!feiyongService.checkYue(Long.valueOf(trainDTO.getUserId()), DDUseRuleEnum.VIDEO_COPY.getRedisKey())){
                return Result.ERROR("余额不足");
            }

            // 2. 处理组信息
            Map<String, Object> groupInfo = handleGroupInfo(trainDTO, methodName);
            if (groupInfo == null) {
                return Result.ERROR("处理组信息失败");
            }
            Long userGroupId = (Long) groupInfo.get("userGroupId");
            String groupId = (String) groupInfo.get("groupId");

            // 3. 创建数字人PO对象
            DigitalUserAvatarPO avatarPO = createAvatarPO(trainDTO, groupId);

            // 4. 上传OSS，获取OSS返回的地址并保存到数据库
            Result<String> uploadResult = uploadToOSSAndSave(trainDTO, avatarPO, userGroupId, groupId, methodName);
            if (!uploadResult.isSuccess()) {
                return uploadResult;
            }

            // 5. 扣费
            koufeiResult = feiyongService.koufei(Long.valueOf(trainDTO.getUserId()), logId, "萤火虫定制形象扣费", DDUseRuleEnum.VIDEO_COPY.getRedisKey());

            return Result.SUCCESS(avatarPO.toString());
        } catch (Exception e) {
            if (koufeiResult) {
                feiyongService.tuifei(Long.valueOf(trainDTO.getUserId()), logId, "萤火虫定制形象退费", DDUseRuleEnum.VIDEO_COPY.getRedisKey());
            }
            log.error("[{}] 训练数字人过程发生异常", methodName, e);
            return Result.ERROR("训练数字人失败：" + e.getMessage());
        }
    }

    /**
     * 参数校验方法
     * @param trainDTO 数字人训练信息
     * @param methodName 方法名
     * @return 校验结果
     */
    private Result<String> validateParameters(DigitalAvatarTrainDTO trainDTO, String methodName) {
        if (!StringUtils.hasText(trainDTO.getAvatarName())) {
            log.error("[{}] 数字人名称为空", methodName);
            return Result.ERROR("数字人名称不能为空");
        }
        if (!StringUtils.hasText(trainDTO.getUserId())) {
            log.error("[{}] 用户ID为空", methodName);
            return Result.ERROR("用户ID不能为空");
        }
        if (trainDTO.getAvatarVideo() == null) {
            log.error("[{}] 训练视频为空", methodName);
            return Result.ERROR("训练视频不能为空");
        }
        if (trainDTO.getAuthVideo() == null) {
            log.error("[{}] 授权视频为空", methodName);
            return Result.ERROR("授权视频不能为空");
        }
        if (trainDTO.getVoiceFile() == null) {
            log.error("[{}] 声音文件为空", methodName);
            return Result.ERROR("声音文件不能为空");
        }
        if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) && !StringUtils.hasText(trainDTO.getGroupName())) {
            log.error("[{}] 创建新组时，组名称为空", methodName);
            return Result.ERROR("创建新组时，组名称不能为空");
        }
        return Result.SUCCESS(null);
    }

    /**
     * 处理组信息方法
     * @param trainDTO 数字人训练信息
     * @param methodName 方法名
     * @return 组信息
     */
    private Map<String, Object> handleGroupInfo(DigitalAvatarTrainDTO trainDTO, String methodName) {
        Long userGroupId = null;
        String groupId = null;

        if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup())) {
            DigitalUserGroupDTO groupDTO = new DigitalUserGroupDTO();
            groupDTO.setGroupName(trainDTO.getGroupName());
            groupDTO.setUserId(trainDTO.getUserId());

            Result<DigitalUserGroupVO> userGroupVOResult = digitalUserGroupService.createGroup(groupDTO);
            if (!userGroupVOResult.isSuccess()) {
                log.error("[{}] 创建数字人组失败：{}", methodName, userGroupVOResult.getMessage());
                return null;
            }
            groupId = userGroupVOResult.getData().getGroupId();
            userGroupId = userGroupVOResult.getData().getId();
            log.info("[{}] 创建数字人组成功，groupId：{}", methodName, groupId);
        } else {
            if (trainDTO.getGroupId() != null && trainDTO.getGroupId() > 0) {
                Result<DigitalUserGroupVO> groupDetailResult = digitalUserGroupService.getGroupDetail(trainDTO.getGroupId());
                if (!groupDetailResult.isSuccess() || groupDetailResult.getData() == null) {
                    log.error("[{}] 组不存在，groupId：{}", methodName, trainDTO.getGroupId());
                    return null;
                }
                groupId = groupDetailResult.getData().getGroupId();
                userGroupId = groupDetailResult.getData().getId();
                log.info("[{}] 使用现有组，groupId：{}", methodName, groupId);
            }
        }

        Map<String, Object> groupInfo = new HashMap<>();
        groupInfo.put("userGroupId", userGroupId);
        groupInfo.put("groupId", groupId);
        return groupInfo;
    }

    /**
     * 创建数字人PO对象方法
     * @param trainDTO 数字人训练信息
     * @param groupId 组ID
     * @return 数字人PO对象
     */
    private DigitalUserAvatarPO createAvatarPO(DigitalAvatarTrainDTO trainDTO, String groupId) {
        DigitalUserAvatarPO avatarPO = new DigitalUserAvatarPO();
        avatarPO.setAvatarName(trainDTO.getAvatarName());
        avatarPO.setUserId(trainDTO.getUserId());
        avatarPO.setGroupId(groupId);
        avatarPO.setCoverUrl("暂无");
        avatarPO.setTaskStatus(TaskStatusEnum.QUEUING.getValue());
        avatarPO.setVideoTaskStatus(VIDEO_TASK_STATUS_0);
        avatarPO.setVoiceTaskStatus(VOICE_TASK_STATUS_0);
        avatarPO.setAvatarType(trainDTO.getAvatarType() != null ? trainDTO.getAvatarType() : 1);
        return avatarPO;
    }

    /**
     * 上传OSS并保存信息到数据库方法
     * @param trainDTO 数字人训练信息
     * @param avatarPO 数字人PO对象
     * @param userGroupId 用户组ID
     * @param groupId 组ID
     * @param methodName 方法名
     * @return 上传结果
     */
    private Result<String> uploadToOSSAndSave(DigitalAvatarTrainDTO trainDTO, DigitalUserAvatarPO avatarPO, Long userGroupId, String groupId, String methodName) {
        // 上传训练视频
        String avatarVideoUrl = DigitalFileUtil.uploadDigitalResource(
                trainDTO.getAvatarVideo(),
                null,
                trainDTO.getUserId(),
                groupId,
                1,
                false
        );
        if (avatarVideoUrl == null) {
            log.error("[{}] 上传训练视频失败", methodName);
            if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                digitalUserGroupService.deleteGroup(userGroupId);
                log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
            }
            return Result.ERROR("上传训练视频失败");
        }
        avatarPO.setAvatarVideoUrl(avatarVideoUrl);
        log.info("[{}] 上传训练视频成功，URL：{}", methodName, avatarVideoUrl);

        // 生成数字人封面
        String coverUrl = DigitalFileUtil.extractVideoCover(avatarVideoUrl, trainDTO.getAvatarName(), trainDTO.getUserId(), groupId, 6, false);
        if (coverUrl != null) {
            avatarPO.setCoverUrl(coverUrl);
            if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup())) {
                DigitalUserGroupDTO updateGroupDTO = new DigitalUserGroupDTO();
                updateGroupDTO.setGroupId(groupId);
                updateGroupDTO.setCoverUrl(coverUrl);
                updateGroupDTO.setUserId(trainDTO.getUserId());
                Result<Boolean> updateGroupResult = digitalUserGroupService.updateGroup(updateGroupDTO);
                if (!updateGroupResult.isSuccess()) {
                    log.error("[{}] 更新数字人组封面失败：{}", methodName, updateGroupResult.getMessage());
                } else {
                    log.info("[{}] 更新数字人组封面成功，groupId：{}", methodName, groupId);
                }
            }
            log.info("[{}] 生成数字人封面成功，URL：{}", methodName, coverUrl);
        } else {
            log.error("[{}] 生成数字人封面失败", methodName);
        }

        // 上传授权视频
        String authVideoUrl = DigitalFileUtil.uploadDigitalResource(
                trainDTO.getAuthVideo(),
                null,
                trainDTO.getUserId(),
                groupId,
                0,
                false
        );
        if (authVideoUrl == null) {
            log.error("[{}] 上传授权视频失败", methodName);
            if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                digitalUserGroupService.deleteGroup(userGroupId);
                log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
            }
            return Result.ERROR("上传授权视频失败");
        }
        avatarPO.setAuthVideoUrl(authVideoUrl);
        log.info("[{}] 上传授权视频成功，URL：{}", methodName, authVideoUrl);

        // 上传声音文件，调用uploadDigitalResource上传，返回voiceUrl，存入到avatarPO中
        String voiceUrl = DigitalFileUtil.uploadDigitalResource(
            trainDTO.getVoiceFile(),
            null,
            trainDTO.getUserId(),
            groupId,
            // 这里需要根据实际情况确定上传类型，假设为 2
            4,
            false
        );
        if (voiceUrl == null) {
        log.error("[{}] 上传声音文件失败", methodName);
        if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
            digitalUserGroupService.deleteGroup(userGroupId);
            log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
        }
        return Result.ERROR("上传声音文件失败");
        }
        avatarPO.setVoiceUrl(voiceUrl);
        log.info("[{}] 上传声音文件成功，URL：{}", methodName, voiceUrl);

        // 保存数字人信息到数据库
        boolean success = this.save(avatarPO);
        if (!success) {
            log.error("[{}] 保存数字人信息失败", methodName);
            if (Boolean.TRUE.equals(trainDTO.getCreateNewGroup()) || trainDTO.getGroupId() == null) {
                digitalUserGroupService.deleteGroup(userGroupId);
                log.info("[{}] 删除数字人组，groupId：{}", methodName, groupId);
            }
            return Result.ERROR("保存数字人信息失败");
        }
        log.info("[{}] 保存数字人信息成功，avatarId：{}", methodName, avatarPO.getAvatarId());

        return Result.SUCCESS(null);
    }

    /**
     * 创建禅境定制数字人
     *
     * @param requestDTO 请求参数
     * @return Result<String> 包含操作结果，成功时data为数字人ID
     */
    @Override
    public Result<String> createChanjingAvatar(ChanJingAvatarRequestDTO requestDTO) {
        String methodName = "createChanjingAvatar";
        log.info("[{}] 开始禅境定制数字人，参数：{}", methodName, requestDTO);

        // 2. 参数校验
        if (!StringUtils.hasText(requestDTO.getName())) {
            log.error("[{}] 数字人名称为空", methodName);
            return Result.ERROR("数字人名称不能为空");
        }
        if (!StringUtils.hasText(requestDTO.getMaterialVideo())) {
            log.error("[{}] 材料视频URL为空", methodName);
            return Result.ERROR("材料视频URL不能为空");
        }

        // 3. 禅境API请求参数
        CreateCustomisedPersonRequest apiRequest = new CreateCustomisedPersonRequest();
        apiRequest.setName(requestDTO.getName());
        apiRequest.setMaterialVideo(requestDTO.getMaterialVideo());
        apiRequest.setCallback(requestDTO.getCallback());
        apiRequest.setTrainType(requestDTO.getTrainType());
        
        // 4. 调用禅境API
        try {
            CreateCustomisedPersonResponse apiResponse = ChanJingApiUtil.createCustomisedPerson(chanJingApi, apiRequest, secretKey);
            log.info("[{}] 禅境定制数字人成功，数字人ID: {}", methodName, apiResponse.getData());
            return Result.SUCCESS(apiResponse.getData());
        } catch (ChanJingApiException e) {
            log.error("[{}] 禅境API业务错误 - Code: {}, Message: {}, TraceID: {}",
                methodName, e.getErrorCode(), e.getMessage(), e.getTraceId(), e);
            return Result.ERROR("禅境定制数字人失败(API)：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            // 捕获 ChanJingApiUtil 中当 chanJingApi 为 null 时抛出的 IllegalArgumentException
            log.error("[{}] 调用禅境工具类参数错误: {}", methodName, e.getMessage(), e);
            return Result.ERROR("禅境定制数字人配置错误（参数）：" + e.getMessage());
        } catch (Exception e) {
            // 捕获其他所有未预料的异常
            log.error("[{}] 禅境定制数字人过程发生未预料的同步执行异常", methodName, e);
            return Result.ERROR("禅境定制数字人失败（系统）：" + e.getMessage());
        }
    }

    /**
     * 拉取禅境定制数字人列表
     *
     * @param page 当前页
     * @param pageSize 每页数量
     * @return Result<String> 包含数字人列表和分页信息的JSON字符串
     */
    @Override
    public Result<String> listChanjingAvatars(Integer page, Integer pageSize) {
        String methodName = "listChanjingAvatars";
        log.info("[{}] 开始拉取禅境定制数字人列表，页码: {}, 每页数量: {}", methodName, page, pageSize);

        // 2. 构建禅境API请求参数
        ListCustomisedPersonRequest apiRequest = new ListCustomisedPersonRequest();
        apiRequest.setPage(page != null && page > 0 ? page : 1); // 默认第一页
        apiRequest.setPageSize(pageSize != null && pageSize > 0 ? pageSize : 20); // 默认每页20条

        // 3. 调用禅境API工具类
        try {
            ListCustomisedPersonResponse apiResponse = ChanJingApiUtil.listCustomisedPerson(chanJingApi, apiRequest, secretKey);
            log.info("[{}] 拉取禅境定制数字人列表成功", methodName);
            // 将结果序列化为JSON字符串
            String responseJson = objectMapper.writeValueAsString(apiResponse);
            return Result.SUCCESS(responseJson);
        } catch (ChanJingApiException e) {
            log.error("[{}] 禅境API业务错误 - Code: {}, Message: {}, TraceID: {}",
                methodName, e.getErrorCode(), e.getMessage(), e.getTraceId(), e);
            return Result.ERROR("拉取禅境数字人列表失败(API)：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("[{}] 调用禅境工具类参数错误: {}", methodName, e.getMessage(), e);
            return Result.ERROR("拉取禅境数字人列表配置错误（参数）：" + e.getMessage());
        } catch (Exception e) { // 包括 JsonProcessingException
            log.error("[{}] 拉取禅境定制数字人列表过程发生未预料的异常", methodName, e);
            return Result.ERROR("拉取禅境数字人列表失败（系统）：" + e.getMessage());
        }
    }

    /**
     * 创建禅境视频合成任务
     *
     * @param requestDTO 视频合成请求参数
     * @return Result<String> 成功时data为视频任务ID
     */
    @Override
    public Result<String> createChanjingVideo(CreateVideoRequestDTO requestDTO) {
        String methodName = "createChanjingVideo";
        log.info("[{}] 开始创建禅境视频合成任务，请求参数: {}", methodName, requestDTO);

        // 2. 参数校验 (基础校验，更详细的可以在ChanJingApiUtil或API层面做)
        if (requestDTO.getPerson() == null || !StringUtils.hasText(requestDTO.getPerson().getId())) {
            log.error("[{}] 请求参数错误：person.id 不能为空", methodName);
            return Result.ERROR("人物ID不能为空");
        }
        if (requestDTO.getAudio() == null) {
            log.error("[{}] 请求参数错误：audio 配置不能为空", methodName);
            return Result.ERROR("音频配置不能为空");
        }
        if ("tts".equalsIgnoreCase(requestDTO.getAudio().getType())) {
            if (requestDTO.getAudio().getTts() == null || 
                requestDTO.getAudio().getTts().getText() == null || 
                requestDTO.getAudio().getTts().getText().isEmpty() || 
                !StringUtils.hasText(requestDTO.getAudio().getTts().getText().get(0))) {
                log.error("[{}] 请求参数错误：TTS文本内容不能为空", methodName);
                return Result.ERROR("TTS文本内容不能为空");
            }
            if (!StringUtils.hasText(requestDTO.getAudio().getTts().getAudioMan())) {
                 log.error("[{}] 请求参数错误：TTS音色ID (audio_man) 不能为空", methodName);
                return Result.ERROR("TTS音色ID不能为空");
            }
        } else if ("audio".equalsIgnoreCase(requestDTO.getAudio().getType())) {
            if (!StringUtils.hasText(requestDTO.getAudio().getWavUrl())) {
                log.error("[{}] 请求参数错误：音频文件URL (wav_url) 不能为空", methodName);
                return Result.ERROR("音频文件URL不能为空");
            }
        } else {
            log.error("[{}] 请求参数错误：audio.type 无效，必须是 tts 或 audio", methodName);
            return Result.ERROR("无效的音频类型");
        }
        
        // 3. 构建禅境API请求参数
        CreateVideoRequest apiRequest = new CreateVideoRequest();
        // 使用BeanUtils或手动映射属性，这里为了清晰，手动映射
        apiRequest.setPerson(requestDTO.getPerson());
        apiRequest.setAudio(requestDTO.getAudio());
        apiRequest.setBgColor(requestDTO.getBgColor());
        apiRequest.setBg(requestDTO.getBg());
        apiRequest.setSubtitleConfig(requestDTO.getSubtitleConfig());
        apiRequest.setScreenWidth(requestDTO.getScreenWidth());
        apiRequest.setScreenHeight(requestDTO.getScreenHeight());
        apiRequest.setCallback(requestDTO.getCallback());

        // 4. 调用禅境API工具类
        try {
            CreateVideoResponse apiResponse = ChanJingApiUtil.createVideo(chanJingApi, apiRequest, secretKey);
            if (apiResponse.getCode() == 0){
                log.info("[{}] 创建禅境视频合成任务成功，任务ID: {}", methodName, apiResponse.getData());
                return Result.SUCCESS(apiResponse.getData()); // 返回视频任务ID
            } else {
                return Result.ERROR("创建视频合成任务失败(API)：" + apiResponse.getCode()+"--"+apiResponse.getMsg());
            }
        } catch (ChanJingApiException e) {
            log.error("[{}] 禅境API业务错误 - Code: {}, Message: {}, TraceID: {}",
                methodName, e.getErrorCode(), e.getMessage(), e.getTraceId(), e);
            return Result.ERROR("创建视频合成任务失败(API)：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("[{}] 调用禅境工具类参数错误: {}", methodName, e.getMessage(), e);
            return Result.ERROR("创建视频合成任务配置错误（参数）：" + e.getMessage());
        } catch (Exception e) {
            log.error("[{}] 创建禅境视频合成任务过程发生未预料的异常", methodName, e);
            return Result.ERROR("创建视频合成任务失败（系统）：" + e.getMessage());
        }
    }

    @Override
    public Result<CustomisedPersonResponse> customisedPerson(String avatarVideoId) {
        String methodName = "customisedPerson";
        log.info("[{}] 开始拉取禅境定制数字人详情，id: {}", avatarVideoId);

        // 2. 构建禅境API请求参数
        CustomisedPersonRequest apiRequest = new CustomisedPersonRequest();
        apiRequest.setId(avatarVideoId); // 默认第一页

        // 3. 调用禅境API工具类
        try {
            CustomisedPersonResponse apiResponse = ChanJingApiUtil.customisedPerson(chanJingApi, apiRequest, secretKey);
            log.info("[{}] 拉取禅境定制数字人列表成功", methodName);
            // 将结果序列化为JSON字符串
            return Result.SUCCESS(apiResponse);
        } catch (ChanJingApiException e) {
            log.error("[{}] 禅境API业务错误 - Code: {}, Message: {}, TraceID: {}",
                    methodName, e.getErrorCode(), e.getMessage(), e.getTraceId(), e);
            return Result.ERROR("拉取禅境数字人列表失败(API)：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("[{}] 调用禅境工具类参数错误: {}", methodName, e.getMessage(), e);
            return Result.ERROR("拉取禅境数字人列表配置错误（参数）：" + e.getMessage());
        } catch (Exception e) { // 包括 JsonProcessingException
            log.error("[{}] 拉取禅境定制数字人列表过程发生未预料的异常", methodName, e);
            return Result.ERROR("拉取禅境数字人列表失败（系统）：" + e.getMessage());
        }
    }

    @Override
    public Result<GetVideoDetailResponse> getVideoDetail(String videoId) {
        String methodName = "getVideoDetail";
        log.info("[{}] 开始拉取禅境视频详情，id: {}", videoId);

        // 3. 调用禅境API工具类
        try {
            GetVideoDetailResponse apiResponse = ChanJingApiUtil.getVideoDetail(chanJingApi, videoId, secretKey);
            log.info("[{}] 拉取禅境视频详情成功", methodName);
            // 将结果序列化为JSON字符串
            return Result.SUCCESS(apiResponse);
        } catch (ChanJingApiException e) {
            log.error("[{}] 禅境API业务错误 - Code: {}, Message: {}, TraceID: {}",
                    methodName, e.getErrorCode(), e.getMessage(), e.getTraceId(), e);
            return Result.ERROR("拉取禅境数字人列表失败(API)：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("[{}] 调用禅境工具类参数错误: {}", methodName, e.getMessage(), e);
            return Result.ERROR("拉取禅境数字人列表配置错误（参数）：" + e.getMessage());
        } catch (Exception e) { // 包括 JsonProcessingException
            log.error("[{}] 拉取禅境定制数字人列表过程发生未预料的异常", methodName, e);
            return Result.ERROR("拉取禅境数字人列表失败（系统）：" + e.getMessage());
        }
    }
} 