package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.MicrosoftParams;
import com.nacos.entity.dto.MinimaxParams;
import com.nacos.entity.dto.TtsParams;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.result.Result;
import com.nacos.service.AsyncDigitalAudioService;
import com.nacos.service.DigitalAudioService;
import com.nacos.service.DigitalNotificationService;
import com.nacos.service.FeiyongService;
import com.nacos.service.IDigitalAudioTaskService;
import com.nacos.model.AzureAudio.AzureAudioApiUtil;
import com.nacos.model.AzureAudio.model.TextToSpeechRequestBO;
import com.nacos.model.AzureAudio.model.TextToSpeechResponseBO;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationResponseBO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 异步数字音频服务实现类
 * 负责处理音频生成任务的异步执行
 * 
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncDigitalAudioServiceImpl implements AsyncDigitalAudioService {

    private final IDigitalAudioTaskService digitalAudioTaskService;
    private final DigitalNotificationService digitalNotificationService;
    private final ObjectMapper objectMapper;
    private final FeiyongService feiyongService;
    
    @Value("${azure.speech.subscription-key:}")
    private String azureSubscriptionKey;
    
    @Value("${azure.speech.region:}")
    private String azureRegion;

    // 从配置文件读取线程池大小，默认为5
    @Value("${digital.audio.thread-pool-size:5}")
    private int threadPoolSize;
    
    // 从配置文件读取任务批处理数量，默认为10
    @Value("${digital.audio.batch-size:10}")
    private int taskBatchSize;
    
    // 从配置文件读取任务超时时间（分钟），默认为5分钟
    @Value("${digital.audio.timeout-minutes:5}")
    private int taskTimeoutMinutes;

    private ExecutorService taskExecutor;
    
    // 初始化线程池
    public void init() {
        log.info("初始化音频生成任务线程池，大小: {}", threadPoolSize);
        taskExecutor = Executors.newFixedThreadPool(threadPoolSize);
    }

    /**
     * 异步处理音频生成任务
     * 
     * @param taskPO 任务对象
     */
    @Override
    @Async
    public void processAudioGenerationAsync(DigitalAudioTaskPO taskPO) {
        String methodName = "processAudioGenerationAsync";
        String taskId = taskPO.getTaskId();
        String provider = taskPO.getProvider();
        boolean koufeiResult = false;
        long logId = IdWorker.getId();
        
        try {

            log.info("[{}] 开始异步处理音频生成任务: taskId={}, provider={}", methodName, taskId, provider);

            // 1. 更新任务状态为处理中
            boolean updated = digitalAudioTaskService.updateTaskStatus(taskId, 1, null);
            if (!updated) {
                log.error("[{}] 更新任务状态失败: taskId={}", methodName, taskId);
                return;
            }

            // 2. 发送任务开始处理通知
            digitalNotificationService.sendAudioGenerationStartNotification(taskPO);

            // 2. 根据服务商类型处理
            String audioUrl = null;
            Integer durationMs = null;//音频长度
            
            if ("MINIMAX".equals(provider)) {
                // 处理MiniMax服务商
                Result<DigitalAudioVO> result = processMiniMaxAudio(taskPO);
                if (result.isSuccess() && result.getData() != null) {
                    audioUrl = result.getData().getAudioUrl();
                    durationMs = result.getData().getAudioLength() != null ?
                            result.getData().getAudioLength().intValue() : null;
                    log.info("[{}] MiniMax音频生成成功: taskId={}, audioUrl={}", methodName, taskId, audioUrl);
                } else {
                    throw new RuntimeException("MiniMax音频生成失败: " + result.getMessage());
                }
            } else if ("MICROSOFT".equals(provider)) {
                // 处理Microsoft服务商
                Result<DigitalAudioVO> result = processMicrosoftAudio(taskPO);
                if (result.isSuccess() && result.getData() != null) {
                    audioUrl = result.getData().getAudioUrl();
                    durationMs = result.getData().getAudioLength() != null ?
                            result.getData().getAudioLength().intValue() : null;
                    log.info("[{}] Microsoft音频生成成功: taskId={}, audioUrl={}", methodName, taskId, audioUrl);
                } else {
                    throw new RuntimeException("Microsoft音频生成失败: " + result.getMessage());
                }
            } else {
                throw new RuntimeException("不支持的服务商: " + provider);
            }

            // 3. 更新任务完成状态
            boolean completed = digitalAudioTaskService.completeTask(taskId, audioUrl, durationMs);
            if (completed) {
                log.info("[{}] 音频生成任务完成: taskId={}, audioUrl={}", methodName, taskId, audioUrl);
                // 获取音频长度并确保它是正值
                if (durationMs == null || durationMs <= 0) {
                    log.warn("[{}] 音频长度无效或为零: taskId={}, durationMs={}", methodName, taskId, durationMs);
                    durationMs = 60000; // 默认为1分钟
                }
                // 计算分钟数(向上取整)
                Integer multiplier = (int) Math.ceil(durationMs / 1000.0 / 60);
                if (multiplier <= 0) {
                    multiplier = 1; // 确保至少为1分钟
                }
                
                try {
                    // 扣费
                    koufeiResult = feiyongService.koufei(
                        Long.valueOf(taskPO.getUserId()),
                        logId,
                        "萤火虫生成音频扣费", 
                        DDUseRuleEnum.AUDIO_CREATE_PER_MIN.getRedisKey(),
                        multiplier
                    );
                    
                    if (koufeiResult) {
                        log.info("[{}] 音频生成扣费完成: taskId={}, userId={}, multiplier={}", 
                            methodName, taskId, taskPO.getUserId(), multiplier);
                            
                        // 只有扣费成功时才发送成功通知
                        digitalNotificationService.sendAudioGenerationSuccessNotification(taskPO, audioUrl, durationMs);
                    } else {
                        // 扣费失败，更新任务状态为失败
                        String errorMsg = "音频生成扣费失败，请检查点子余额或扣费配置";
                        log.error("[{}] {}: taskId={}", methodName, errorMsg, taskId);
                        digitalAudioTaskService.updateTaskStatus(taskId, 3, errorMsg);
                        
                        // 发送失败通知
                        digitalNotificationService.sendAudioGenerationFailureNotification(taskPO, errorMsg);
                    }
                } catch (Exception e) {
                    log.error("[{}] 音频生成扣费失败: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
                    
                    // 扣费异常，更新任务状态为失败
                    String errorMsg = "音频生成扣费异常: " + e.getMessage();
                    digitalAudioTaskService.updateTaskStatus(taskId, 3, errorMsg);
                    
                    // 发送失败通知
                    digitalNotificationService.sendAudioGenerationFailureNotification(taskPO, errorMsg);
                }
            } else {
                log.error("[{}] 更新任务完成状态失败: taskId={}", methodName, taskId);
                // 发送失败通知
                digitalNotificationService.sendAudioGenerationFailureNotification(taskPO, "更新任务完成状态失败");
            }

        } catch (Exception e) {
            log.error("[{}] 异步处理音频生成任务异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            
            // 只有在之前扣费成功的情况下才尝试退费
            if (koufeiResult) {
                try {
                    feiyongService.tuifei(
                        Long.valueOf(taskPO.getUserId()),
                        logId,
                        "萤火虫生成音频退费",
                        DDUseRuleEnum.AUDIO_CREATE_PER_MIN.getRedisKey()
                    );
                    log.info("[{}] 音频生成退费完成: taskId={}, userId={}", 
                        methodName, taskId, taskPO.getUserId());
                } catch (Exception ex) {
                    log.error("[{}] 音频生成退费失败: taskId={}, error={}", 
                        methodName, taskId, ex.getMessage(), ex);
                }
            }
            
            // 尝试从异常消息中提取错误码
            String errorCode = null;
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                // 匹配 "statusCode=数字" 模式
                Pattern pattern = Pattern.compile("statusCode=(\\d+)");
                Matcher matcher = pattern.matcher(errorMessage);
                if (matcher.find()) {
                    errorCode = matcher.group(1);
                }
            }
            
            // 根据错误码获取错误描述
            String detailedErrorMessage = null;
            if ("MINIMAX".equals(provider)) {
                detailedErrorMessage = provider + " API调用失败: " + 
                        (errorCode != null ? MiniMaxApiUtil.getErrorMessageByCode(errorCode) : e.getMessage());
            } else {
                detailedErrorMessage = provider + " API调用失败: " + e.getMessage();
            }
            
            // 更新任务失败状态
            digitalAudioTaskService.updateTaskStatus(taskId, 3, detailedErrorMessage);

            // 发送WebSocket失败通知
            digitalNotificationService.sendAudioGenerationFailureNotification(taskPO, detailedErrorMessage);
        }
    }

    /**
     * 处理MiniMax音频生成
     * 
     * @param taskPO 任务PO对象
     * @return 音频生成结果
     */
    private Result<DigitalAudioVO> processMiniMaxAudio(DigitalAudioTaskPO taskPO) {
        String methodName = "processMiniMaxAudio";
        try {
            // 反序列化参数为MiniMax参数
            MinimaxParams minimaxParams = objectMapper.readValue(
                    taskPO.getRequestParamsJson(), MinimaxParams.class);
                
            MiniMaxVoiceGenerationRequestBO requestBO = new MiniMaxVoiceGenerationRequestBO();
            requestBO.setModel(minimaxParams.getModel());
            requestBO.setText(minimaxParams.getText());
            requestBO.setStream(false);

            MiniMaxVoiceGenerationRequestBO.VoiceSetting voiceSetting = new MiniMaxVoiceGenerationRequestBO.VoiceSetting();
            voiceSetting.setVoiceId(minimaxParams.getVoiceId());
            voiceSetting.setSpeed(minimaxParams.getSpeed().floatValue());
            voiceSetting.setVol(minimaxParams.getVol().floatValue());
            voiceSetting.setPitch(minimaxParams.getPitch());
            voiceSetting.setEmotion(minimaxParams.getEmotion());
            voiceSetting.setLatexRead(minimaxParams.getLatexRead());
            requestBO.setVoiceSetting(voiceSetting);
            
            MiniMaxVoiceGenerationRequestBO.AudioSetting audioSetting = new MiniMaxVoiceGenerationRequestBO.AudioSetting();
            audioSetting.setFormat(minimaxParams.getAudioFormat());
            audioSetting.setSampleRate(minimaxParams.getSampleRate());
            audioSetting.setBitrate(minimaxParams.getBitRate());
            requestBO.setAudioSetting(audioSetting);
            
            String fileName = "task_" + taskPO.getTaskId();
            Result<MiniMaxVoiceGenerationResponseBO> result = MiniMaxApiUtil.generateAndProcessAudio(requestBO, taskPO.getUserId(), fileName, 5);
            if (!result.isSuccess() || result.getData() == null) {
                log.error("[{}] MiniMax音频生成失败: taskId={}, error={}",
                        methodName, taskPO.getTaskId(), result.getMessage());
                return Result.ERROR("MiniMax音频生成失败: " + result.getMessage());
            }
            MiniMaxVoiceGenerationResponseBO responseBO = result.getData();
            DigitalAudioVO audioVO = DigitalAudioVO.builder()
                    .audioName(taskPO.getTaskName())
                    .provider("MINIMAX")
                    .audioUrl(responseBO.getAudioUrl())
                    .audioLength(responseBO.getExtraInfo().getAudioLength())
                    .audioFormat(responseBO.getExtraInfo().getAudioFormat())
                    .build();
            return Result.SUCCESS("语音生成成功", audioVO);

        } catch (JsonProcessingException e) {
            log.error("[{}] 反序列化参数失败: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage());
            return Result.ERROR("参数解析失败");
        } catch (Exception e) {
            log.error("[{}] 处理MiniMax音频生成异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("MiniMax音频生成异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理Microsoft音频生成
     * 
     * @param taskPO 任务PO对象
     * @return 音频生成结果
     */
    private Result<DigitalAudioVO> processMicrosoftAudio(DigitalAudioTaskPO taskPO) {
        String methodName = "processMicrosoftAudio";
        try {
            if (azureSubscriptionKey == null || azureRegion == null ||
                azureSubscriptionKey.trim().isEmpty() || azureRegion.trim().isEmpty()) {
                log.error("[{}] Azure语音服务配置无效: taskId={}", methodName, taskPO.getTaskId());
                return Result.ERROR("Azure语音服务配置无效");
            }
            
            // 反序列化参数为微软参数
            MicrosoftParams microsoftParams = objectMapper.readValue(
                    taskPO.getRequestParamsJson(), MicrosoftParams.class);
                    
            TextToSpeechRequestBO azureRequest = new TextToSpeechRequestBO();
            azureRequest.setText(microsoftParams.getText());
            azureRequest.setVoiceName(microsoftParams.getVoiceId());
            azureRequest.setLanguage(microsoftParams.getLanguage());
            azureRequest.setOutputFormat(microsoftParams.getOutputFormat());
            azureRequest.setRate(microsoftParams.getRate().floatValue());
            azureRequest.setPitch(microsoftParams.getPitch().floatValue());
            
            String fileName = "task_" + taskPO.getTaskId();
            Result<TextToSpeechResponseBO> azureResult = AzureAudioApiUtil.textToSpeech(
                    azureRequest, fileName, azureSubscriptionKey, azureRegion, taskPO.getUserId());
            if (!azureResult.isSuccess() || azureResult.getData() == null) {
                log.error("[{}] Azure文本转语音失败: taskId={}, error={}",
                        methodName, taskPO.getTaskId(), azureResult.getMessage());
                return Result.ERROR("Azure文本转语音失败: " + azureResult.getMessage());
            }
            TextToSpeechResponseBO azureResponse = azureResult.getData();
            DigitalAudioVO audioVO = DigitalAudioVO.builder()
                    .audioName(taskPO.getTaskName())
                    .provider("MICROSOFT")
                    .audioUrl(azureResponse.getUrl())
                    .audioLength(azureResponse.getDuration())
                    .audioFormat(azureResponse.getFormat())
                    .build();
            log.info("[{}] Azure文本转语音成功: taskId={}, audioUrl={}",
                    methodName, taskPO.getTaskId(), audioVO.getAudioUrl());
            return Result.SUCCESS("语音生成成功", audioVO);
            
        } catch (JsonProcessingException e) {
            log.error("[{}] 反序列化参数失败: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage());
            return Result.ERROR("参数解析失败");
        } catch (Exception e) {
            log.error("[{}] 处理Microsoft音频生成异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("Microsoft音频生成异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理排队中的音频生成任务
     * 由定时任务调用
     * 1. 从数据库中获取状态为"排队中(0)"的任务，最多获取配置的批量大小
     * 2. 将任务状态更新为"进行中(1)"
     * 3. 将任务提交到线程池异步处理
     * 
     * 通过这种方式，系统可以控制同时处理的任务数量，防止资源过载
     */
    public void processQueuingAudioTasks() {
        String methodName = "processQueuingAudioTasks";
        
        // 确保线程池已初始化
        if (taskExecutor == null) {
            init();
        }
        
        // 使用配置的批处理大小
        List<DigitalAudioTaskPO> queuingTasks = digitalAudioTaskService.lambdaQuery()
                .eq(DigitalAudioTaskPO::getStatus, 0) // 排队中
                .eq(DigitalAudioTaskPO::getIsDeleted, false)
                .orderByAsc(DigitalAudioTaskPO::getCreatedTime)
                .last("LIMIT " + taskBatchSize) // 使用配置的批处理大小
                .list();

        if (queuingTasks.isEmpty()) {
            log.debug("[{}] 没有排队中的音频生成任务。", methodName);
            return;
        }

        log.info("[{}] 发现 {} 个排队中的音频生成任务，开始处理。", methodName, queuingTasks.size());
        for (DigitalAudioTaskPO task : queuingTasks) {
            taskExecutor.submit(() -> {
                try {
                    // 更新任务状态为进行中
                    digitalAudioTaskService.updateTaskStatus(task.getTaskId(), 1, null);
                    log.info("[{}] 任务ID: {} 状态更新为进行中。", methodName, task.getTaskId());

                    // 调用异步服务处理音频生成
                    processAudioGenerationAsync(task);

                } catch (Exception e) {
                    log.error("[{}] 处理排队中的音频任务 {} 失败: {}", methodName, task.getTaskId(), e.getMessage(), e);
                    // 标记任务为失败
                    digitalAudioTaskService.updateTaskStatus(task.getTaskId(), 3, "处理失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 处理超时的音频生成任务
     * 由定时任务调用
     */
    public void processTimeoutAudioTasks() {
        String methodName = "processTimeoutAudioTasks";
        
        // 使用配置的超时时间
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(taskTimeoutMinutes);

        List<DigitalAudioTaskPO> inProgressTasks = digitalAudioTaskService.lambdaQuery()
                .eq(DigitalAudioTaskPO::getStatus, 1) // 进行中
                .eq(DigitalAudioTaskPO::getIsDeleted, false)
                .lt(DigitalAudioTaskPO::getUpdateTime, timeoutThreshold) // 更新时间早于阈值
                .list();

        if (inProgressTasks.isEmpty()) {
            log.debug("[{}] 没有需要处理的超时音频生成任务。", methodName);
            return;
        }

        log.warn("[{}] 发现 {} 个超时音频生成任务，开始处理。", methodName, inProgressTasks.size());
        for (DigitalAudioTaskPO task : inProgressTasks) {
            try {
                log.warn("[{}] 任务ID: {} 已超时，将其标记为超时。", methodName, task.getTaskId());
                digitalAudioTaskService.updateTaskStatus(task.getTaskId(), 4, "任务处理超时"); // 标记为超时
                digitalNotificationService.sendAudioGenerationTimeoutNotification(task); // 发送超时通知
            } catch (Exception e) {
                log.error("[{}] 更新超时任务 {} 状态失败: {}", methodName, task.getTaskId(), e.getMessage(), e);
            }
        }
    }

    // 在应用程序关闭时优雅地关闭线程池
    public void shutdown() {
        String methodName = "shutdown";
        
        if (taskExecutor == null) {
            log.info("[{}] 音频生成任务线程池未初始化，无需关闭。", methodName);
            return;
        }
        
        log.info("[{}] 正在关闭音频生成任务线程池...", methodName);
        taskExecutor.shutdown();
        try {
            if (!taskExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                taskExecutor.shutdownNow();
                log.warn("[{}] 音频生成任务线程池未能及时关闭，强制关闭。", methodName);
            } else {
                log.info("[{}] 音频生成任务线程池已优雅关闭。", methodName);
            }
        } catch (InterruptedException e) {
            taskExecutor.shutdownNow();
            Thread.currentThread().interrupt();
            log.error("[{}] 音频生成任务线程池关闭被中断，强制关闭。", methodName);
        }
    }
}
