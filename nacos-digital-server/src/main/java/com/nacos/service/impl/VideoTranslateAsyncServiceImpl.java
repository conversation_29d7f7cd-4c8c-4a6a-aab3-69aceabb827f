package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.model.SoundView.enums.VideoTranslateStatusEnum;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.model.SoundView.SoundViewApiUtil;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateAsyncService;
import com.nacos.service.processor.VideoTranslateProcessor;
import com.nacos.config.VideoTranslateConfig;
import com.nacos.service.processor.VideoTranslateProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频翻译异步服务实现类
 * 负责处理视频翻译任务的异步执行，包括状态同步、进度监控等
 * 参考DigitalVideoAsyncServiceImpl的设计模式
 * 
 * <AUTHOR>
 * @since 2025-01-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoTranslateAsyncServiceImpl implements VideoTranslateAsyncService {

    // 依赖注入
    private final VideoTranslateTaskMapper videoTranslateTaskMapper;
    private final VideoTranslateProcessorFactory processorFactory;
    private final VideoTranslateConfig config;

    // 配置参数（从VideoTranslateConfig获取，保留@Value作为备用）
    @Value("${digital.video.translate.poll-interval-ms:5000}")
    private long pollInterval = 5000; // 轮询间隔，默认5秒

    @Value("${digital.video.translate.max-retries:60}")
    private int maxRetries = 60; // 最大重试次数，默认60次

    @Value("${digital.video.translate.global-timeout-minutes:30}")
    private int timeoutMinutes = 30; // 任务超时时间，默认30分钟

    @Value("${video.translate.batch-size:10}")
    private int batchSize = 10; // 批处理大小

    /**
     * 异步处理视频翻译任务
     * 主要负责任务的异步执行流程控制
     */
    @Async("videoTaskExecutor")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processTranslateTask(String taskId, String userId) {
        String methodName = "processTranslateTask";
        log.info("[{}] 开始异步处理视频翻译任务: taskId={}, userId={}", methodName, taskId, userId);

        try {
            // 1. 获取任务信息
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getTaskId, taskId)
                    .eq(VideoTranslateTaskPO::getUserId, userId)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
            );

            if (taskPO == null) {
                log.error("[{}] 任务不存在: taskId={}", methodName, taskId);
                return;
            }

            // 2. 检查任务状态
            if (!VideoTranslateStatusEnum.SUBMITTED.getCode().equals(taskPO.getStatus())) {
                log.warn("[{}] 任务状态不正确，跳过处理: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                return;
            }

            // 3. 获取处理器并提交任务
            String provider = config.getDefaultProvider(); // 从配置获取默认服务商
            Optional<VideoTranslateProcessor> processorOpt = processorFactory.getProcessor(provider);

            if (!processorOpt.isPresent()) {
                // 尝试获取健康的备用处理器
                List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
                if (healthyProcessors.isEmpty()) {
                    handleTaskFailure(taskPO, "没有可用的视频翻译处理器");
                    return;
                }
                processorOpt = Optional.of(healthyProcessors.get(0));
                log.warn("[{}] 指定处理器{}不可用，使用备用处理器: {}", methodName, provider, processorOpt.get().getProviderName());
            }

            VideoTranslateProcessor processor = processorOpt.get();
            log.info("[{}] 使用处理器: {}", methodName, processor.getProviderName());

            // 构建请求DTO
            VideoTranslateRequestDTO requestDTO = buildRequestDTO(taskPO);

            // 提交任务到处理器
            Result<VideoTranslateProcessor.VideoTranslateResult> submitResultWrapper = processor.processRequest(requestDTO, userId);
            if (!submitResultWrapper.isSuccess() || submitResultWrapper.getData() == null) {
                handleTaskFailure(taskPO, "提交到" + processor.getProviderName() + "失败: " + submitResultWrapper.getMessage());
                return;
            }

            VideoTranslateProcessor.VideoTranslateResult submitResult = submitResultWrapper.getData();
            if (submitResult.getTaskId() == null) {
                handleTaskFailure(taskPO, "提交到" + processor.getProviderName() + "失败: 返回任务ID为空");
                return;
            }

            // 4. 更新任务状态为处理中
            taskPO.setLingyangTaskId(submitResult.getProviderTaskId()); // 使用现有字段存储提供商任务ID
            taskPO.setStatus(VideoTranslateStatusEnum.PROCESSING.getCode());
            taskPO.setStartTime(LocalDateTime.now());
            taskPO.setUpdateTime(LocalDateTime.now());
            videoTranslateTaskMapper.updateById(taskPO);

            // 5. 推送状态通知
            pushTaskStatusNotification(userId, taskId, VideoTranslateStatusEnum.PROCESSING.getCode(), "任务处理中");

            // 6. 开始轮询任务状态
            pollTaskStatusWithProcessor(processor, taskPO, userId);

        } catch (Exception e) {
            log.error("[{}] 异步处理任务异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            
            // 获取任务对象进行失败处理
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getTaskId, taskId)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
            );
            
            if (taskPO != null) {
                handleTaskFailure(taskPO, "异步处理异常: " + e.getMessage());
            }
        }
    }

    /**
     * 提交视频翻译任务到羚羊平台
     */
    @Override
    public Result<Map<String, Object>> submitTranslateTaskToLingyang(VideoTranslateTaskPO taskPO) {
        String methodName = "submitTranslateTaskToLingyang";
        try {
            log.info("[{}] 提交任务到羚羊平台: taskId={}", methodName, taskPO.getTaskId());

            // 调用羚羊平台API
            Result<Map<String, Object>> result = SoundViewApiUtil.submitVideoTranslation(
                taskPO.getSourceVideoUrl(),
                taskPO.getSourceLanguage(),
                taskPO.getTargetLanguage(),
                taskPO.getVoiceId(),
                taskPO.getTaskName(),
                taskPO.getUserId()
            );

            if (result.isSuccess()) {
                log.info("[{}] 提交羚羊平台成功: taskId={}, lingyangTaskId={}", 
                    methodName, taskPO.getTaskId(), result.getData().get("lingyangTaskId"));
            } else {
                log.error("[{}] 提交羚羊平台失败: taskId={}, error={}", 
                    methodName, taskPO.getTaskId(), result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 提交羚羊平台异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("提交羚羊平台异常: " + e.getMessage());
        }
    }

    /**
     * 轮询检查任务状态
     */
    @Override
    public void pollTaskStatus(String lingyangTaskId, VideoTranslateTaskPO taskPO, String userId) {
        String methodName = "pollTaskStatus";
        log.info("[{}] 开始轮询任务状态: taskId={}, lingyangTaskId={}", methodName, taskPO.getTaskId(), lingyangTaskId);

        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                // 等待轮询间隔
                Thread.sleep(pollInterval);
                retryCount++;

                // 查询羚羊平台任务状态
                Result<Map<String, Object>> statusResult = SoundViewApiUtil.getTaskStatus(lingyangTaskId);
                
                if (!statusResult.isSuccess()) {
                    log.warn("[{}] 查询状态失败: taskId={}, retry={}/{}, error={}", 
                        methodName, taskPO.getTaskId(), retryCount, maxRetries, statusResult.getMessage());
                    continue;
                }

                Map<String, Object> statusData = statusResult.getData();
                Boolean isCompleted = (Boolean) statusData.get("isCompleted");
                Boolean isFailed = (Boolean) statusData.get("isFailed");
                String status = (String) statusData.get("status");

                log.info("[{}] 查询状态成功: taskId={}, status={}, retry={}/{}", 
                    methodName, taskPO.getTaskId(), status, retryCount, maxRetries);

                // 更新本地任务状态
                syncTaskStatusFromResult(taskPO, statusData);

                // 检查是否为最终状态
                if (Boolean.TRUE.equals(isCompleted)) {
                    handleTaskCompletion(taskPO, statusData);
                    pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.COMPLETED.getCode(), "任务完成");
                    return;
                } else if (Boolean.TRUE.equals(isFailed)) {
                    String errorMessage = (String) statusData.get("errorMessage");
                    handleTaskFailure(taskPO, "羚羊平台处理失败: " + errorMessage);
                    pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.FAILED.getCode(), "任务失败");
                    return;
                }

                // 推送进度通知
                Integer progress = (Integer) statusData.get("progress");
                String statusDesc = (String) statusData.get("statusDesc");
                pushTaskStatusNotification(userId, taskPO.getTaskId(), status, statusDesc + " (" + progress + "%)");

            } catch (InterruptedException e) {
                log.warn("[{}] 轮询被中断: taskId={}", methodName, taskPO.getTaskId());
                Thread.currentThread().interrupt();
                return;
            } catch (Exception e) {
                log.error("[{}] 轮询异常: taskId={}, retry={}/{}, error={}", 
                    methodName, taskPO.getTaskId(), retryCount, maxRetries, e.getMessage(), e);
            }
        }

        // 超过最大重试次数，标记为超时
        log.error("[{}] 任务轮询超时: taskId={}, maxRetries={}", methodName, taskPO.getTaskId(), maxRetries);
        handleTaskFailure(taskPO, "任务处理超时");
        pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.TIMEOUT.getCode(), "任务超时");
    }

    /**
     * 从查询结果同步任务状态
     */
    private void syncTaskStatusFromResult(VideoTranslateTaskPO taskPO, Map<String, Object> statusData) {
        try {
            taskPO.setProgress((Integer) statusData.get("progress"));
            taskPO.setResultVideoUrl((String) statusData.get("resultVideoUrl"));
            taskPO.setSubtitleUrl((String) statusData.get("subtitleUrl"));
            taskPO.setOriginalText((String) statusData.get("originalText"));
            taskPO.setTranslatedText((String) statusData.get("translatedText"));
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

        } catch (Exception e) {
            log.error("同步任务状态异常: taskId={}, error={}", taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 处理排队中的翻译任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            log.debug("[{}] 开始处理排队中的翻译任务", methodName);

            // 查询排队中的任务
            List<VideoTranslateTaskPO> queueingTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getStatus, VideoTranslateStatusEnum.SUBMITTED.getCode())
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .orderByAsc(VideoTranslateTaskPO::getCreatedTime)
                    .last("LIMIT " + batchSize)
            );

            if (queueingTasks.isEmpty()) {
                log.debug("[{}] 没有排队中的翻译任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个排队中的翻译任务", methodName, queueingTasks.size());

            // 异步处理每个任务
            for (VideoTranslateTaskPO task : queueingTasks) {
                try {
                    processTranslateTask(task.getTaskId(), task.getUserId());
                } catch (Exception e) {
                    log.error("[{}] 处理排队任务异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("[{}] 处理排队任务异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 处理超时的翻译任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        try {
            log.debug("[{}] 开始处理超时的翻译任务", methodName);

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询超时的任务
            List<VideoTranslateTaskPO> timeoutTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getStatus, VideoTranslateStatusEnum.PROCESSING.getCode())
                    .lt(VideoTranslateTaskPO::getStartTime, timeoutThreshold)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .last("LIMIT " + batchSize)
            );

            if (timeoutTasks.isEmpty()) {
                log.debug("[{}] 没有超时的翻译任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个超时的翻译任务", methodName, timeoutTasks.size());

            // 处理超时任务
            for (VideoTranslateTaskPO task : timeoutTasks) {
                try {
                    handleTaskFailure(task, "任务处理超时");
                    pushTaskStatusNotification(task.getUserId(), task.getTaskId(),
                        VideoTranslateStatusEnum.TIMEOUT.getCode(), "任务超时");
                } catch (Exception e) {
                    log.error("[{}] 处理超时任务异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 同步单个任务状态
     */
    @Override
    public Result<Map<String, Object>> syncTaskStatus(VideoTranslateTaskPO taskPO) {
        String methodName = "syncTaskStatus";
        try {
            log.info("[{}] 同步任务状态: taskId={}", methodName, taskPO.getTaskId());

            if (taskPO.getLingyangTaskId() == null) {
                return Result.ERROR("提供商任务ID为空，无法同步状态");
            }

            // 获取处理器（从配置获取默认服务商，后续可以从任务记录中获取实际使用的处理器）
            String provider = config.getDefaultProvider(); // 从配置获取默认服务商
            Optional<VideoTranslateProcessor> processorOpt = processorFactory.getProcessor(provider);

            if (!processorOpt.isPresent()) {
                // 尝试使用健康的处理器
                List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
                if (healthyProcessors.isEmpty()) {
                    return Result.ERROR("没有可用的视频翻译处理器");
                }
                processorOpt = Optional.of(healthyProcessors.get(0));
                log.warn("[{}] 指定处理器{}不可用，使用备用处理器: {}", methodName, provider, processorOpt.get().getProviderName());
            }

            VideoTranslateProcessor processor = processorOpt.get();

            // 使用处理器查询状态
            Result<VideoTranslateProcessor.VideoTranslateResult> statusResultWrapper = processor.checkTaskStatus(taskPO.getLingyangTaskId());

            if (!statusResultWrapper.isSuccess() || statusResultWrapper.getData() == null) {
                return Result.ERROR("查询" + processor.getProviderName() + "状态失败: " + statusResultWrapper.getMessage());
            }

            // 更新本地状态
            VideoTranslateProcessor.VideoTranslateResult statusResult = statusResultWrapper.getData();
            syncTaskStatusFromProcessorResult(taskPO, statusResult);

            // 转换为Map格式返回（兼容现有接口）
            Map<String, Object> statusData = convertToMap(statusResult);

            log.info("[{}] 同步任务状态成功: taskId={}", methodName, taskPO.getTaskId());
            return Result.SUCCESS(statusData);

        } catch (Exception e) {
            log.error("[{}] 同步任务状态异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("同步任务状态异常: " + e.getMessage());
        }
    }

    /**
     * 批量同步任务状态
     */
    @Override
    public Result<Map<String, Object>> batchSyncTaskStatus(List<VideoTranslateTaskPO> taskList) {
        String methodName = "batchSyncTaskStatus";
        try {
            log.info("[{}] 批量同步任务状态: count={}", methodName, taskList.size());

            Map<String, Object> result = new HashMap<>();
            int successCount = 0;
            int failCount = 0;

            for (VideoTranslateTaskPO task : taskList) {
                try {
                    Result<Map<String, Object>> syncResult = syncTaskStatus(task);
                    if (syncResult.isSuccess()) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("[{}] 批量同步单个任务异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                    failCount++;
                }
            }

            result.put("totalCount", taskList.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);

            log.info("[{}] 批量同步完成: total={}, success={}, fail={}", methodName, taskList.size(), successCount, failCount);
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 批量同步任务状态异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("批量同步异常: " + e.getMessage());
        }
    }

    /**
     * 处理任务完成逻辑
     */
    @Override
    public Result<String> handleTaskCompletion(VideoTranslateTaskPO taskPO, Map<String, Object> resultData) {
        String methodName = "handleTaskCompletion";
        try {
            log.info("[{}] 处理任务完成: taskId={}", methodName, taskPO.getTaskId());

            // 更新任务状态为完成
            taskPO.setStatus(VideoTranslateStatusEnum.COMPLETED.getCode());
            taskPO.setProgress(100);
            taskPO.setFinishTime(LocalDateTime.now());
            taskPO.setUpdateTime(LocalDateTime.now());

            // 更新结果数据
            if (resultData != null) {
                taskPO.setResultVideoUrl((String) resultData.get("resultVideoUrl"));
                taskPO.setSubtitleUrl((String) resultData.get("subtitleUrl"));
                taskPO.setOriginalText((String) resultData.get("originalText"));
                taskPO.setTranslatedText((String) resultData.get("translatedText"));
            }

            videoTranslateTaskMapper.updateById(taskPO);

            log.info("[{}] 任务完成处理成功: taskId={}", methodName, taskPO.getTaskId());
            return Result.SUCCESS("任务完成处理成功");

        } catch (Exception e) {
            log.error("[{}] 处理任务完成异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("处理任务完成异常: " + e.getMessage());
        }
    }

    /**
     * 处理任务失败逻辑
     */
    @Override
    public Result<String> handleTaskFailure(VideoTranslateTaskPO taskPO, String errorMessage) {
        String methodName = "handleTaskFailure";
        try {
            log.info("[{}] 处理任务失败: taskId={}, error={}", methodName, taskPO.getTaskId(), errorMessage);

            // 更新任务状态为失败
            taskPO.setStatus(VideoTranslateStatusEnum.FAILED.getCode());
            taskPO.setProgress(-1);
            taskPO.setErrorMessage(errorMessage);
            taskPO.setFinishTime(LocalDateTime.now());
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

            log.info("[{}] 任务失败处理成功: taskId={}", methodName, taskPO.getTaskId());
            return Result.SUCCESS("任务失败处理成功");

        } catch (Exception e) {
            log.error("[{}] 处理任务失败异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("处理任务失败异常: " + e.getMessage());
        }
    }

    /**
     * 取消正在进行的任务
     */
    @Override
    public Result<String> cancelTask(VideoTranslateTaskPO taskPO, String userId) {
        String methodName = "cancelTask";
        try {
            log.info("[{}] 取消任务: taskId={}, userId={}", methodName, taskPO.getTaskId(), userId);

            // 检查任务状态是否可以取消
            VideoTranslateStatusEnum currentStatus = VideoTranslateStatusEnum.getByCode(taskPO.getStatus());
            if (currentStatus == null || !currentStatus.isCancellable()) {
                return Result.ERROR("任务当前状态不允许取消");
            }

            // 如果有羚羊任务ID，尝试取消羚羊平台的任务
            if (taskPO.getLingyangTaskId() != null) {
                Result<String> cancelResult = SoundViewApiUtil.cancelTranslationTask(taskPO.getLingyangTaskId(), userId);
                if (!cancelResult.isSuccess()) {
                    log.warn("[{}] 取消羚羊平台任务失败: taskId={}, error={}", methodName, taskPO.getTaskId(), cancelResult.getMessage());
                }
            }

            // 更新本地任务状态为已取消
            taskPO.setStatus(VideoTranslateStatusEnum.CANCELLED.getCode());
            taskPO.setProgress(-1);
            taskPO.setErrorMessage("用户取消任务");
            taskPO.setFinishTime(LocalDateTime.now());
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

            log.info("[{}] 任务取消成功: taskId={}", methodName, taskPO.getTaskId());
            return Result.SUCCESS("任务取消成功");

        } catch (Exception e) {
            log.error("[{}] 取消任务异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("取消任务异常: " + e.getMessage());
        }
    }

    /**
     * 重试失败的任务
     */
    @Override
    public Result<String> retryTask(VideoTranslateTaskPO taskPO, String userId) {
        String methodName = "retryTask";
        try {
            log.info("[{}] 重试任务: taskId={}, userId={}", methodName, taskPO.getTaskId(), userId);

            // 检查任务状态是否可以重试
            VideoTranslateStatusEnum currentStatus = VideoTranslateStatusEnum.getByCode(taskPO.getStatus());
            if (currentStatus == null || !currentStatus.isRetryable()) {
                return Result.ERROR("任务当前状态不允许重试");
            }

            // 重置任务状态
            taskPO.setStatus(VideoTranslateStatusEnum.SUBMITTED.getCode());
            taskPO.setProgress(0);
            taskPO.setErrorMessage(null);
            taskPO.setLingyangTaskId(null);
            taskPO.setStartTime(null);
            taskPO.setFinishTime(null);
            taskPO.setResultVideoUrl(null);
            taskPO.setSubtitleUrl(null);
            taskPO.setOriginalText(null);
            taskPO.setTranslatedText(null);
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

            // 异步重新处理任务
            processTranslateTask(taskPO.getTaskId(), userId);

            log.info("[{}] 任务重试成功: taskId={}", methodName, taskPO.getTaskId());
            return Result.SUCCESS("任务重试成功");

        } catch (Exception e) {
            log.error("[{}] 重试任务异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("重试任务异常: " + e.getMessage());
        }
    }

    /**
     * 获取任务进度信息
     */
    @Override
    public Result<Map<String, Object>> getTaskProgress(VideoTranslateTaskPO taskPO) {
        String methodName = "getTaskProgress";
        try {
            log.debug("[{}] 获取任务进度: taskId={}", methodName, taskPO.getTaskId());

            // 基础进度计算
            Map<String, Object> progressData = new HashMap<>();
            progressData.put("taskId", taskPO.getTaskId());
            progressData.put("status", taskPO.getStatus());
            progressData.put("progress", taskPO.getProgress());
            progressData.put("progressDesc", taskPO.getErrorMessage());
            progressData.put("startTime", taskPO.getStartTime());
            progressData.put("updateTime", taskPO.getUpdateTime());

            // 计算预估完成时间
            if (taskPO.getProgress() != null && taskPO.getProgress() > 0 && taskPO.getStartTime() != null) {
                long elapsedSeconds = java.time.Duration.between(taskPO.getStartTime(), LocalDateTime.now()).getSeconds();
                if (elapsedSeconds > 0) {
                    long estimatedTotalSeconds = (long) (elapsedSeconds * 100.0 / taskPO.getProgress());
                    long remainingSeconds = estimatedTotalSeconds - elapsedSeconds;
                    progressData.put("estimatedRemainingTime", remainingSeconds);
                    progressData.put("estimatedCompletionTime", LocalDateTime.now().plusSeconds(remainingSeconds));
                }
            }

            return Result.SUCCESS(progressData);

        } catch (Exception e) {
            log.error("[{}] 获取任务进度异常: taskId={}, error={}", methodName, taskPO.getTaskId(), e.getMessage(), e);
            return Result.ERROR("获取任务进度异常: " + e.getMessage());
        }
    }

    /**
     * 推送任务状态变更通知
     */
    @Override
    public void pushTaskStatusNotification(String userId, String taskId, String status, String message) {
        String methodName = "pushTaskStatusNotification";
        try {
            log.debug("[{}] 推送状态通知: userId={}, taskId={}, status={}, message={}",
                methodName, userId, taskId, status, message);

            // 构建通知数据
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "VIDEO_TRANSLATE_STATUS");
            notification.put("userId", userId);
            notification.put("taskId", taskId);
            notification.put("status", status);
            notification.put("message", message);
            notification.put("timestamp", System.currentTimeMillis());

            // 这里可以集成WebSocket推送、消息队列等通知机制
            // 示例：websocketService.sendToUser(userId, notification);
            // 示例：messageQueueService.sendNotification(notification);

            log.debug("[{}] 状态通知推送完成: userId={}, taskId={}", methodName, userId, taskId);

        } catch (Exception e) {
            log.error("[{}] 推送状态通知异常: userId={}, taskId={}, error={}",
                methodName, userId, taskId, e.getMessage(), e);
        }
    }

    /**
     * 检查系统健康状态
     */
    @Override
    public Result<Map<String, Object>> checkSystemHealth() {
        String methodName = "checkSystemHealth";
        try {
            log.debug("[{}] 检查系统健康状态", methodName);

            Map<String, Object> health = new HashMap<>();

            // 检查羚羊平台连接
            Result<Map<String, Object>> apiHealth = SoundViewApiUtil.checkSystemEnvironment();
            health.put("lingyangApiHealth", apiHealth.isSuccess());
            if (apiHealth.isSuccess()) {
                health.putAll(apiHealth.getData());
            }

            // 检查数据库连接
            try {
                long taskCount = videoTranslateTaskMapper.selectCount(null);
                health.put("databaseHealth", true);
                health.put("totalTaskCount", taskCount);
            } catch (Exception e) {
                health.put("databaseHealth", false);
                health.put("databaseError", e.getMessage());
            }

            // 检查各状态任务数量
            for (VideoTranslateStatusEnum status : VideoTranslateStatusEnum.values()) {
                try {
                    long count = videoTranslateTaskMapper.selectCount(
                        new LambdaQueryWrapper<VideoTranslateTaskPO>()
                            .eq(VideoTranslateTaskPO::getStatus, status.getCode())
                            .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    );
                    health.put("status_" + status.getCode() + "_count", count);
                } catch (Exception e) {
                    health.put("status_" + status.getCode() + "_error", e.getMessage());
                }
            }

            // 系统资源信息
            Runtime runtime = Runtime.getRuntime();
            health.put("jvmMaxMemory", runtime.maxMemory());
            health.put("jvmTotalMemory", runtime.totalMemory());
            health.put("jvmFreeMemory", runtime.freeMemory());
            health.put("jvmUsedMemory", runtime.totalMemory() - runtime.freeMemory());
            health.put("availableProcessors", runtime.availableProcessors());

            // 配置信息
            health.put("pollInterval", pollInterval);
            health.put("maxRetries", maxRetries);
            health.put("timeoutMinutes", timeoutMinutes);
            health.put("batchSize", batchSize);

            boolean overallHealth = (Boolean) health.getOrDefault("lingyangApiHealth", false) &&
                                   (Boolean) health.getOrDefault("databaseHealth", false);
            health.put("overallHealth", overallHealth);

            return Result.SUCCESS(health);

        } catch (Exception e) {
            log.error("[{}] 检查系统健康状态异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("检查系统健康状态异常: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @Override
    public Result<Map<String, Object>> getTaskStatistics(String userId) {
        String methodName = "getTaskStatistics";
        try {
            log.debug("[{}] 获取任务统计: userId={}", methodName, userId);

            Map<String, Object> statistics = new HashMap<>();

            LambdaQueryWrapper<VideoTranslateTaskPO> baseQuery = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                .eq(VideoTranslateTaskPO::getIsDeleted, 0);

            if (userId != null) {
                baseQuery.eq(VideoTranslateTaskPO::getUserId, userId);
            }

            // 总任务数
            long totalCount = videoTranslateTaskMapper.selectCount(baseQuery);
            statistics.put("totalCount", totalCount);

            // 各状态任务数量
            for (VideoTranslateStatusEnum status : VideoTranslateStatusEnum.values()) {
                LambdaQueryWrapper<VideoTranslateTaskPO> statusQuery = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .eq(VideoTranslateTaskPO::getStatus, status.getCode());

                if (userId != null) {
                    statusQuery.eq(VideoTranslateTaskPO::getUserId, userId);
                }

                long count = videoTranslateTaskMapper.selectCount(statusQuery);
                statistics.put(status.getCode() + "Count", count);
            }

            // 成功率计算
            long completedCount = (Long) statistics.getOrDefault("completedCount", 0L);
            long failedCount = (Long) statistics.getOrDefault("failedCount", 0L);
            long cancelledCount = (Long) statistics.getOrDefault("cancelledCount", 0L);
            long timeoutCount = (Long) statistics.getOrDefault("timeoutCount", 0L);

            long finishedCount = completedCount + failedCount + cancelledCount + timeoutCount;
            if (finishedCount > 0) {
                double successRate = (double) completedCount / finishedCount * 100;
                statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            } else {
                statistics.put("successRate", 0.0);
            }

            // 今日统计
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LambdaQueryWrapper<VideoTranslateTaskPO> todayQuery = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                .ge(VideoTranslateTaskPO::getCreatedTime, todayStart);

            if (userId != null) {
                todayQuery.eq(VideoTranslateTaskPO::getUserId, userId);
            }

            long todayCount = videoTranslateTaskMapper.selectCount(todayQuery);
            statistics.put("todayCount", todayCount);

            return Result.SUCCESS(statistics);

        } catch (Exception e) {
            log.error("[{}] 获取任务统计异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("获取任务统计异常: " + e.getMessage());
        }
    }

    /**
     * 同步所有处理中任务的状态
     */
    @Override
    public void syncProcessingTasksStatus() {
        String methodName = "syncProcessingTasksStatus";
        try {
            log.debug("[{}] 开始同步处理中任务状态", methodName);

            // 查询所有处理中的任务
            List<VideoTranslateTaskPO> processingTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getStatus, VideoTranslateStatusEnum.PROCESSING.getCode())
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .isNotNull(VideoTranslateTaskPO::getLingyangTaskId)
                    .orderByAsc(VideoTranslateTaskPO::getStartTime)
                    .last("LIMIT " + batchSize)
            );

            if (processingTasks.isEmpty()) {
                log.debug("[{}] 没有处理中的任务需要同步", methodName);
                return;
            }

            log.info("[{}] 找到{}个处理中的任务需要同步状态", methodName, processingTasks.size());

            // 批量同步状态
            Result<Map<String, Object>> batchResult = batchSyncTaskStatus(processingTasks);
            if (batchResult.isSuccess()) {
                Map<String, Object> result = batchResult.getData();
                log.info("[{}] 批量同步完成: total={}, success={}, fail={}",
                    methodName,
                    result.get("totalCount"),
                    result.get("successCount"),
                    result.get("failCount"));
            } else {
                log.error("[{}] 批量同步失败: {}", methodName, batchResult.getMessage());
            }

        } catch (Exception e) {
            log.error("[{}] 同步处理中任务状态异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 构建视频翻译请求DTO
     */
    private VideoTranslateRequestDTO buildRequestDTO(VideoTranslateTaskPO taskPO) {
        VideoTranslateRequestDTO requestDTO = new VideoTranslateRequestDTO();
        requestDTO.setUserId(taskPO.getUserId());
        requestDTO.setVideoUrl(taskPO.getSourceVideoUrl());
        requestDTO.setSourceLanguage(taskPO.getSourceLanguage());
        requestDTO.setTargetLanguage(taskPO.getTargetLanguage());
        requestDTO.setVoiceId(taskPO.getVoiceId());
        requestDTO.setTaskName(taskPO.getTaskName());

        // 设置扩展参数
        requestDTO.setExtParams(taskPO.getConfigParams());

        // 设置默认值
        requestDTO.setDefaults();

        return requestDTO;
    }

    /**
     * 使用处理器轮询任务状态
     */
    private void pollTaskStatusWithProcessor(VideoTranslateProcessor processor, VideoTranslateTaskPO taskPO, String userId) {
        String methodName = "pollTaskStatusWithProcessor";
        log.info("[{}] 开始轮询任务状态: taskId={}, processor={}", methodName, taskPO.getTaskId(), processor.getProviderName());

        // 使用配置中的参数，如果配置不可用则使用字段默认值
        long actualPollInterval = config != null ? config.getPollIntervalMs() : pollInterval;
        int actualMaxRetries = config != null ? config.getMaxRetries() : maxRetries;

        int retryCount = 0;
        while (retryCount < actualMaxRetries) {
            try {
                // 等待轮询间隔
                Thread.sleep(actualPollInterval);
                retryCount++;

                // 使用处理器查询任务状态
                Result<VideoTranslateProcessor.VideoTranslateResult> statusResultWrapper = processor.checkTaskStatus(taskPO.getLingyangTaskId());

                if (!statusResultWrapper.isSuccess() || statusResultWrapper.getData() == null) {
                    log.warn("[{}] 查询状态失败: taskId={}, retry={}/{}, error={}",
                        methodName, taskPO.getTaskId(), retryCount, actualMaxRetries, statusResultWrapper.getMessage());
                    continue;
                }

                VideoTranslateProcessor.VideoTranslateResult statusResult = statusResultWrapper.getData();
                String status = statusResult.getStatus();

                log.info("[{}] 查询状态成功: taskId={}, status={}, retry={}/{}",
                    methodName, taskPO.getTaskId(), status, retryCount, actualMaxRetries);

                // 更新本地任务状态
                syncTaskStatusFromProcessorResult(taskPO, statusResult);

                // 检查是否为最终状态
                if ("completed".equals(status)) {
                    handleTaskCompletion(taskPO, convertToMap(statusResult));
                    pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.COMPLETED.getCode(), "任务完成");
                    return;
                } else if ("failed".equals(status)) {
                    String errorMessage = statusResult.getErrorMessage();
                    handleTaskFailure(taskPO, processor.getProviderName() + "处理失败: " + errorMessage);
                    pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.FAILED.getCode(), "任务失败");
                    return;
                }

                // 推送进度通知
                Integer progress = statusResult.getProgress();
                String statusDesc = status + " (" + progress + "%)";
                pushTaskStatusNotification(userId, taskPO.getTaskId(), status, statusDesc);

            } catch (InterruptedException e) {
                log.warn("[{}] 轮询被中断: taskId={}", methodName, taskPO.getTaskId());
                Thread.currentThread().interrupt();
                return;
            } catch (Exception e) {
                log.error("[{}] 轮询异常: taskId={}, retry={}/{}, error={}",
                    methodName, taskPO.getTaskId(), retryCount, actualMaxRetries, e.getMessage(), e);
            }
        }

        // 超过最大重试次数，标记为超时
        log.error("[{}] 任务轮询超时: taskId={}, maxRetries={}", methodName, taskPO.getTaskId(), actualMaxRetries);
        handleTaskFailure(taskPO, "任务处理超时");
        pushTaskStatusNotification(userId, taskPO.getTaskId(), VideoTranslateStatusEnum.TIMEOUT.getCode(), "任务超时");
    }

    /**
     * 从处理器结果同步任务状态
     */
    private void syncTaskStatusFromProcessorResult(VideoTranslateTaskPO taskPO, VideoTranslateProcessor.VideoTranslateResult result) {
        try {
            taskPO.setProgress(result.getProgress());
            taskPO.setResultVideoUrl(result.getResultVideoUrl());
            taskPO.setUpdateTime(LocalDateTime.now());

            videoTranslateTaskMapper.updateById(taskPO);

        } catch (Exception e) {
            log.error("同步任务状态异常: taskId={}, error={}", taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 将VideoTranslateResult转换为Map格式（兼容现有方法）
     */
    private Map<String, Object> convertToMap(VideoTranslateProcessor.VideoTranslateResult result) {
        Map<String, Object> map = new HashMap<>();
        map.put("resultVideoUrl", result.getResultVideoUrl());
        map.put("progress", result.getProgress());
        map.put("status", result.getStatus());
        // 注意：VideoTranslateResult暂时没有subtitleUrl、originalText、translatedText字段
        // 这些字段可能需要从其他地方获取或在后续版本中添加到VideoTranslateResult
        map.put("subtitleUrl", null);
        map.put("originalText", null);
        map.put("translatedText", null);
        return map;
    }

}
