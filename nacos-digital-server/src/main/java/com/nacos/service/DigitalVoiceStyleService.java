package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.result.Result;
import java.util.List;

public interface DigitalVoiceStyleService extends IService<DigitalVoiceStylesPO> {
    
    /**
     * 同步音色信息
     * @return Result
     */
    Result<String> syncVoiceStyles();

    /**
     * 同步Minimax语音列表
     * @return Result
     */
    Result<String> syncMinimaxVoiceStyles();

    /**
     * 同步Microsoft语音列表
     * @return Result
     */
    Result<String> syncMicrosoftVoiceStyles();

    /**
     * 同步ElevenLabs语音列表
     * @return Result
     */
    Result<String> syncElevenLabsVoiceStyles();
    
    /**
     * 获取音色信息
     * @param voiceId 音色ID
     * @return VoiceStylesPO
     */
    DigitalVoiceStylesPO getVoiceInfo(String voiceId);
    
    /**
     * 获取所有音色信息
     * @return List<VoiceStylesPO>
     */
    List<DigitalVoiceStylesPO> getAllVoiceInfo();

    /**
     * 根据音色ID列表获取音色信息列表
     */
    List<DigitalVoiceStyleVO> getVoiceStylesByIds(List<String> voiceIds);
} 