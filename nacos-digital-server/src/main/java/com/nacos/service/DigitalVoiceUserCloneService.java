package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.dto.DigitalVoiceStyleCreateDTO;
import com.nacos.entity.po.DigitalVoiceUserClonePO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.result.Result;
import java.util.List;

public interface DigitalVoiceUserCloneService extends IService<DigitalVoiceUserClonePO> {
    /**
     * 获取用户音色列表
     *
     * @param userId 用户ID
     * @return Result<List<DigitalVoiceUserCloneVO>>
     */
    Result<List<DigitalVoiceUserCloneVO>> getUserVoiceList(String userId);
    
    /**
     * 删除用户音色
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @return Result<String>
     */
    Result<String> deleteUserVoice(String userId, String voiceId);
    
    /**
     * 修改用户音色名称
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @param voiceName 新的音色名称
     * @return Result<String>
     */
    Result<String> updateUserVoiceName(String userId, String voiceId, String voiceName);

    /**
     * 创建音色风格（MiniMax）
     * @param digitalVoiceStyleCreateDTO 创建音色风格DTO（包含用户ID）
     * @return Result<DigitalVoiceStyleVO>
     */
    Result<DigitalVoiceStyleVO> createVoiceStyle(DigitalVoiceStyleCreateDTO digitalVoiceStyleCreateDTO);

    /**
     * 创建音色风格（Microsoft）
     * @param digitalVoiceStyleCreateDTO 创建音色风格DTO（包含用户ID）
     * @return Result<DigitalVoiceStyleVO>
     */
    Result<DigitalVoiceStyleVO> createVoiceStyleMicroSoft(DigitalVoiceStyleCreateDTO digitalVoiceStyleCreateDTO);
} 