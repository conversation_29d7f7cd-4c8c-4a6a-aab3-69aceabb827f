package com.nacos.controller.admin;

import com.nacos.entity.vo.DigitalUserAvatarVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalAvatarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人数据管理Controller（Admin模块）
 * 专门处理数字人的CRUD操作，未来将迁移到admin微服务
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人数据管理（Admin）", description = "数字人CRUD操作，未来admin微服务核心功能")
@RestController
@RequestMapping("/admin/v1/avatars")
public class AvatarAdminController {
    
    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 获取数字人列表
     * URL: /admin/v1/avatars/list
     * Method: GET
     * 
     * @param userId 用户ID（可选）
     * @param page 页码（可选，默认1）
     * @param size 每页大小（可选，默认20）
     * @return 数字人列表
     */
    @Operation(summary = "获取数字人列表", description = "管理员获取数字人数据列表")
    @GetMapping("/list")
    public Result<List<DigitalUserAvatarVO>> listAvatars(
            @Parameter(description = "用户ID，可选") @RequestParam(required = false) String userId,
            @Parameter(description = "页码，默认1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小，默认20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("Admin获取数字人列表，userId：{}，page：{}，size：{}", userId, page, size);
        
        if (userId != null) {
            return digitalAvatarService.listAvatarsByUserId(userId);
        }
        return digitalAvatarService.listAvatars();
    }

    /**
     * 创建数字人
     * URL: /admin/v1/avatars
     * Method: POST
     * 
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "创建数字人", description = "管理员创建新的数字人记录")
    @PostMapping
    public Result<Boolean> createAvatar(@RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Admin创建数字人，参数：{}", avatarVO);
        return digitalAvatarService.addAvatar(avatarVO);
    }

    /**
     * 获取数字人详情
     * URL: /admin/v1/avatars/{id}
     * Method: GET
     * 
     * @param id 数字人ID
     * @return 数字人详情
     */
    @Operation(summary = "获取数字人详情", description = "管理员获取指定数字人的详细信息")
    @GetMapping("/{id}")
    public Result<DigitalUserAvatarVO> getAvatarDetail(@Parameter(description = "数字人ID") @PathVariable Long id) {
        log.info("Admin获取数字人详情，ID：{}", id);
        // TODO: 需要实现根据ID获取数字人详情的功能
        log.warn("数字人详情获取功能暂未实现");
        return Result.ERROR("数字人详情获取功能暂未实现");
    }

    /**
     * 更新数字人
     * URL: /admin/v1/avatars/{id}
     * Method: PUT
     * 
     * @param id 数字人ID
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "更新数字人", description = "管理员更新数字人信息")
    @PutMapping("/{id}")
    public Result<Boolean> updateAvatar(
            @Parameter(description = "数字人ID") @PathVariable Long id,
            @RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Admin更新数字人，ID：{}，参数：{}", id, avatarVO);
        avatarVO.setId(id); // 确保ID一致
        return digitalAvatarService.updateAvatar(avatarVO);
    }

    /**
     * 删除数字人
     * URL: /admin/v1/avatars/{id}
     * Method: DELETE
     * 
     * @param id 数字人ID
     * @return 操作结果
     */
    @Operation(summary = "删除数字人", description = "管理员删除指定数字人")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteAvatar(@Parameter(description = "数字人ID") @PathVariable Long id) {
        log.info("Admin删除数字人，ID：{}", id);
        return digitalAvatarService.deleteAvatar(id);
    }

    /**
     * 批量更新数字人状态
     * URL: /admin/v1/avatars/batch/status
     * Method: PUT
     * 
     * @param ids 数字人ID列表
     * @param status 状态：0-禁用，1-启用
     * @return 操作结果
     */
    @Operation(summary = "批量更新数字人状态", description = "管理员批量更新数字人状态")
    @PutMapping("/batch/status")
    public Result<Boolean> batchUpdateStatus(
            @Parameter(description = "数字人ID列表") @RequestParam List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam Integer status) {
        log.info("Admin批量更新数字人状态，IDs：{}，状态：{}", ids, status);
        
        // 批量更新逻辑
        for (Long id : ids) {
            digitalAvatarService.updateStatus(id, status);
        }
        return Result.SUCCESS(true);
    }

    /**
     * 根据组ID获取数字人列表
     * URL: /admin/v1/avatars/groups/{groupId}
     * Method: GET
     * 
     * @param groupId 组ID
     * @param userId 用户ID
     * @return 数字人列表
     */
    @Operation(summary = "根据组ID获取数字人列表", description = "管理员根据组ID获取数字人数据")
    @GetMapping("/groups/{groupId}")
    public Result<List<DigitalUserAvatarVO>> listAvatarsByGroup(
            @Parameter(description = "组ID") @PathVariable Long groupId,
            @Parameter(description = "用户ID") @RequestParam String userId) {
        log.info("Admin根据组ID获取数字人列表，组ID：{}，用户ID：{}", groupId, userId);
        return digitalAvatarService.listAvatarsByGroupId(groupId, userId);
    }
}
