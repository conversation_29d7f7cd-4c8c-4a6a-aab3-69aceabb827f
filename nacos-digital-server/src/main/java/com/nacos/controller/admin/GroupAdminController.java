package com.nacos.controller.admin;

import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.service.DigitalUserGroupService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户组数据管理Controller（Admin模块）
 * 专门处理用户组的CRUD操作，未来将迁移到admin微服务
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "用户组数据管理（Admin）", description = "用户组CRUD操作，未来admin微服务核心功能")
@RestController
@RequestMapping("/admin/v1/groups")
public class GroupAdminController {
    
    @Autowired
    private DigitalUserGroupService userGroupService;

    /**
     * 获取用户组列表
     * URL: /admin/v1/groups/list
     * Method: GET
     */
    @Operation(summary = "获取用户组列表", description = "管理员获取用户组数据列表")
    @GetMapping("/list")
    public Result<List<DigitalUserGroupVO>> listGroups(
            @Parameter(description = "用户ID，可选") @RequestParam(required = false) String userId) {
        log.info("Admin获取用户组列表，userId：{}", userId);
        
        if (userId != null) {
            return userGroupService.listUserGroups(userId);
        }
        // 如果没有提供userId，返回所有组（需要Service支持）
        return userGroupService.listUserGroups(null);
    }

    /**
     * 创建用户组
     * URL: /admin/v1/groups
     * Method: POST
     */
    @Operation(summary = "创建用户组", description = "管理员创建新的用户组")
    @PostMapping
    public Result<DigitalUserGroupVO> createGroup(@RequestBody DigitalUserGroupDTO groupDTO) {
        log.info("Admin创建用户组，参数：{}", groupDTO);
        return userGroupService.createGroup(groupDTO);
    }

    /**
     * 更新用户组
     * URL: /admin/v1/groups/{id}
     * Method: PUT
     */
    @Operation(summary = "更新用户组", description = "管理员更新用户组信息")
    @PutMapping("/{id}")
    public Result<Boolean> updateGroup(
            @Parameter(description = "组ID") @PathVariable Long id,
            @RequestBody DigitalUserGroupDTO groupDTO) {
        log.info("Admin更新用户组，ID：{}，参数：{}", id, groupDTO);
        // TODO: 需要实现根据数据库ID更新用户组的功能，DTO中没有id字段
        log.warn("用户组更新功能暂未实现，DTO中缺少id字段");
        return Result.ERROR("用户组更新功能暂未实现");
    }

    /**
     * 删除用户组
     * URL: /admin/v1/groups/{id}
     * Method: DELETE
     */
    @Operation(summary = "删除用户组", description = "管理员删除指定用户组")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteGroup(@Parameter(description = "组ID") @PathVariable Long id) {
        log.info("Admin删除用户组，ID：{}", id);
        return userGroupService.deleteGroup(id);
    }

    /**
     * 获取用户组详情
     * URL: /admin/v1/groups/{id}
     * Method: GET
     */
    @Operation(summary = "获取用户组详情", description = "管理员获取指定用户组的详细信息")
    @GetMapping("/{id}")
    public Result<DigitalUserGroupVO> getGroupDetail(@Parameter(description = "组ID") @PathVariable Long id) {
        log.info("Admin获取用户组详情，ID：{}", id);
        return userGroupService.getGroupDetail(id);
    }

    /**
     * 根据用户ID获取用户组
     * URL: /admin/v1/groups/users/{userId}
     * Method: GET
     */
    @Operation(summary = "根据用户ID获取用户组", description = "管理员根据用户ID获取其所有用户组")
    @GetMapping("/users/{userId}")
    public Result<List<DigitalUserGroupVO>> listGroupsByUserId(
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("Admin根据用户ID获取用户组，用户ID：{}", userId);
        return userGroupService.listUserGroups(userId);
    }
}
