package com.nacos.controller;

import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.dto.TextToSpeechFileRequestDTO;
import com.nacos.entity.dto.TextToSpeechFileResponseDTO;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalAudioService;
import com.nacos.service.IDigitalAudioTaskService;
import com.nacos.component.AudioGenerationRateLimiter;
import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/audio")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "数字音频", description = "数字音频生成相关接口")
public class DigitalAudioController {

    private final DigitalAudioService digitalAudioService;
    private final AudioGenerationRateLimiter rateLimiter;

    /**
     * 克隆音色 API
     * URL: /audio/voice_cloning
     * Method: POST
     * @param digitalVoiceCloningDTO
     * @param userId
     * @return
     */
    @PostMapping("/voice_cloning")
    public Result<String> voiceCloning(
            @RequestBody DigitalVoiceCloningDTO digitalVoiceCloningDTO,
            @RequestParam("userId") String userId
    ) {
        return digitalAudioService.voiceCloning(digitalVoiceCloningDTO, userId);
    }

    /**
     * 提交音频生成任务 API (异步)
     * URL: /audio/generate-async
     * Method: POST
     * @param audioGenerationRequestDTO
     * @return Result 包含 taskId
     */
    @PostMapping("/generate-async")
    public Result<String> submitAudioGenerationTask(
            @RequestBody @Valid AudioGenerationRequestDTO audioGenerationRequestDTO,
            @RequestParam("userId") String userId
    ) {
        return digitalAudioService.submitAudioGenerationTask(audioGenerationRequestDTO, userId);
    }

    /**
     * 音频生成 API（同步）
     * URL: /audio/generate
     * Method: POST
     * @param audioGenerationRequestDTO 音频生成请求参数
     * @param userId 用户ID
     * @param request HTTP请求对象，用于获取客户端IP
     * @return Result 包含音频生成结果
     */
    @PostMapping("/generate")
    @Operation(summary = "同步音频生成", description = "提供即时的音频生成服务，支持多服务商，包含限流控制")
    public Result<AudioGenerationResponseVO> generateAudioSync(
            @RequestBody @Valid AudioGenerationRequestDTO audioGenerationRequestDTO,
            @RequestParam("userId") String userId,
            HttpServletRequest request
    ) {
        String methodName = "generateAudioSync";
        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();

        log.info("[{}]开始同步音频生成请求，userId={}, provider={}, clientIp={}",
                methodName, userId, audioGenerationRequestDTO.getProvider(), clientIp);

        try {
            // 1. 参数验证
            if (audioGenerationRequestDTO == null) {
                log.warn("[{}]请求参数为空，userId={}, clientIp={}", methodName, userId, clientIp);
                return Result.ERROR("请求参数不能为空");
            }

            if (userId == null || userId.trim().isEmpty()) {
                log.warn("[{}]用户ID为空，clientIp={}", methodName, clientIp);
                return Result.ERROR("用户ID不能为空");
            }

            // // 2. 限流检查
            // if (!rateLimiter.tryAcquireForUser(userId, 3, TimeUnit.SECONDS)) {
            //     log.warn("[{}]限流触发，userId={}, clientIp={}", methodName, userId, clientIp);
            //     return Result.ERROR("请求过于频繁，请稍后重试");
            // }

            // 3. 调用服务层
            Result<AudioGenerationResponseVO> result = digitalAudioService.generateAudioSync(
                    audioGenerationRequestDTO, userId);

            long processingTime = System.currentTimeMillis() - startTime;

            if (result.isSuccess()) {
                log.info("[{}]同步音频生成成功，userId={}, provider={}, processingTime={}ms, clientIp={}",
                        methodName, userId, audioGenerationRequestDTO.getProvider(), processingTime, clientIp);
            } else {
                log.error("[{}]同步音频生成失败，userId={}, provider={}, error={}, processingTime={}ms, clientIp={}",
                        methodName, userId, audioGenerationRequestDTO.getProvider(),
                        result.getMessage(), processingTime, clientIp);
            }

            return result;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("[{}]同步音频生成异常，userId={}, provider={}, processingTime={}ms, clientIp={}, error={}",
                    methodName, userId, audioGenerationRequestDTO.getProvider(),
                    processingTime, clientIp, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 获取客户端真实IP地址
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个非unknown的有效IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * 测试获取声音id
     * URL：/audio/test/getvoice
     * Method: GET
     * @return
     */
    @GetMapping("/test/getvoice")
    public MiniMaxGetVoiceIdResponseBO testGetVoice() {
        return MiniMaxApiUtil.getVoiceId("all");
    }

}