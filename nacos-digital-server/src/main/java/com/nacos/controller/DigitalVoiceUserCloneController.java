package com.nacos.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.business.db.model.po.ConfigModelPO;
import com.nacos.entity.dto.DigitalVoiceStyleCreateDTO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.service.DigitalVoiceUserCloneService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "数字人音色克隆接口")
@RestController
@RequestMapping("/voice/clone")
@RequiredArgsConstructor
public class DigitalVoiceUserCloneController {

    private final DigitalVoiceUserCloneService digitalVoiceUserCloneService;

    /**
     * 获取用户音色列表
     * URL: /voice/clone/list
     * Method: GET
     * @param userId 用户ID
     * @return Result<List<DigitalVoiceUserCloneVO>>
     */
    @Operation(summary = "获取用户音色列表")
    @GetMapping("/list")
    public Result<List<DigitalVoiceUserCloneVO>> getUserVoiceList(
            @Parameter(description = "用户ID") @RequestParam String userId) {
        return digitalVoiceUserCloneService.getUserVoiceList(userId);
    }

    /**
     * 删除用户音色
     * URL: /voice/clone/delete
     * Method: DELETE
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @return Result<String>
     */
    @Operation(summary = "删除用户音色")
    @DeleteMapping("/delete")
    public Result<String> deleteUserVoice(
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "音色ID") @RequestParam String voiceId) {
        return digitalVoiceUserCloneService.deleteUserVoice(userId, voiceId);
    }

    /**
     * 更新音色名称
     * URL: /voice/clone/update/name
     * Method: PUT
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @param voiceName 新的音色名称
     * @return Result<String>
     */
    @Operation(summary = "更新音色名称")
    @PutMapping("/update/name")
    public Result<String> updateVoiceName(
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "音色ID") @RequestParam String voiceId,
            @Parameter(description = "音色名称") @RequestParam String voiceName) {
        return digitalVoiceUserCloneService.updateUserVoiceName(userId, voiceId, voiceName);
    }

    /**
     * 克隆音色创建接口
     * URL: /voice/clone/create
     * Method: POST
     *
     * @return 创建的音色信息
     */
    @PostMapping(value = "/create", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<DigitalVoiceStyleVO> createVoiceStyle(
            @ModelAttribute DigitalVoiceStyleCreateDTO digitalVoiceStyleCreateDTO
    ) {
        return digitalVoiceUserCloneService.createVoiceStyle(digitalVoiceStyleCreateDTO);
    }

}
