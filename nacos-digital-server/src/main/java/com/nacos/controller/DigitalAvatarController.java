package com.nacos.controller;

import com.nacos.entity.dto.DigitalAvatarTrainDTO;
import com.nacos.entity.vo.DigitalUserAvatarVO;
import com.nacos.entity.vo.DigitalAvatarInstanceListVO;
import com.nacos.result.Result;
import com.nacos.controller.avatar.AvatarTrainingController;
import com.nacos.controller.avatar.AvatarInstanceController;
import com.nacos.controller.admin.AvatarAdminController;
import com.nacos.service.DigitalAvatarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人控制器（Legacy兼容层）
 * 通过委托模式保持原有API路径不变，将请求路由到新的模块化Controller
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人管理（兼容层）", description = "保持向后兼容的数字人管理接口")
@RestController
@RequestMapping("/avatar")
public class DigitalAvatarController {

    // 委托给新的模块化Controller
    @Autowired
    private AvatarTrainingController avatarTrainingController;

    @Autowired
    private AvatarInstanceController avatarInstanceController;

    @Autowired
    private AvatarAdminController avatarAdminController;

    // 保留Service依赖用于一些特殊方法
    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 获取数字人列表（委托给AvatarAdminController）
     * URL: /avatar/list
     * Method: GET
     *
     * @return 数字人列表
     */
    @Operation(summary = "获取数字人列表")
    @GetMapping("/list")
    public Result<List<DigitalUserAvatarVO>> listAvatars() {
        log.info("Legacy API: /avatar/list - 委托给AvatarAdminController");
        return avatarAdminController.listAvatars(null, 1, 20);
    }

    /**
     * 添加数字人（委托给AvatarAdminController）
     * URL: /avatar/add
     * Method: POST
     *
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "添加数字人")
    @PostMapping("/add")
    public Result<Boolean> addAvatar(@RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Legacy API: /avatar/add - 委托给AvatarAdminController");
        return avatarAdminController.createAvatar(avatarVO);
    }

    /**
     * 更新数字人（委托给AvatarAdminController）
     * URL: /avatar/update
     * Method: PUT
     *
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "更新数字人")
    @PutMapping("/update")
    public Result<Boolean> updateAvatar(@RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Legacy API: /avatar/update - 委托给AvatarAdminController");
        return avatarAdminController.updateAvatar(avatarVO.getId(), avatarVO);
    }

    /**
     * 删除数字人（委托给AvatarAdminController）
     * URL: /avatar/delete/{id}
     * Method: DELETE
     *
     * @param id 数字人ID
     * @return 操作结果
     */
    @Operation(summary = "删除数字人")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteAvatar(@Parameter(description = "数字人ID") @PathVariable Long id) {
        log.info("Legacy API: /avatar/delete/{} - 委托给AvatarAdminController", id);
        return avatarAdminController.deleteAvatar(id);
    }

    /**
     * 更新数字人状态（委托给Service）
     * URL: /avatar/status/{id}/{status}
     * Method: PUT
     *
     * @param id     数字人ID
     * @param status 状态：0-禁用，1-启用
     * @return 操作结果
     */
    @Operation(summary = "更新数字人状态")
    @PutMapping("/status/{id}/{status}")
    public Result<Boolean> updateStatus(
            @Parameter(description = "数字人ID") @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @PathVariable Integer status) {
        log.info("Legacy API: /avatar/status/{}/{} - 委托给DigitalAvatarService", id, status);
        return digitalAvatarService.updateStatus(id, status);
    }

    /**
     * 根据组ID和用户ID获取数字人列表（委托给Service）
     * URL: /avatar/list/group/{groupId}/{userId}
     * Method: GET
     *
     * @param groupId 组ID
     * @param userId  用户ID
     * @return 数字人列表
     */
    @Operation(summary = "根据组ID和用户ID获取数字人列表")
    @GetMapping("/list/group/{groupId}/{userId}")
    public Result<List<DigitalUserAvatarVO>> listAvatarsByGroupId(
            @Parameter(description = "组ID") @PathVariable Long groupId,
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("Legacy API: /avatar/list/group/{}/{} - 委托给DigitalAvatarService", groupId, userId);
        return digitalAvatarService.listAvatarsByGroupId(groupId, userId);
    }

    /**
     * 开始训练按钮接口（委托给AvatarTrainingController）
     * 该接口支持两种功能：
     * 1. 创建新组并添加数字人：上传训练视频、授权视频、声音文件，创建新组，并将数字人添加到新组中
     * - 设置createNewGroup=true，并提供groupName
     * 2. 使用现有组添加数字人：上传训练视频、授权视频、声音文件，将数字人添加到现有组中
     * - 设置createNewGroup=false，并提供groupId
     *
     * 处理流程：
     * - 根据createNewGroup字段判断是否需要创建新组
     * - 如果创建新组，则先创建组，再创建数字人
     * - 如果使用现有组，则直接创建数字人
     * - 上传文件到OSS
     * - 调用上传原音频到MiniMax获取文件id，存入到数字人表中
     * - 调用音频克隆接口，获取克隆后的音色id，存入到音色用户克隆表中
     *
     * URL: /avatar/train
     * Method: POST
     *
     * @param trainDTO 数字人训练信息
     * @return 操作结果，返回数字人ID
     */
    @Operation(summary = "开始训练按钮接口（支持创建组和数字人）")
    @PostMapping(value = "/train", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> trainAvatar(@ModelAttribute DigitalAvatarTrainDTO trainDTO) {
        log.info("Legacy API: /avatar/train - 委托给AvatarTrainingController");
        return avatarTrainingController.startTraining(trainDTO);
    }

    /**
     * 根据组ID获取数字人信息（用于创建视频）（委托给Service）
     * URL: /avatar/info/{groupId}
     * Method: GET
     *
     * @param groupId 组ID
     * @return 数字人信息
     */
    @Operation(summary = "根据组ID获取数字人信息")
    @GetMapping("/info/{groupId}")
    public Result<List<DigitalUserAvatarVO>> getAvatarInfoByGroupId(
            @Parameter(description = "组ID") @PathVariable Long groupId) {
        log.info("Legacy API: /avatar/info/{} - 委托给DigitalAvatarService", groupId);
        return digitalAvatarService.listAvatarsByGroupId(groupId, null);
    }

    /**
     * 获取组下的分身列表（委托给AvatarInstanceController）
     * URL: /avatar/groups/{groupId}/instances
     * Method: GET
     *
     * @param groupId 组ID
     * @param userId 用户ID（必填，用于权限验证）
     * @return 该组下的数字人分身列表
     */
    @Operation(summary = "获取组下的分身列表", description = "获取指定组下的所有数字人分身")
    @GetMapping("/groups/{groupId}/instances")
    public Result<DigitalAvatarInstanceListVO> listInstancesByGroup(
            @Parameter(description = "组ID", required = true)
            @PathVariable String groupId,
            @Parameter(description = "用户ID，用于权限验证", required = true)
            @RequestParam String userId) {

        log.info("Legacy API: /avatar/groups/{}/instances - 委托给AvatarInstanceController", groupId);
        return avatarInstanceController.listInstancesByGroup(groupId, userId);
    }
}