package com.nacos.controller;

import com.nacos.entity.dto.DigitalVideoGenerationDTO;
// import com.nacos.entity.dto.VideoMergeTestDTO; // Assuming this was for the commented out section
import com.nacos.result.Result;
import com.nacos.service.DigitalVideoService;
// import com.nacos.utils.DigitalFileUtil; // Assuming this was for the commented out section
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.media.Schema;
// import org.apache.commons.lang3.StringUtils; // Assuming this was for the commented out section

// New Imports for TXaPaas
import com.nacos.model.TXaPaas.TXaPaasApiUtil;
import com.nacos.model.TXaPaas.model.TxAIPaasQueryProgressResponse;
import com.nacos.model.TXaPaas.model.TxAIPaasVideoMakeNoTrainRequest;
import com.nacos.model.TXaPaas.model.TxAIPaasVideoMakeNoTrainResponse;
import com.nacos.entity.dto.TestVideoMakeNoTrainControllerRequest;
import com.alibaba.fastjson2.JSONObject;

@Tag(name = "数字人视频生成", description = "数字人视频生成接口")
@Log4j2
@RestController
@RequiredArgsConstructor
@RequestMapping("/video")
public class DigitalVideoController {

    private final DigitalVideoService digitalVideoService;
    private final TXaPaasApiUtil txAIPaasApiUtil; // Added for TXaPaas

    /**
     * 数字人视频生成接口
     * url: /video/generate
     * method: POST
     * 
     * @param digitalVideoGenerationDTO 数字人视频生成请求参数
     * @return 最终视频的URL
     */
    @PostMapping(value = "/generate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Schema(description = "提交数字人视频生成请求（异步）")
    public Result<String> submitDigitalVideoGenerationRequest(
            @ModelAttribute @Valid DigitalVideoGenerationDTO digitalVideoGenerationDTO) {
        return digitalVideoService.submitDigitalVideoGenerationRequest(digitalVideoGenerationDTO);
    }

    /**
     * 测试TXaPaas API - 查询音视频制作进度
     * url: /video/test-getprogress/{taskId}
     * method: GET
     * 
     * @param taskId 任务ID (路径参数)
     * @return 任务详情
     */
    @GetMapping("/test-getprogress/{taskId}")
    @Schema(description = "测试TXaPaas API - 查询进度 (GET)")
    public Result<String> testGetProgress(@PathVariable String taskId) {
        log.info("[{}] - Testing GetProgress with taskId: {}", "testGetProgress", taskId);
        TxAIPaasQueryProgressResponse response = txAIPaasApiUtil.queryVideoProgress(taskId,
                "878d9158c1234ffd9ae6a7bce3f41cec", "27623a9455f0484ab6e7b68e89a9aec4");
        if (response != null && response.getHeader() != null && response.getHeader().getCode() == 0) {
            return Result.SUCCESS(JSONObject.toJSONString(response));
        } else {
            String errorMsg = "testGetProgress - API call failed or returned error: ";
            if (response != null && response.getHeader() != null) {
                errorMsg += "Code=" + response.getHeader().getCode() + ", Msg=" + response.getHeader().getMessage();
            } else if (response != null) {
                errorMsg += JSONObject.toJSONString(response);
            } else {
                errorMsg += "Received null response from Util layer.";
            }
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
        }
    }

    /**
     * 测试TXaPaas API - 提交视频制作（视频免训练）任务
     * url: /video/test-videomakenotrain
     * method: POST
     * 
     * @param controllerRequest 视频制作请求参数
     * @return 任务提交结果，可能包含任务ID
     */
    @PostMapping("/test-videomakenotrain")
    @Schema(description = "测试TXaPaas API - 提交视频制作(免训练)")
    public Result<String> testVideomakeNoTrain(
            @RequestBody @Valid TestVideoMakeNoTrainControllerRequest controllerRequest) {
        log.info("[{}] - Testing VideomakeNoTrain with request: {}", "testVideomakeNoTrain", controllerRequest);
        TxAIPaasVideoMakeNoTrainRequest.RequestPayload payload = new TxAIPaasVideoMakeNoTrainRequest.RequestPayload();
        payload.setRefVideoUrl(controllerRequest.getRefVideoUrl());
        payload.setDriverType(controllerRequest.getDriverType());
        payload.setInputSsml(controllerRequest.getInputSsml());
        payload.setSpeechParam(controllerRequest.getSpeechParam());
        payload.setInputAudioUrl(controllerRequest.getInputAudioUrl());
        // payload.setCallbackUrl(...); // Set if needed for testing
        // payload.setUserData(...); // Set if needed for testing

        TxAIPaasVideoMakeNoTrainResponse response = txAIPaasApiUtil.videomakeNoTrain(payload,
                "878d9158c1234ffd9ae6a7bce3f41cec", "27623a9455f0484ab6e7b68e89a9aec4");

        if (response != null && response.getHeader() != null && response.getHeader().getCode() == 0
                && response.getPayload() != null) {
            return Result.SUCCESS(JSONObject.toJSONString(response));
        } else {
            String errorMsg = "testVideomakeNoTrain - API call failed or returned error: ";
            if (response != null && response.getHeader() != null) {
                errorMsg += "Code=" + response.getHeader().getCode() + ", Msg=" + response.getHeader().getMessage();
            } else if (response != null) {
                errorMsg += JSONObject.toJSONString(response);
            } else {
                errorMsg += "Received null response from Util layer.";
            }
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
        }
    }
}
