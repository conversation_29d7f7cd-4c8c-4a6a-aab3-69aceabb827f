package com.nacos.controller.system;

import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * 缓存管理业务Controller
 * 专注于缓存清理和管理的业务逻辑操作
 * 数据CRUD操作已迁移到admin包的SystemAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "缓存管理业务", description = "缓存清理与管理业务逻辑")
@RestController
@RequestMapping("/api/v1/system/cache")
public class CacheController {

    @Autowired(required = false)
    private CacheManager cacheManager;

    /**
     * 清除所有缓存
     * URL: /api/v1/system/cache/clear/all
     * Method: POST
     */
    @Operation(summary = "清除所有缓存", description = "清除系统中的所有缓存")
    @PostMapping("/clear/all")
    public Result<String> clearAllCache() {
        log.info("清除所有缓存");

        if (cacheManager != null) {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            for (String cacheName : cacheNames) {
                cacheManager.getCache(cacheName).clear();
                log.info("已清除缓存：{}", cacheName);
            }
            return Result.SUCCESS("已清除所有缓存，共清除 " + cacheNames.size() + " 个缓存", "success");
        } else {
            return Result.SUCCESS("缓存管理器未配置", "success");
        }
    }

    /**
     * 清除指定缓存
     * URL: /api/v1/system/cache/clear/{cacheName}
     * Method: POST
     */
    @Operation(summary = "清除指定缓存", description = "清除指定名称的缓存")
    @PostMapping("/clear/{cacheName}")
    public Result<String> clearCache(
            @Parameter(description = "缓存名称") @PathVariable String cacheName) {
        log.info("清除指定缓存，缓存名称：{}", cacheName);

        if (cacheManager != null) {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                return Result.SUCCESS("已清除缓存：" + cacheName, "success");
            } else {
                return Result.ERROR("缓存不存在：" + cacheName);
            }
        } else {
            return Result.ERROR("缓存管理器未配置");
        }
    }

    /**
     * 获取缓存列表
     * URL: /api/v1/system/cache/list
     * Method: GET
     */
    @Operation(summary = "获取缓存列表", description = "获取系统中所有缓存的名称列表")
    @GetMapping("/list")
    public Result<Collection<String>> listCaches() {
        log.info("获取缓存列表");

        if (cacheManager != null) {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            return Result.SUCCESS(cacheNames);
        } else {
            return Result.ERROR("缓存管理器未配置");
        }
    }

    /**
     * 清除指定缓存的特定键
     * URL: /api/v1/system/cache/evict/{cacheName}/{key}
     * Method: DELETE
     */
    @Operation(summary = "清除缓存特定键", description = "清除指定缓存中的特定键值")
    @DeleteMapping("/evict/{cacheName}/{key}")
    public Result<String> evictCacheKey(
            @Parameter(description = "缓存名称") @PathVariable String cacheName,
            @Parameter(description = "缓存键") @PathVariable String key) {
        log.info("清除缓存特定键，缓存名称：{}，键：{}", cacheName, key);

        if (cacheManager != null) {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                return Result.SUCCESS("已清除缓存键：" + cacheName + ":" + key, "success");
            } else {
                return Result.ERROR("缓存不存在：" + cacheName);
            }
        } else {
            return Result.ERROR("缓存管理器未配置");
        }
    }
}
