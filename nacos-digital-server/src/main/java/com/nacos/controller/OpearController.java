package com.nacos.controller;

import com.nacos.entity.dto.DigitalAvatarTrainDTO;
import com.nacos.entity.vo.DigitalUserAvatarVO;
import com.nacos.result.Result;
import com.nacos.controller.DigitalAvatarController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人控制器（Legacy兼容层）
 * OpearController与DigitalAvatarController功能重复，通过委托模式统一路由
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人管理（兼容层-Opear）", description = "保持向后兼容的数字人管理接口")
@RestController
@RequestMapping("/opear")
public class OpearController {

    // 委托给DigitalAvatarController统一处理
    @Autowired
    private DigitalAvatarController digitalAvatarController;

    /**
     * 获取数字人列表（委托给DigitalAvatarController）
     * URL: /opear/list
     * Method: GET
     *
     * @return 数字人列表
     */
    @Operation(summary = "获取数字人列表")
    @GetMapping("/list")
    public Result<List<DigitalUserAvatarVO>> listAvatars() {
        log.info("Legacy API: /opear/list - 委托给DigitalAvatarController");
        return digitalAvatarController.listAvatars();
    }

    /**
     * 添加数字人（委托给DigitalAvatarController）
     * URL: /opear/add
     * Method: POST
     *
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "添加数字人")
    @PostMapping("/add")
    public Result<Boolean> addAvatar(@RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Legacy API: /opear/add - 委托给DigitalAvatarController");
        return digitalAvatarController.addAvatar(avatarVO);
    }

    /**
     * 更新数字人（委托给DigitalAvatarController）
     * URL: /opear/update
     * Method: PUT
     *
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    @Operation(summary = "更新数字人")
    @PutMapping("/update")
    public Result<Boolean> updateAvatar(@RequestBody DigitalUserAvatarVO avatarVO) {
        log.info("Legacy API: /opear/update - 委托给DigitalAvatarController");
        return digitalAvatarController.updateAvatar(avatarVO);
    }

    /**
     * 删除数字人（委托给DigitalAvatarController）
     * URL: /opear/delete/{id}
     * Method: DELETE
     *
     * @param id 数字人ID
     * @return 操作结果
     */
    @Operation(summary = "删除数字人")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteAvatar(@Parameter(description = "数字人ID") @PathVariable Long id) {
        log.info("Legacy API: /opear/delete/{} - 委托给DigitalAvatarController", id);
        return digitalAvatarController.deleteAvatar(id);
    }

    /**
     * 更新数字人状态（委托给DigitalAvatarController）
     * URL: /opear/status/{id}/{status}
     * Method: PUT
     *
     * @param id     数字人ID
     * @param status 状态：0-禁用，1-启用
     * @return 操作结果
     */
    @Operation(summary = "更新数字人状态")
    @PutMapping("/status/{id}/{status}")
    public Result<Boolean> updateStatus(
            @Parameter(description = "数字人ID") @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @PathVariable Integer status) {
        log.info("Legacy API: /opear/status/{}/{} - 委托给DigitalAvatarController", id, status);
        return digitalAvatarController.updateStatus(id, status);
    }

    /**
     * 根据组ID和用户ID获取数字人列表（委托给DigitalAvatarController）
     * URL: /opear/list/group/{groupId}/{userId}
     * Method: GET
     *
     * @param groupId 组ID
     * @param userId  用户ID
     * @return 数字人列表
     */
    @Operation(summary = "根据组ID和用户ID获取数字人列表")
    @GetMapping("/list/group/{groupId}/{userId}")
    public Result<List<DigitalUserAvatarVO>> listAvatarsByGroupId(
            @Parameter(description = "组ID") @PathVariable Long groupId,
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("Legacy API: /opear/list/group/{}/{} - 委托给DigitalAvatarController", groupId, userId);
        return digitalAvatarController.listAvatarsByGroupId(groupId, userId);
    }

    /**
     * 开始训练按钮接口（委托给DigitalAvatarController）
     * 该接口支持两种功能：
     * 1. 创建新组并添加数字人：上传训练视频、授权视频、声音文件，创建新组，并将数字人添加到新组中
     * - 设置createNewGroup=true，并提供groupName
     * 2. 使用现有组添加数字人：上传训练视频、授权视频、声音文件，将数字人添加到现有组中
     * - 设置createNewGroup=false，并提供groupId
     *
     * 处理流程：
     * - 根据createNewGroup字段判断是否需要创建新组
     * - 如果创建新组，则先创建组，再创建数字人
     * - 如果使用现有组，则直接创建数字人
     * - 上传文件到OSS
     * - 调用上传原音频到MiniMax获取文件id，存入到数字人表中
     * - 调用音频克隆接口，获取克隆后的音色id，存入到音色用户克隆表中
     *
     * URL: /opear/train
     * Method: POST
     *
     * @param trainDTO 数字人训练信息
     * @return 操作结果，返回数字人ID
     */
    @Operation(summary = "开始训练按钮接口（支持创建组和数字人）")
    @PostMapping(value = "/train", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> trainAvatar(@ModelAttribute DigitalAvatarTrainDTO trainDTO) {
        log.info("Legacy API: /opear/train - 委托给DigitalAvatarController");
        return digitalAvatarController.trainAvatar(trainDTO);
    }

    /**
     * 根据组ID获取数字人信息（委托给DigitalAvatarController）
     * URL: /opear/info/{groupId}
     * Method: GET
     *
     * @param groupId 组ID
     * @return 数字人信息
     */
    @Operation(summary = "根据组ID获取数字人信息")
    @GetMapping("/info/{groupId}")
    public Result<List<DigitalUserAvatarVO>> getAvatarInfoByGroupId(
            @Parameter(description = "组ID") @PathVariable Long groupId) {
        log.info("Legacy API: /opear/info/{} - 委托给DigitalAvatarController", groupId);
        return digitalAvatarController.getAvatarInfoByGroupId(groupId);
    }

}