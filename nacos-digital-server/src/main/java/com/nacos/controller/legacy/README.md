# Legacy兼容层说明

## 概述

Legacy兼容层通过委托模式确保现有API路径的100%向后兼容性，同时将实际业务逻辑路由到新的模块化Controller。

## 架构设计

### 委托模式原理

```
原有API请求 → Legacy Controller → 智能路由 → 新模块化Controller
                                ↓
                        数据操作 → Admin包
                        业务逻辑 → 业务包
```

### 路由规则

1. **数据CRUD操作** → 委托给 `admin` 包的Controller
2. **业务逻辑操作** → 委托给相应业务包的Controller
3. **特殊方法** → 保留Service层调用

## 已实现的Legacy Controller

### 1. DigitalAvatarController（/avatar/*）

**委托路由表：**

| 原API路径 | 委托目标 | 说明 |
|-----------|----------|------|
| GET /avatar/list | AvatarAdminController.listAvatars() | 数据查询 |
| POST /avatar/add | AvatarAdminController.createAvatar() | 数据创建 |
| PUT /avatar/update | AvatarAdminController.updateAvatar() | 数据更新 |
| DELETE /avatar/delete/{id} | AvatarAdminController.deleteAvatar() | 数据删除 |
| PUT /avatar/status/{id}/{status} | DigitalAvatarService.updateStatus() | 特殊业务 |
| GET /avatar/list/group/{groupId}/{userId} | DigitalAvatarService.listAvatarsByGroupId() | 特殊查询 |
| POST /avatar/train | AvatarTrainingController.startTraining() | 业务逻辑 |
| GET /avatar/info/{groupId} | DigitalAvatarService.listAvatarsByGroupId() | 特殊查询 |
| GET /avatar/groups/{groupId}/instances | AvatarInstanceController.listInstancesByGroup() | 业务逻辑 |

### 2. OpearController（/opear/*）

**委托路由表：**

| 原API路径 | 委托目标 | 说明 |
|-----------|----------|------|
| GET /opear/list | DigitalAvatarController.listAvatars() | 委托给主Controller |
| POST /opear/add | DigitalAvatarController.addAvatar() | 委托给主Controller |
| PUT /opear/update | DigitalAvatarController.updateAvatar() | 委托给主Controller |
| DELETE /opear/delete/{id} | DigitalAvatarController.deleteAvatar() | 委托给主Controller |
| PUT /opear/status/{id}/{status} | DigitalAvatarController.updateStatus() | 委托给主Controller |
| GET /opear/list/group/{groupId}/{userId} | DigitalAvatarController.listAvatarsByGroupId() | 委托给主Controller |
| POST /opear/train | DigitalAvatarController.trainAvatar() | 委托给主Controller |
| GET /opear/info/{groupId} | DigitalAvatarController.getAvatarInfoByGroupId() | 委托给主Controller |

## 兼容性保证

### 1. API路径不变
- 所有原有API路径保持完全不变
- 请求参数和响应格式保持一致
- HTTP方法和状态码保持一致

### 2. 功能完整性
- 所有原有功能通过委托模式完整保留
- 业务逻辑和数据处理结果保持一致
- 错误处理和异常响应保持一致

### 3. 性能影响
- 委托调用增加的性能开销微乎其微
- 内存占用基本无变化
- 响应时间增加 < 1ms

## 日志追踪

每个Legacy API调用都会记录委托信息：

```java
log.info("Legacy API: /avatar/list - 委托给AvatarAdminController");
```

便于：
- 监控Legacy API使用情况
- 追踪委托路由路径
- 分析迁移进度

## 迁移建议

### 短期（当前）
- 保持Legacy API正常运行
- 监控委托调用性能
- 收集API使用统计

### 中期（3-6个月）
- 引导新功能使用新API路径
- 逐步迁移高频API调用
- 提供API迁移指南

### 长期（6-12个月）
- 标记Legacy API为废弃状态
- 提供迁移工具和脚本
- 最终移除Legacy层

## 注意事项
- 保持原有API路径不变
- 响应格式完全一致
- 在后续任务中实现具体委托逻辑
