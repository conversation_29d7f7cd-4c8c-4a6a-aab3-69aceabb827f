package com.nacos.controller.media;

import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.entity.dto.TextToSpeechFileRequestDTO;
import com.nacos.entity.dto.TextToSpeechFileResponseDTO;
import com.nacos.entity.vo.VoiceListResponseVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalAudioService;
import com.nacos.component.AudioGenerationRateLimiter;

import java.util.List;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 音频处理业务Controller
 * 专注于音频生成、处理等业务逻辑操作
 * 数据CRUD操作已迁移到admin包的AudioAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "音频处理业务", description = "音频生成与处理业务逻辑")
@RestController
@RequestMapping("/api/v1/media/audio")
public class AudioController {

    @Autowired
    private DigitalAudioService digitalAudioService;

    @Autowired
    private AudioGenerationRateLimiter rateLimiter;

    /**
     * 音频生成接口（同步）
     * URL: /api/v1/media/audio/generate
     * Method: POST
     */
    @Operation(summary = "音频生成（同步）", description = "根据文本同步生成音频")
    @PostMapping("/generate")
    public Result<AudioGenerationResponseVO> generateAudio(
            @Valid @RequestBody AudioGenerationRequestDTO requestDTO,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId,
            HttpServletRequest request) {
        log.info("音频生成请求，用户ID：{}，参数：{}", userId, requestDTO);

        // 应用限流（简化版本，实际应该检查限流器的具体实现）
        String clientIp = getClientIp(request);
        log.debug("客户端IP：{}", clientIp);

        return digitalAudioService.generateAudioSync(requestDTO, userId);
    }

    /**
     * 音频生成接口（异步）
     * URL: /api/v1/media/audio/generate/async
     * Method: POST
     */
    @Operation(summary = "音频生成（异步）", description = "异步提交音频生成任务")
    @PostMapping("/generate/async")
    public Result<String> generateAudioAsync(
            @Valid @RequestBody AudioGenerationRequestDTO requestDTO,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("异步音频生成请求，用户ID：{}，参数：{}", userId, requestDTO);
        return digitalAudioService.submitAudioGenerationTask(requestDTO, userId);
    }

    /**
     * 音色克隆接口
     * URL: /api/v1/media/audio/voice/clone
     * Method: POST
     */
    @Operation(summary = "音色克隆", description = "克隆用户音色")
    @PostMapping("/voice/clone")
    public Result<String> cloneVoice(
            @RequestBody DigitalVoiceCloningDTO digitalVoiceCloningDTO,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("音色克隆请求，用户ID：{}，参数：{}", userId, digitalVoiceCloningDTO);
        return digitalAudioService.voiceCloning(digitalVoiceCloningDTO, userId);
    }

    /**
     * 音频文件上传接口
     * URL: /api/v1/media/audio/upload
     * Method: POST
     */
    @Operation(summary = "音频文件上传", description = "上传音频文件进行处理")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadAudio(
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "音频用途") @RequestParam("purpose") String purpose,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("音频文件上传，用户ID：{}，用途：{}，文件名：{}", userId, purpose, file.getOriginalFilename());
        return digitalAudioService.uploadAudio(file, purpose, userId);
    }

    /**
     * 文本转语音文件接口
     * URL: /api/v1/media/audio/tts/file
     * Method: POST
     */
    @Operation(summary = "文本转语音文件", description = "将文本转换为语音文件")
    @PostMapping("/tts/file")
    public Result<TextToSpeechFileResponseDTO> textToSpeechFile(
            @Valid @RequestBody TextToSpeechFileRequestDTO requestDTO,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("文本转语音文件请求，用户ID：{}，参数：{}", userId, requestDTO);
        return digitalAudioService.textToSpeech(requestDTO, userId);
    }

    /**
     * 获取支持的语音列表
     * URL: /api/v1/media/audio/voices/supported
     * Method: GET
     */
    @Operation(summary = "获取支持的语音列表", description = "获取系统支持的所有语音")
    @GetMapping("/voices/supported")
    public Result<List<VoiceListResponseVO>> getSupportedVoices() {
        log.info("获取支持的语音列表");
        return digitalAudioService.getSupportedVoices();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return request.getRemoteAddr();
    }
}
