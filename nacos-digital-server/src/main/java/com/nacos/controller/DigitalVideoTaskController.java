package com.nacos.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.entity.vo.DigitalVideoTaskStatusVO;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.DigitalVideoTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频任务控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/video/task")
@Tag(name = "视频任务接口")
public class DigitalVideoTaskController {

    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVideoTaskService digitalVideoTaskService;

    /**
     * 查询用户的视频任务列表
     * url: /video/task/list
     * method: GET
     * 
     * @param userId 用户ID
     * @return 视频任务列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询用户的视频任务列表")
    public Result<List<DigitalVideoTaskPO>> getTaskList(@RequestParam String userId) {
        try {
            List<DigitalVideoTaskPO> tasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getUserId, userId)
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByDesc(DigitalVideoTaskPO::getCreatedTime));
            return Result.SUCCESS(tasks);
        } catch (Exception e) {
            log.error("查询用户视频任务列表失败：{}", e.getMessage(), e);
            return Result.ERROR("查询任务列表失败");
        }
    }

    /**
     * 查询单个任务详情
     * url: /video/task/{taskId}
     * method: GET
     * 
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "查询单个任务详情")
    public Result<DigitalVideoTaskPO> getTaskDetail(@PathVariable String taskId) {
        try {
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getTaskId, taskId)
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0));
            if (task == null) {
                return Result.ERROR("任务不存在");
            }
            return Result.SUCCESS(task);
        } catch (Exception e) {
            log.error("查询任务详情失败：{}", e.getMessage(), e);
            return Result.ERROR("查询任务详情失败");
        }
    }

    /**
     * 查询用户的进行中和成功的任务
     * url: /video/task/active
     * method: GET
     * 
     * @param userId 用户ID
     * @return 任务状态列表
     */
    @GetMapping("/active")
    @Operation(summary = "查询用户的进行中和成功的任务")
    public Result<List<DigitalVideoTaskStatusVO>> getActiveTaskList(@RequestParam String userId) {
        try {
            List<DigitalVideoTaskStatusVO> tasks = digitalVideoTaskService.getActiveTaskList(userId);
            return Result.SUCCESS(tasks);
        } catch (Exception e) {
            log.error("查询用户活跃任务列表失败：{}", e.getMessage(), e);
            return Result.ERROR("查询活跃任务列表失败");
        }
    }
}