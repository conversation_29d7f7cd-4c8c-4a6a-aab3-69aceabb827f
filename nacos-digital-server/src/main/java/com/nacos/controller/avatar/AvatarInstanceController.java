package com.nacos.controller.avatar;

import com.nacos.entity.vo.DigitalAvatarInstanceDetailVO;
import com.nacos.entity.vo.DigitalAvatarInstanceListVO;
import com.nacos.entity.vo.DigitalAvatarCategoryStatsVO;
import com.nacos.entity.vo.DigitalAvatarGroupListVO;
import com.nacos.service.DigitalAvatarInstanceService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人分身实例Controller
 * 支持数字人视频创建业务流程的API接口
 * 正确的层级关系：分类 -> 组 -> 分身
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人分身实例管理", description = "数字人分身实例CRUD操作")
@RestController
@RequestMapping("/api/v1/avatars/instances")
public class AvatarInstanceController {

    @Autowired
    private DigitalAvatarInstanceService avatarInstanceService;

    /**
     * 获取组下的分身列表
     * URL: /digital/api/v1/avatars/instances/groups/{groupId}
     * Method: GET
     *
     * @param groupId 组ID
     * @param userId 用户ID（必填，用于权限验证）
     * @return 该组下的数字人分身列表
     */
    @Operation(summary = "获取组下的分身列表", description = "获取指定组下的所有数字人分身")
    @GetMapping("/groups/{groupId}")
    public Result<DigitalAvatarInstanceListVO> listInstancesByGroup(
            @Parameter(description = "组ID", required = true)
            @PathVariable String groupId,
            @Parameter(description = "用户ID，用于权限验证", required = true)
            @RequestParam String userId) {

        log.info("获取组下的分身列表 - groupId: {}, userId: {}", groupId, userId);
        return avatarInstanceService.listInstancesByGroup(groupId, userId);
    }

    /**
     * 获取分身详情
     * URL: /digital/api/v1/avatars/instances/{avatarId}
     * Method: GET
     *
     * @param avatarId 分身ID
     * @param userId 用户ID（必填，用于权限验证）
     * @return 数字人分身详细信息
     */
    @Operation(summary = "获取分身详情", description = "获取指定分身的详细信息，包含关联的音色信息，用于跳转创建视频页面")
    @GetMapping("/{avatarId}")
    public Result<DigitalAvatarInstanceDetailVO> getInstanceDetail(
            @Parameter(description = "分身ID", required = true)
            @PathVariable String avatarId,
            @Parameter(description = "用户ID，用于权限验证", required = true)
            @RequestParam String userId) {

        log.info("获取分身详情 - avatarId: {}, userId: {}", avatarId, userId);
        return avatarInstanceService.getAvatarInstanceDetail(avatarId, userId);
    }

    /**
     * 获取分类统计信息
     * URL: /api/v1/avatars/instances/categories/stats
     * Method: GET
     *
     * @param userId 用户ID（必填，用于权限验证）
     * @return 各分类下的数字人统计信息
     */
    @Operation(summary = "获取分类统计信息", description = "获取各分类下的数字人统计信息")
    @GetMapping("/categories/stats")
    public Result<DigitalAvatarCategoryStatsVO> getCategoryStats(
            @Parameter(description = "用户ID，用于权限验证", required = true)
            @RequestParam String userId) {

        log.info("获取分类统计信息 - userId: {}", userId);
        return avatarInstanceService.getCategoryStats(userId);
    }

    /**
     * 获取分类下的组列表
     * URL: /api/v1/avatars/instances/categories/{categoryId}/groups
     * Method: GET
     *
     * @param categoryId 分类ID
     * @param userId 用户ID（必填，用于权限验证）
     * @return 该分类下的组列表
     */
    @Operation(summary = "获取分类下的组列表", description = "获取指定分类下的所有组")
    @GetMapping("/categories/{categoryId}/groups")
    public Result<List<DigitalAvatarGroupListVO>> listGroupsByCategory(
            @Parameter(description = "分类ID", required = true)
            @PathVariable Long categoryId,
            @Parameter(description = "用户ID，用于权限验证", required = true)
            @RequestParam String userId) {

        log.info("获取分类下的组列表 - categoryId: {}, userId: {}", categoryId, userId);
        // TODO: 需要实现获取分类下组列表的功能
        log.warn("获取分类下组列表功能暂未实现");
        return Result.ERROR("获取分类下组列表功能暂未实现");
    }

}
