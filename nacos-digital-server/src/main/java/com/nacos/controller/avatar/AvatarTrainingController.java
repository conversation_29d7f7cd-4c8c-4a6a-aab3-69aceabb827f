package com.nacos.controller.avatar;

import com.nacos.entity.dto.DigitalAvatarTrainDTO;
import com.nacos.result.Result;
import com.nacos.service.DigitalAvatarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 数字人训练业务Controller
 * 专门处理数字人训练相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人训练业务", description = "数字人训练相关业务操作")
@RestController
@RequestMapping("/api/v1/avatars/training")
public class AvatarTrainingController {
    
    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 开始训练按钮接口
     * 该接口支持两种功能：
     * 1. 创建新组并添加数字人：上传训练视频、授权视频、声音文件，创建新组，并将数字人添加到新组中
     * - 设置createNewGroup=true，并提供groupName
     * 2. 使用现有组添加数字人：上传训练视频、授权视频、声音文件，将数字人添加到现有组中
     * - 设置createNewGroup=false，并提供groupId
     * 
     * 处理流程：
     * - 根据createNewGroup字段判断是否需要创建新组
     * - 如果创建新组，则先创建组，再创建数字人
     * - 如果使用现有组，则直接创建数字人
     * - 上传文件到OSS
     * - 调用上传原音频到MiniMax获取文件id，存入到数字人表中
     * - 调用音频克隆接口，获取克隆后的音色id，存入到音色用户克隆表中
     * 
     * URL: /api/v1/avatars/training/start
     * Method: POST
     * 
     * @param trainDTO 数字人训练信息
     * @return 操作结果，返回数字人ID
     */
    @Operation(summary = "开始训练数字人", description = "支持创建组和数字人的训练接口")
    @PostMapping(value = "/start", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> startTraining(@ModelAttribute DigitalAvatarTrainDTO trainDTO) {
        log.info("开始训练数字人，参数：{}", trainDTO);
        return digitalAvatarService.trainAvatarNew(trainDTO);
    }

    /**
     * 训练数字人（旧版本接口）
     * URL: /api/v1/avatars/training/train
     * Method: POST
     * 
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    @Operation(summary = "训练数字人（旧版本）", description = "旧版本的数字人训练接口")
    @PostMapping(value = "/train", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> trainAvatar(@ModelAttribute DigitalAvatarTrainDTO trainDTO) {
        log.info("训练数字人（旧版本），参数：{}", trainDTO);
        return digitalAvatarService.trainAvatar(trainDTO);
    }
}
