package com.nacos.controller.avatar;

import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.vo.DigitalAvatarGroupListVO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.entity.vo.PageResultVO;
import com.nacos.service.DigitalUserGroupService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人分组管理Controller
 * 处理数字人分组相关操作
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人分组管理", description = "数字人分组CRUD操作")
@RestController
@RequestMapping("/api/v1/avatars/groups")
public class AvatarGroupController {

    @Autowired
    private DigitalUserGroupService userGroupService;

    /**
     * 分页查询所有数字人组列表（通用接口）
     * URL: /digital/api/v1/avatars/groups/queryAllGroupsPage
     * Method: GET
     *
     * 支持多种查询模式：
     * 1. 查询用户创建的组：groupType=USER
     * 2. 查询系统预设的组：groupType=SYSTEM
     * 3. 查询所有组：groupType=ALL（默认）
     * 4. 按分类筛选：传入categoryCode参数
     * 5. 组合查询：同时指定groupType和categoryCode
     *
     * @param userId 用户ID，用于权限验证和标识用户组所有权
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量，默认10条，最大100条
     * @param groupType 组类型筛选：USER-用户组，SYSTEM-系统组，ALL-全部（默认）
     * @param categoryCode 分类编码，可选，用于筛选特定分类下的组
     * @return 分页的数字人组列表，包含统计信息
     */
    @Operation(summary = "分页查询所有数字人组列表",
               description = "通用分页查询接口，支持按组类型和分类筛选，包含分身数量统计")
    @GetMapping("/queryAllGroupsPage")
    public Result<PageResultVO<DigitalAvatarGroupListVO>> queryAllGroupsPage(
            @Parameter(description = "用户ID，用于权限验证和标识用户组所有权", required = true)
            @RequestParam String userId,
            @Parameter(description = "页码，从1开始", required = false)
            @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量，默认10条，最大100条", required = false)
            @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "组类型筛选：USER-用户组，SYSTEM-系统组，ALL-全部（默认）", required = false)
            @RequestParam(defaultValue = "ALL") String groupType,
            @Parameter(description = "分类编码，可选，用于筛选特定分类下的组", required = false)
            @RequestParam(required = false) String categoryCode
            ) {
        log.info("分页查询所有数字人组列表 - userId: {}, pageNum: {}, pageSize: {}, groupType: {}, categoryCode: {}",
                userId, pageNum, pageSize, groupType, categoryCode);

        // 参数验证
        if (userId == null || userId.trim().isEmpty()) {
            return Result.ERROR("用户ID不能为空");
        }

        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }

        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        // 验证组类型参数
        if (groupType == null || groupType.trim().isEmpty()) {
            groupType = "ALL";
        }
        groupType = groupType.toUpperCase();
        if (!"USER".equals(groupType) && !"SYSTEM".equals(groupType) && !"ALL".equals(groupType)) {
            return Result.ERROR("组类型参数无效，支持的值：USER、SYSTEM、ALL");
        }

        try {
            // 调用Service层方法进行分页查询
            return userGroupService.queryAllGroupsPage(userId, pageNum, pageSize, groupType, categoryCode);
        } catch (Exception e) {
            log.error("分页查询所有数字人组列表失败 - userId: {}, 错误信息: {}", userId, e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户组列表
     * URL: /digital/api/v1/avatars/groups/list/{userId}
     * Method: GET
     *
     * @param userId 用户ID
     * @return 用户组列表
     */
    @Operation(summary = "获取用户组列表")
    @GetMapping("/list/{userId}")
    public Result<List<DigitalUserGroupVO>> listGroupsByUserId(
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("获取用户组列表，用户ID：{}", userId);
        return userGroupService.listUserGroups(userId);
    }

    /**
     * 创建用户组
     * URL: /api/v1/avatars/groups/create
     * Method: POST
     *
     * @param groupDTO 组信息
     * @return 操作结果
     */
    @Operation(summary = "创建用户组")
    @PostMapping("/create")
    public Result<DigitalUserGroupVO> createGroup(@RequestBody DigitalUserGroupDTO groupDTO) {
        log.info("创建用户组，参数：{}", groupDTO);
        return userGroupService.createGroup(groupDTO);
    }

    /**
     * 更新用户组
     * URL: /api/v1/avatars/groups/update
     * Method: PUT
     *
     * @param groupDTO 组信息
     * @return 操作结果
     */
    @Operation(summary = "更新用户组")
    @PutMapping("/update")
    public Result<Boolean> updateGroup(@RequestBody DigitalUserGroupDTO groupDTO) {
        log.info("更新用户组，参数：{}", groupDTO);
        return userGroupService.updateGroup(groupDTO);
    }

    /**
     * 删除用户组
     * URL: /api/v1/avatars/groups/delete/{id}
     * Method: DELETE
     *
     * @param id 组ID
     * @return 操作结果
     */
    @Operation(summary = "删除用户组")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteGroup(@Parameter(description = "组ID") @PathVariable Long id) {
        log.info("删除用户组，ID：{}", id);
        return userGroupService.deleteGroup(id);
    }
}
