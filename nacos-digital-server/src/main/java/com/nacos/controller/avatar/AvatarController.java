package com.nacos.controller.avatar;

import com.nacos.result.Result;
import com.nacos.service.DigitalAvatarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数字人业务逻辑Controller
 * 专门处理数字人相关的业务逻辑操作（禅境集成、视频详情等）
 * CRUD操作已迁移到admin包的AvatarAdminController
 * 训练功能已迁移到AvatarTrainingController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "数字人业务逻辑", description = "数字人禅境集成、视频详情等业务操作")
@RestController
@RequestMapping("/api/v1/avatars")
public class AvatarController {

    @Autowired
    private DigitalAvatarService digitalAvatarService;

    // CRUD操作已迁移到 /admin/v1/avatars/*
    // 训练功能已迁移到 /api/v1/avatars/training/*
    // 此Controller专注于禅境集成、视频详情等业务逻辑操作

    // 训练功能已统一到 AvatarTrainingController
    // 原 /api/v1/avatars/train 接口已移除，请使用：
    // - /api/v1/avatars/training/start (推荐，新版本接口)
    // - /api/v1/avatars/training/train (旧版本兼容接口)
    // Legacy API (/avatar/train, /opear/train) 通过委托模式继续可用

    /**
     * 获取视频详情
     * URL: /api/v1/avatars/video/detail/{videoId}
     * Method: GET
     *
     * @param videoId 视频ID
     * @return 操作结果，包含视频详情
     */
    @Operation(summary = "获取视频详情")
    @GetMapping("/video/detail/{videoId}")
    public Result<com.nacos.model.ChanJing.model.GetVideoDetailResponse> getVideoDetail(
            @Parameter(description = "视频ID") @PathVariable String videoId) {
        log.info("获取视频详情，videoId：{}", videoId);
        return digitalAvatarService.getVideoDetail(videoId);
    }
}
