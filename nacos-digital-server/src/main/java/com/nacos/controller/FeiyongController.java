package com.nacos.controller;

import com.business.db.model.po.FlowRecordPO;
//import com.nacos.model.Azure.PersonalVoiceService;
import com.nacos.result.Result;
import com.nacos.service.CheckBalanService;
import com.nacos.service.FeiyongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 费用相关接口
 * 包括余额更新、扣费等操作
 */
@RestController
@RequestMapping("/api/feiyong")
public class FeiyongController {
    
//    @Autowired
//    private PersonalVoiceService personalVoiceService;

    @jakarta.annotation.Resource
    CheckBalanService checkBalanService;

    @javax.annotation.Resource
    FeiyongService feiyongService;

    /**
     * 更新用户余额,退费
     * @param userId
     * @param ddQuantity
     * @param flowRecordPO
     */
    @PostMapping("/updateRemainingTimes")
    public void updateRemainingTimes(@RequestParam Long userId,
                                     @RequestParam Double ddQuantity,
                                     @RequestBody FlowRecordPO flowRecordPO) {
        feiyongService.updateRemainingTimes(userId,ddQuantity,flowRecordPO);
    }

    /**
     * 老扣费方法
     */
    @PostMapping("/checkUser")
    public Result<Long> checkUser(Long userId, Double dzNumber, FlowRecordPO flowRecordPO) {
        try {
            return Result.SUCCESS(checkBalanService.checkUser(userId, dzNumber, flowRecordPO));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR();
        }
    }

    @PostMapping("/checkYue")
    public Result<Boolean> checkYue(Long userId, String feiyongType) {
        try {
            return Result.SUCCESS(feiyongService.checkYue(userId, feiyongType));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR();
        }
    }

    @PostMapping("/koufei")
    public Result<Boolean> koufei(Long userId, Long logId, String remark, String feiyongType) {
        try {
            return Result.SUCCESS(feiyongService.koufei(userId, logId, remark, feiyongType));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR();
        }
    }
}