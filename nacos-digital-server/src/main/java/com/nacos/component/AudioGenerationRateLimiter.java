package com.nacos.component;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 音频生成限流组件
 * 实现全局限流和用户级别限流
 */
@Slf4j
@Component
public class AudioGenerationRateLimiter {

    /**
     * 全局限流配置 - 每秒允许的请求数
     */
    @Value("${audio.generation.rate-limit.global:2.0}")
    private double globalRateLimit;

    /**
     * 用户级限流配置 - 每秒允许的请求数
     */
    @Value("${audio.generation.rate-limit.user:0.5}")
    private double userRateLimit;

    /**
     * 用户限流器缓存过期时间（分钟）
     */
    @Value("${audio.generation.rate-limit.user-cache-expire:30}")
    private int userCacheExpireMinutes;

    /**
     * 全局限流器
     */
    private RateLimiter globalRateLimiter;

    /**
     * 用户级限流器缓存
     * Key: userId, Value: UserRateLimiterWrapper
     */
    private final ConcurrentHashMap<String, UserRateLimiterWrapper> userRateLimiters = new ConcurrentHashMap<>();

    /**
     * 用户限流器包装类，包含限流器和最后访问时间
     */
    private static class UserRateLimiterWrapper {
        private final RateLimiter rateLimiter;
        private volatile long lastAccessTime;

        public UserRateLimiterWrapper(RateLimiter rateLimiter) {
            this.rateLimiter = rateLimiter;
            this.lastAccessTime = System.currentTimeMillis();
        }

        public RateLimiter getRateLimiter() {
            this.lastAccessTime = System.currentTimeMillis();
            return rateLimiter;
        }

        public long getLastAccessTime() {
            return lastAccessTime;
        }
    }

    @PostConstruct
    public void init() {
        // 初始化全局限流器
        globalRateLimiter = RateLimiter.create(globalRateLimit);
        log.info("音频生成限流组件初始化完成 - 全局限流: {} req/s, 用户限流: {} req/s", 
                globalRateLimit, userRateLimit);
        
        // 启动清理过期用户限流器的定时任务
        startCleanupTask();
    }

    /**
     * 全局限流检查
     * @return true-允许通过，false-被限流
     */
    public boolean tryAcquire() {
        boolean acquired = globalRateLimiter.tryAcquire();
        if (!acquired) {
            log.warn("音频生成全局限流触发");
        }
        return acquired;
    }

    /**
     * 全局限流检查（带超时）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true-允许通过，false-被限流
     */
    public boolean tryAcquire(long timeout, TimeUnit unit) {
        boolean acquired = globalRateLimiter.tryAcquire(timeout, unit);
        if (!acquired) {
            log.warn("音频生成全局限流触发（超时: {} {}）", timeout, unit);
        }
        return acquired;
    }

    /**
     * 用户级限流检查
     * @param userId 用户ID
     * @return true-允许通过，false-被限流
     */
    public boolean tryAcquireForUser(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            log.warn("用户ID为空，使用全局限流");
            return tryAcquire();
        }

        UserRateLimiterWrapper wrapper = userRateLimiters.computeIfAbsent(userId, 
                k -> new UserRateLimiterWrapper(RateLimiter.create(userRateLimit)));
        
        boolean acquired = wrapper.getRateLimiter().tryAcquire();
        if (!acquired) {
            log.warn("音频生成用户限流触发 - userId: {}", userId);
        }
        return acquired;
    }

    /**
     * 用户级限流检查（带超时）
     * @param userId 用户ID
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true-允许通过，false-被限流
     */
    public boolean tryAcquireForUser(String userId, long timeout, TimeUnit unit) {
        if (userId == null || userId.trim().isEmpty()) {
            log.warn("用户ID为空，使用全局限流");
            return tryAcquire(timeout, unit);
        }

        UserRateLimiterWrapper wrapper = userRateLimiters.computeIfAbsent(userId, 
                k -> new UserRateLimiterWrapper(RateLimiter.create(userRateLimit)));
        
        boolean acquired = wrapper.getRateLimiter().tryAcquire(timeout, unit);
        if (!acquired) {
            log.warn("音频生成用户限流触发 - userId: {}, 超时: {} {}", userId, timeout, unit);
        }
        return acquired;
    }

    /**
     * 组合限流检查：同时检查全局和用户限流
     * @param userId 用户ID
     * @return true-允许通过，false-被限流
     */
    public boolean tryAcquireBoth(String userId) {
        // 先检查全局限流
        if (!tryAcquire()) {
            return false;
        }
        
        // 再检查用户限流
        return tryAcquireForUser(userId);
    }

    /**
     * 获取当前用户限流器数量
     * @return 用户限流器数量
     */
    public int getUserRateLimiterCount() {
        return userRateLimiters.size();
    }

    /**
     * 清理指定用户的限流器
     * @param userId 用户ID
     */
    public void removeUserRateLimiter(String userId) {
        if (userRateLimiters.remove(userId) != null) {
            log.debug("已清理用户限流器 - userId: {}", userId);
        }
    }

    /**
     * 启动清理过期用户限流器的定时任务
     */
    private void startCleanupTask() {
        Thread cleanupThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    cleanupExpiredUserRateLimiters();
                    Thread.sleep(TimeUnit.MINUTES.toMillis(5)); // 每5分钟清理一次
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("清理过期用户限流器异常", e);
                }
            }
        });
        cleanupThread.setDaemon(true);
        cleanupThread.setName("AudioRateLimiter-Cleanup");
        cleanupThread.start();
        log.info("用户限流器清理任务已启动");
    }

    /**
     * 清理过期的用户限流器
     */
    private void cleanupExpiredUserRateLimiters() {
        long expireTime = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(userCacheExpireMinutes);
        int removedCount = 0;
        
        userRateLimiters.entrySet().removeIf(entry -> {
            if (entry.getValue().getLastAccessTime() < expireTime) {
                return true;
            }
            return false;
        });
        
        if (removedCount > 0) {
            log.info("清理过期用户限流器完成，清理数量: {}, 剩余数量: {}", removedCount, userRateLimiters.size());
        }
    }
}
