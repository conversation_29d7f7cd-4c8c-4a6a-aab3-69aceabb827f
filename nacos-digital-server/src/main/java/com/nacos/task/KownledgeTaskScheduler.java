package com.nacos.task;

import com.nacos.service.DigitalVectorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 知识库任务定时处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KownledgeTaskScheduler {

    private final DigitalVectorService digitalVectorService;

    /**
     * 每30秒执行一次，处理排队中的任务，老的騰訊任務
     */
    @Scheduled(fixedRate = 15000)
    public void processQueueingTasks() {
        try {
            //创建知识库
            digitalVectorService.processItemIndex();
            //添加文档
            digitalVectorService.processDocument();
            //提交更新任务
            digitalVectorService.processAddDocumentsJob();
            //查询文档更新任务状态
            digitalVectorService.processDocumentStatus();
            //删除应用空间文档(将删除顺序提前，避免应用空间数量不足)
            digitalVectorService.processDelSpaceDoc();
            //删除知识库文档
            digitalVectorService.processDelIndexDoc();
        } catch (Exception e) {
            log.error("处理排队中任务异常", e);
        }
    }



} 