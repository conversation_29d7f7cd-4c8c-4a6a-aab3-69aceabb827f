package com.nacos.task;

import com.nacos.service.ChangjinMiniService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 视频任务定时处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChangjinMIniTaskScheduler {

    private final ChangjinMiniService changjinMiniService;
    
    // 从配置文件读取定时任务执行频率，默认为5000毫秒
    @Value("${digital.video.task-scan-rate:10000}")
    private int taskScanRate;

    /**
     * 定时执行，处理各类视频任务
     * 执行频率从配置文件读取，默认5秒
     */
    @Scheduled(fixedRateString = "${digital.video.task-scan-rate:10000}")
    public void processVideoTasks() {
//        log.info("开始扫描并处理视频相关任务...");
        try {
            // 1. 处理训练任务
            // log.debug("处理视频训练任务...");
            changjinMiniService.processVideo();
            
            // 2. 处理训练状态
            // log.debug("处理视频训练状态...");
            changjinMiniService.processVideoTrainStatus();
            
            // 3. 处理语音上传
            // log.debug("处理语音上传任务...");
            changjinMiniService.processVoiceUpload();
            
            // 4. 处理语音克隆
            // log.debug("处理语音克隆任务...");
            changjinMiniService.processVoiceClone();
            
            // 5. 处理任务完成推送
            // log.debug("处理任务完成推送...");
            changjinMiniService.processFinish();
            
            // 6. 处理视频生成状态
            // log.debug("处理视频生成状态...");
            changjinMiniService.processVideoGenerationTaskStatus();
            
            // 7. 处理音频生成
            // log.debug("处理音频生成任务...");
            changjinMiniService.processVoiceGeneration();
            
            // 8. 处理视频生成
            // log.debug("处理视频生成任务...");
            changjinMiniService.processVideoGeneration();
            
            // 9. 处理视频状态
            // log.debug("处理视频状态...");
            changjinMiniService.processVideoStatus();
            
            // 10. 处理视频任务状态完成
            // log.debug("处理视频任务状态完成...");
            changjinMiniService.processVideoTaskStatusFinish();

        } catch (Exception e) {
            log.error("处理视频相关任务异常", e);
        }
    }
} 