package com.nacos.task;

import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.service.AsyncDigitalAudioService;
import com.nacos.service.IDigitalAudioTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 数字人音频生成任务定时处理器
 * 负责定期扫描并处理数据库中排队中和超时的任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DigitalAudioTaskScheduler {

//     private final AsyncDigitalAudioService asyncDigitalAudioService;

//     // 从配置文件读取定时任务执行频率，默认为5000毫秒
//     @Value("${digital.audio.task-scan-rate:10000}")
//     private int taskScanRate;

//     /**
//      * 初始化方法，在Spring容器启动后自动调用
//      */
//     @PostConstruct
//     public void init() {
//         log.info("初始化数字人音频生成任务定时处理器...");
//         // 确保AsyncDigitalAudioService的线程池被初始化
//         asyncDigitalAudioService.init();
//         log.info("数字人音频生成任务定时处理器初始化完成。");
//     }

//     /**
//      * 每10秒执行一次，处理排队中的音频生成任务和超时任务
//      * 
//      */
//     @Scheduled(fixedRateString = "${digital.audio.task-scan-rate:10000}")
//     public void processAudioTasks() {
// //        log.info("开始扫描并处理音频生成任务...");
//         try {
//             // 1. 处理排队中的任务 (Status = 0)
//             asyncDigitalAudioService.processQueuingAudioTasks();

//             // 2. 处理超时任务 (Status = 1, 且超过设定时间)
//             asyncDigitalAudioService.processTimeoutAudioTasks();

//         } catch (Exception e) {
//             log.error("处理音频生成任务异常", e);
//         }
//     }

//     // 在应用程序关闭时优雅地关闭线程池
//     public void shutdown() {
//         asyncDigitalAudioService.shutdown();
//     }
}