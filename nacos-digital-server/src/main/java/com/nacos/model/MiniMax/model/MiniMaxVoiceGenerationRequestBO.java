package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Pattern;
import java.util.Arrays;
import java.util.List;

@Data
@Slf4j
@Schema(description = "MINIMAX 语音生成请求参数")
public class MiniMaxVoiceGenerationRequestBO {

    @NotNull(message = "模型名称不能为空")
    @Schema(description = "请求的模型版本：speech-01-turbo、speech-01-240228、speech-01-turbo-240228、speech-01-hd")
    private String model;

    @NotNull(message = "文本内容不能为空")
    @Size(max = 10000, message = "文本长度不能超过10000字符")
    @Schema(description = "待合成的文本，长度限制<10000字符")
    private String text;

    @Schema(description = "音色设置")
    @JSONField(name = "voice_setting")
    private VoiceSetting voiceSetting = new VoiceSetting();

    @Data
    public static class VoiceSetting {
        @Min(value = 0, message = "语速最小值为0.5")
        @Max(value = 2, message = "语速最大值为2")
        @Schema(description = "生成声音的语速，范围[0.5,2]，默认值为1.0")
        private Float speed = 1.0f;

        @Min(value = 0, message = "音量最小值为0.1")
        @Max(value = 10, message = "音量最大值为10")
        @Schema(description = "生成声音的音量，范围(0,10]，默认值为1.0")
        private Float vol = 1.0f;

        @Min(value = -12, message = "语调最小值为-12")
        @Max(value = 12, message = "语调最大值为12")
        @Schema(description = "生成声音的语调，范围[-12,12]，默认值为0")
        private Integer pitch = 0;

        @NotNull(message = "音色ID不能为空")
        @Schema(description = "请求的音色编号")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(description = "控制合成语音的情绪")
        private String emotion;

        @Schema(description = "是否支持朗读latex公式，默认为false")
        @JSONField(name = "latex_read")
        private Boolean latexRead = false;
    }

    @Schema(description = "音频设置")
    @JSONField(name = "audio_setting")
    private AudioSetting audioSetting = new AudioSetting();

    @Data
    @Slf4j
    public static class AudioSetting {
        @Schema(description = "生成声音的采样率，范围【8000，16000，22050，24000，32000，44100】，32000为默认值")
        @JSONField(name = "sample_rate")
        private Integer sampleRate = 32000;

        // 验证sampleRate方法
        public void setSampleRate(Integer sampleRate) {
            List<Integer> allowedValues = Arrays.asList(8000, 16000, 22050, 24000, 32000, 44100);
            if (sampleRate != null && !allowedValues.contains(sampleRate)) {
                log.warn("无效的sampleRate值: {}，必须是以下值之一: {}", sampleRate, allowedValues);
                // 设置为默认值32000
                this.sampleRate = 32000;
            } else {
                this.sampleRate = sampleRate;
            }
        }

        @Schema(description = "生成声音的比特率，范围【32000，64000，128000，256000】，默认值为128000")
        private Integer bitrate = 128000;

        // 验证bitrate方法
        public void setBitrate(Integer bitrate) {
            List<Integer> allowedValues = Arrays.asList(32000, 64000, 128000, 256000);
            if (bitrate != null && !allowedValues.contains(bitrate)) {
                log.warn("无效的bitrate值: {}，必须是以下值之一: {}", bitrate, allowedValues);
                // 设置为默认值128000
                this.bitrate = 128000;
            } else {
                this.bitrate = bitrate;
            }
        }

        @Schema(description = "生成的音频格式，默认mp3")
        private String format = "mp3";

        @Schema(description = "生成音频的声道数，默认1：单声道")
        private Integer channel = 1;
    }

    @Schema(description = "发音词典")
    @JSONField(name = "pronunciation_dict")
    private PronunciationDict pronunciationDict;

    @Data
    public static class PronunciationDict {
        @Schema(description = "替换需要特殊标注的文字、符号及对应的注音")
        private List<String> tone;
    }

    @Schema(description = "音色混合权重")
    @JSONField(name = "timber_weights")
    private List<TimberWeight> timberWeights;

    @Data
    public static class TimberWeight {
        @NotNull(message = "音色ID不能为空")
        @Schema(description = "音色ID")
        @JSONField(name = "voice_id")
        private String voiceId;

        @NotNull(message = "权重不能为空")
        @Min(value = 1, message = "权重最小值为1")
        @Max(value = 100, message = "权重最大值为100")
        @Schema(description = "权重，范围[1,100]")
        private Integer weight;
    }

    @Schema(description = "是否流式输出")
    private Boolean stream = false;

    @Schema(description = "增强对指定的小语种和方言的识别能力")
    @JSONField(name = "language_boost")
    private String languageBoost;

    @Schema(description = "控制是否开启字幕服务的开关，默认为false。此参数仅对speech-01-turbo和speech-01-hd有效")
    @JSONField(name = "subtitle_enable")
    private Boolean subtitleEnable = false;
}