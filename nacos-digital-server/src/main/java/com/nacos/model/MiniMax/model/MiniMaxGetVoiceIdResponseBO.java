package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "MINIMAX获取可用Voice ID响应实体")
public class MiniMaxGetVoiceIdResponseBO {

    @Schema(title = "系统预定义音色")
    @JSONField(name = "system_voice")
    private List<SystemVoice> systemVoice;

    @Schema(title = "快速复刻音色")
    @JSONField(name = "voice_cloning")
    private List<VoiceCloning> voiceCloning;

    @Schema(title = "音色生成音色")
    @JSONField(name = "voice_generation")
    private List<VoiceGeneration> voiceGeneration;

    @Schema(title = "音乐生成音色")
    @JSONField(name = "music_generation")
    private List<MusicGeneration> musicGeneration;

    @Schema(title = "状态码及其详情")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static class BaseResp {
        @Schema(title = "状态码")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;
    }

    @Data
    public static class SystemVoice {
        @Schema(title = "音色ID")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(title = "音色名称")
        @JSONField(name = "voice_name")
        private String voiceName;

        @Schema(title = "描述信息")
        private String description;
    }

    @Data
    public static class VoiceCloning {
        @Schema(title = "音色ID")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(title = "描述信息")
        private List<String> description;

        @Schema(title = "创建时间")
        @JSONField(name = "created_time")
        private String createdTime;
    }

    @Data
    public static class VoiceGeneration {
        @Schema(title = "音色ID")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(title = "描述信息")
        private List<String> description;

        @Schema(title = "创建时间")
        @JSONField(name = "created_time")
        private String createdTime;
    }

    @Data
    public static class MusicGeneration {
        @Schema(title = "人声音色ID")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(title = "伴奏ID")
        @JSONField(name = "instrumental_id")
        private String instrumentalId;

        @Schema(title = "创建时间")
        @JSONField(name = "created_time")
        private String createdTime;
    }


} 