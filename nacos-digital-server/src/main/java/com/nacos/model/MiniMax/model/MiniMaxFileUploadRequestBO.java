package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
@Schema(title = "MINIMAX文件上传请求实体")
public class MiniMaxFileUploadRequestBO {

    @Schema(title = "文件用途")
    @JSONField(name = "purpose")
    private String purpose;

    @Schema(title = "文件")
    @JSONField(serialize = false)
    private MultipartFile file;

    public MiniMaxFileUploadRequestBO() {}
    
    public MiniMaxFileUploadRequestBO(String purpose, MultipartFile file) {
        this.purpose = purpose;
        this.file = file;
    }
}
