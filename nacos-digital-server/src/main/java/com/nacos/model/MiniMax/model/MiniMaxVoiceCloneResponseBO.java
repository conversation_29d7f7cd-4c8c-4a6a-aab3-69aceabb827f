package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "MINIMAX声音克隆响应实体")
public class MiniMaxVoiceCloneResponseBO {

    @Schema(title = "输入音频是否命中风控")
    @JSONField(name = "input_sensitive")
    private Boolean inputSensitive;

    @Schema(title = "输入音频命中风控的类型", description = "风控类型代码")
    private Integer inputSensitiveType;

    @Schema(title = "试听音频")
    @JSONField(name = "demo_audio")
    private String demoAudio;

    @Schema(title = "状态码及其详情")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static class BaseResp {
        @Schema(title = "状态码", description = "0:成功; 1000:未知错误; 1001:超时; 1002:触发限流; 1004:鉴权失败; 1013:服务内部错误; 2013:输入格式信息不正常; 2038:无复刻权限")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;
    }
}
