package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.nacos.model.MiniMax.enums.MiniMaxVoiceCloneEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Schema(title = "MINIMAX声音克隆请求实体")
public class MiniMaxVoiceCloneRequestBO {

    /**
     * 支持上传的文件需遵从以下规范：
     * 上传的音频文件格式需为：mp3、m4a、wav格式；
     * 上传的音频文件的时长最少应不低于10秒，最长应不超过5分钟；
     * 上传的音频文件大小需不超过20mb。
     */
    @NotNull(message = "音频文件ID不能为空")
    @Schema(title = "音频文件ID", description = "支持mp3、m4a、wav格式的音频文件ID，int64类型", required = true)
    @JSONField(name = "file_id")
    private Long fileId;

    /**
     * 用户进行自定义voice_id时需注意：
     * 1. 自定义的voice_id长度范围[8,256]
     * 2. 首字符必须为英文字母
     * 3. 允许数字、字母、-、_
     * 4. 末位字符不可为-、_
     * （正确示例如下：MiniMax001）如创建的voice_id与之前重复则会进行报错.
     */
    @NotNull(message = "声音ID不能为空")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_-]{6,254}[a-zA-Z0-9]$", message = "声音ID格式不正确")
    @Schema(title = "声音ID", required = true)
    @JSONField(name = "voice_id")
    private String voiceId;

    @NotNull(message = "克隆提示不能为空")
    @Schema(title = "克隆提示", required = true)
    @JSONField(name = "clone_prompt")
    private ClonePrompt clonePrompt;

    @Schema(title = "验证文本")
    @JSONField(name = "text_validation")
    private String textValidation;

    @Schema(title = "文本")
    @JSONField(name = "text")
    private String text;

    @Schema(title = "模型", description = "声音克隆的模型，可选：speech-01-turbo、speech-01-240228、speech-01-turbo-240228、speech-01-hd")
    @JSONField(name = "model")
    private String model = MiniMaxVoiceCloneEnum.Model.SPEECH_01_TURBO.getCode();

    @Schema(title = "准确度", description = "声音克隆的准确度，取值范围[0,1]，默认0.7")
    @JSONField(name = "accuracy")
    private Double accuracy;

    @Schema(title = "是否需要降噪", description = "是否需要降噪，默认取false")
    @JSONField(name = "need_noise_reduction")
    private Boolean needNoiseReduction;

    @Schema(title = "是否需要音量归一化", description = "是否需要音量归一化，默认取false")
    @JSONField(name = "need_volume_normalization")
    private Boolean needVolumeNormalization;

    @Data
    public static class ClonePrompt {
        @NotNull(message = "提示音频不能为空")
        @Schema(title = "提示音频", description = "音频prompt参数，填入通过File接口中的upload上传示例音频得到的\"file_id\"，示例音频时长必须小于8s", required = true)
        @JSONField(name = "prompt_audio")
        private Long promptAudio;

        @NotNull(message = "提示文本不能为空")
        @Schema(title = "提示文本", description = "音频prompt参数，填入示例音频的对应文本，需确保和音频内容一致，句末需有标点符号做结尾", required = true)
        @JSONField(name = "prompt_text")
        private String promptText;
    }

    public MiniMaxVoiceCloneRequestBO() {}

    public MiniMaxVoiceCloneRequestBO(String fileId, String voiceId, String textValidation, Double accuracy, Long promptAudio, String promptText) {
        this.fileId = Long.parseLong(fileId);
        this.voiceId = voiceId;
        this.textValidation = textValidation;
        this.accuracy = accuracy;
        
        ClonePrompt prompt = new ClonePrompt();
        prompt.setPromptAudio(promptAudio);
        prompt.setPromptText(promptText);
        this.clonePrompt = prompt;
    }
}
