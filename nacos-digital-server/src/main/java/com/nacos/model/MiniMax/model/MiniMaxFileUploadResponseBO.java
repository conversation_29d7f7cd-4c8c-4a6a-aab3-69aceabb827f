package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "MINIMAX文件上传响应实体")
public class MiniMaxFileUploadResponseBO {

    @Schema(title = "文件信息")
    @JSONField(name = "file")
    private FileInfo file;

    @Schema(title = "状态码及其详情")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static class FileInfo {
        @Schema(title = "文件ID")
        @JSONField(name = "file_id")
        private String fileId;

        @Schema(title = "文件大小")
        @JSONField(name = "bytes")
        private Long bytes;

        @Schema(title = "创建时间")
        @JSONField(name = "created_at")
        private Long createdAt;

        @Schema(title = "文件名")
        @JSONField(name = "filename")
        private String filename;

        @Schema(title = "用途")
        @JSONField(name = "purpose")
        private String purpose;
    }

    @Data
    public static class BaseResp {
        @Schema(title = "状态码")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;
    }
}
