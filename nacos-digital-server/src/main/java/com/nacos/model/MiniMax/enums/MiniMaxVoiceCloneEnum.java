package com.nacos.model.MiniMax.enums;

import lombok.Getter;

/**
 * MiniMax 声音克隆参数枚举
 */
public class MiniMaxVoiceCloneEnum {

    /**
     * 声音克隆模型枚举
     */
    @Getter
    public enum Model {
        SPEECH_01_TURBO("speech-01-turbo", "标准模型"),
        SPEECH_01_240228("speech-01-240228", "2024年2月28日更新模型"),
        SPEECH_01_TURBO_240228("speech-01-turbo-240228", "2024年2月28日更新的Turbo模型"),
        SPEECH_01_HD("speech-01-hd", "高清模型");

        private final String code;
        private final String description;

        Model(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static Model getByCode(String code) {
            for (Model model : values()) {
                if (model.getCode().equals(code)) {
                    return model;
                }
            }
            return null;
        }
    }

    /**
     * 声音克隆文本枚举
     */
    @Getter
    public enum Text {
        GREETING("您好，欢迎使用萤火虫AI，我是你的虚拟助理。", "标准问候语"),
        INTRODUCTION("我可以为您提供全天候的智能服务，包括问答、咨询等功能。", "自我介绍"),
        BUSINESS("欢迎咨询我们的业务，我会为您提供专业的解答。", "业务咨询"),
        CUSTOMER_SERVICE("您好，我是您的专属客服，请问有什么可以帮您？", "客服问候"),
        PROFESSIONAL("作为一名专业的AI助手，我将竭诚为您服务。", "专业服务");

        private final String content;
        private final String description;

        Text(String content, String description) {
            this.content = content;
            this.description = description;
        }
    }

    /**
     * 声音克隆准确度建议值
     */
    @Getter
    public enum Accuracy {
        LOW(0.3, "低准确度，速度快"),
        MEDIUM(0.7, "中等准确度，平衡速度和质量"),
        HIGH(0.9, "高准确度，质量好但速度慢");

        private final double value;
        private final String description;

        Accuracy(double value, String description) {
            this.value = value;
            this.description = description;
        }
    }

    /**
     * 声音克隆音频要求
     */
    public static class AudioRequirement {
        // 支持的音频格式
        public static final String[] SUPPORTED_FORMATS = {"mp3", "m4a", "wav"};
        // 最小时长（秒）
        public static final int MIN_DURATION = 10;
        // 最大时长（秒）
        public static final int MAX_DURATION = 300;
        // 最大文件大小（MB）
        public static final int MAX_FILE_SIZE = 20;
    }
} 