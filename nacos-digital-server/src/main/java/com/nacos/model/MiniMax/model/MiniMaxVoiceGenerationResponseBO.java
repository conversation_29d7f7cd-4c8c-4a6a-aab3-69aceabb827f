package com.nacos.model.MiniMax.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

@Data
@Schema(description = "MINIMAX 语音生成响应参数")
public class MiniMaxVoiceGenerationResponseBO {

    @Schema(description = "音频URL（系统扩展字段，用于存储上传到OSS后的URL，非MiniMax API原始返回字段）")
    private String audioUrl;

    @Schema(description = "响应数据")
    private AudioData data;

    @Schema(description = "追踪ID")
    @JSONField(name = "trace_id")
    private String traceId;

    @Schema(description = "额外信息")
    @JSONField(name = "extra_info")
    private ExtraInfo extraInfo;

    @Schema(description = "基础响应信息")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static class AudioData {
        @Schema(description = "合成后的音频片段，采用hex编码")
        private String audio;

        @Schema(description = "合成的字幕下载链接，音频文件对应的字幕，精确到句（不超过50字），单位为毫秒，格式为json")
        @JSONField(name = "subtitle_file")
        private String subtitleFile;

        @Schema(description = "当前音频流状态，1表示合成中，2表示合成结束")
        private Integer status;
    }

    @Data
    public static class ExtraInfo {
        @Schema(description = "音频时长，精确到毫秒")
        @JSONField(name = "audio_length")
        private Long audioLength;

        @Schema(description = "采样率")
        @JSONField(name = "audio_sample_rate")
        private Long audioSampleRate;

        @Schema(description = "音频大小，单位为字节")
        @JSONField(name = "audio_size")
        private Long audioSize;

        @Schema(description = "比特率")
        @JSONField(name = "audio_bitrate")
        private Long audioBitrate;

        @Schema(description = "生成音频文件的格式")
        @JSONField(name = "audio_format")
        private String audioFormat;

        @Schema(description = "音频声道数")
        @JSONField(name = "audio_channel")
        private Long audioChannel;

        @Schema(description = "非法字符占比")
        @JSONField(name = "invisible_character_ratio")
        private Double invisibleCharacterRatio;

        @Schema(description = "计费字符数，本次语音生成的计费字符数")
        @JSONField(name = "usage_characters")
        private Long usageCharacters;
    }

    @Data
    public static class BaseResp {
        @Schema(description = "状态码")
        @JSONField(name = "status_code")
        private Long statusCode;

        @Schema(description = "状态详情")
        @JSONField(name = "status_msg")
        private String statusMsg;
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查响应是否成功
     *
     * @return true if successful, false otherwise
     */
    public boolean isSuccess() {
        return baseResp != null &&
               baseResp.getStatusCode() != null &&
               baseResp.getStatusCode() == 0L;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息，如果成功则返回null
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }

        if (baseResp != null) {
            String statusMsg = baseResp.getStatusMsg();
            Long statusCode = baseResp.getStatusCode();

            if (statusMsg != null && !statusMsg.trim().isEmpty()) {
                return statusMsg;
            }

            if (statusCode != null) {
                // 使用MiniMaxApiUtil获取友好的错误信息
                return com.nacos.model.MiniMax.MiniMaxApiUtil.getErrorMessageByCode(String.valueOf(statusCode));
            }
        }

        return "未知错误";
    }

    /**
     * 检查是否包含音频数据
     *
     * @return true if has audio data, false otherwise
     */
    public boolean hasAudioData() {
        return data != null &&
               data.getAudio() != null &&
               !data.getAudio().trim().isEmpty();
    }

    /**
     * 获取音频时长（毫秒）
     *
     * @return 音频时长，如果无法获取则返回0
     */
    public Long getAudioDuration() {
        if (extraInfo != null && extraInfo.getAudioLength() != null) {
            return extraInfo.getAudioLength();
        }
        return 0L;
    }

    /**
     * 获取音频大小（字节）
     *
     * @return 音频大小，如果无法获取则返回0
     */
    public Long getAudioSize() {
        if (extraInfo != null && extraInfo.getAudioSize() != null) {
            return extraInfo.getAudioSize();
        }
        return 0L;
    }

    /**
     * 获取音频格式
     *
     * @return 音频格式，如果无法获取则返回"unknown"
     */
    public String getAudioFormat() {
        if (extraInfo != null && extraInfo.getAudioFormat() != null) {
            return extraInfo.getAudioFormat();
        }
        return "unknown";
    }

    /**
     * 检查音频是否合成完成
     *
     * @return true if audio synthesis is completed, false otherwise
     */
    public boolean isAudioCompleted() {
        return data != null &&
               data.getStatus() != null &&
               data.getStatus() == 2; // 2表示合成结束
    }

    /**
     * 获取状态码
     *
     * @return 状态码，如果无法获取则返回null
     */
    public Long getStatusCode() {
        return baseResp != null ? baseResp.getStatusCode() : null;
    }


}