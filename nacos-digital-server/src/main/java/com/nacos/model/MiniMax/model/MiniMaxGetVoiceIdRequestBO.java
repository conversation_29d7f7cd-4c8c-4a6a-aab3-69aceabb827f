package com.nacos.model.MiniMax.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "MINIMAX获取可用Voice ID请求实体")
public class MiniMaxGetVoiceIdRequestBO {

    @Schema(title = "音色类型", description = "支持以下取值：system（系统音色），voice_cloning（快速复刻的音色），voice_generation（文生音色接口生成的音色），music_generation（音乐生成产生的人声或者伴奏音色），all（以上全部）")
    @JSONField(name = "voice_type")
    private String voiceType;
} 