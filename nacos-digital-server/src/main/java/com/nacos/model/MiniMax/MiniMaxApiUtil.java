package com.nacos.model.MiniMax;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.business.utils.BThirdPartyKey;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.enums.DictConfigEnum;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceCloneRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceCloneResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationResponseBO;
import com.nacos.result.Result;
import com.nacos.utils.DigitalFileUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

@Slf4j
@Schema(title = "MiniMaxApiUtil API工具类")
public class MiniMaxApiUtil {

    public static final String STATUS_FAIL = "fail";
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_PROCESSING = "processing";

    /**
     * 获取Minimax API Key
     *
     * @return API Key，获取失败返回null
     */
    private static String getMinimaxApiKey() {
        try {
            HashMap<Long, String> dictConfigMap = BThirdPartyKey
                    .getSecretKeyInfo(DictConfigEnum.MINIMAX_API_KEY.getDictType());
            if (dictConfigMap == null || dictConfigMap.isEmpty()) {
                log.error("Minimax API配置错误：无法获取到有效的secretKey");
                return null;
            }
            String apiKey = dictConfigMap.get(DictConfigEnum.MINIMAX_API_KEY.getDictKey());
            if (!StringUtils.hasText(apiKey)) {
                log.error("Minimax API配置错误：secretKey为空");
                return null;
            }
            return apiKey;
        } catch (Exception e) {
            log.error("获取Minimax API Key失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取Minimax Group ID
     *
     * @return Group ID，获取失败返回null
     */
    private static String getMinimaxGroupId() {
        try {
            // TODO: 修复 DictConfigEnum.MINIMAX_GROUP_ID 编译错误
            // HashMap<Long, String> dictConfigMap = BThirdPartyKey
            //         .getSecretKeyInfo(DictConfigEnum.MINIMAX_GROUP_ID.getDictType());
            // if (dictConfigMap == null || dictConfigMap.isEmpty()) {
            //     log.error("Minimax GroupId配置错误：无法获取到有效的GroupId");
            //     return null;
            // }
            // String groupId = dictConfigMap.get(DictConfigEnum.MINIMAX_GROUP_ID.getDictKey());
            // if (!StringUtils.hasText(groupId)) {
            //     log.error("Minimax GroupId配置错误：GroupId为空");
            //     return null;
            // }
            // return groupId;
            log.warn("getMinimaxGroupId 方法暂时被注释，返回默认值");
            return "default-group-id";
        } catch (Exception e) {
            log.error("获取Minimax Group ID失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据MiniMax API错误码返回对应的错误描述
     * 参考官方文档提供用户友好的错误信息和解决建议
     *
     * @param errorCode 错误码
     * @return 错误描述
     */
    public static String getErrorMessageByCode(String errorCode) {
        if (errorCode == null) {
            return "未知错误";
        }

        return switch (errorCode) {
            case "1000" -> "服务内部错误，请稍后重试";
            case "1001" -> "请求超时，请检查网络连接或稍后重试";
            case "1002" -> "请求频率超限，请降低调用频率后重试";
            case "1004" -> "认证失败，请检查API密钥是否正确或权限是否足够";
            case "1013" -> "服务暂时不可用，请稍后重试";
            case "2013" -> "输入参数格式错误，请检查文本内容、音色设置等参数格式";
            case "2002" -> "输入文本过长，请确保文本长度不超过10000字符";
            case "2003" -> "音色ID无效，请检查音色ID是否正确";
            case "2004" -> "音频设置参数无效，请检查采样率、比特率等音频参数";
            case "2005" -> "语速参数超出范围，请确保语速在0.5-2.0之间";
            case "2006" -> "音量参数超出范围，请确保音量在0.1-10之间";
            case "2007" -> "语调参数超出范围，请确保语调在-12到12之间";
            case "4001" -> "账户余额不足，请充值后重试";
            case "4002" -> "账户已被限制使用，请联系客服";
            case "4003" -> "API调用次数已达上限，请升级套餐或等待重置";
            case "5001" -> "音频生成失败，请检查输入参数或稍后重试";
            case "5002" -> "音频编码失败，请稍后重试";
            case "5003" -> "音频文件过大，请减少文本长度或调整音频参数";
            default -> "未知错误，错误码: " + errorCode + "，请联系技术支持";
        };
    }

    /**
     * 上传文件到MINIMAX
     */
    public static String uploadFileToMiniMax(MiniMaxFileUploadRequestBO miniMaxFileUploadRequestBO) {
        String methodName = "uploadFileToMiniMax";
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            log.error("[{}] Minimax API配置错误，请联系管理员", methodName);
            return "Minimax API配置错误，请联系管理员";
        }
        MiniMaxFileUploadResponseBO responseBO = MiniMaxApi.uploadFile(miniMaxFileUploadRequestBO, apiKey);
        if (responseBO != null) {
            MiniMaxFileUploadResponseBO.BaseResp baseResp = responseBO.getBaseResp();
            if (baseResp != null) {
                Integer statusCode = baseResp.getStatusCode();
                String statusMsg = baseResp.getStatusMsg();
                switch (statusCode) {
                    case 0 -> {
                        if (STATUS_SUCCESS.equals(statusMsg)) {
                            return responseBO.getFile().getFileId();
                        } else {
                            String str = "MINIMAX文件上传失败，原因：" + statusMsg + " fileId="
                                    + miniMaxFileUploadRequestBO.getFile().getOriginalFilename();
                            log.error("[{}]文件上传失败，原因：{}", methodName, str);
                            return str;
                        }
                    }
                    case 1008 -> {
                        // String dateKey = RedisUtil.REDIS_MINIMAX_ACCOUNT_PREFIX + "-" + "upload";
                        // String value = RedisUtil.getValue(dateKey);
                        // if (StringUtils.isBlank(value)) {
                        // BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "MINIMAX需要充值！",
                        // "uploadFileToMiniMax.resp " + JSON.toJSONString(responseBO));
                        // RedisUtil.setValue(dateKey, "1");
                        // }
                        return null;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 上传文件到MINIMAX，并返回Result对象
     *
     * @param miniMaxFileUploadRequestBO 文件上传请求参数
     * @param apiKey                     API令牌
     * @return Result对象，成功时包含文件ID，失败时包含错误信息
     */
    public static Result<String> uploadFileToMiniMaxNew(MiniMaxFileUploadRequestBO miniMaxFileUploadRequestBO) {
        String methodName = "uploadFileToMiniMax";
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            log.error("[{}] Minimax API配置错误，请联系管理员", methodName);
            return Result.ERROR("Minimax API配置错误，请联系管理员");
        }
        MiniMaxFileUploadResponseBO responseBO = MiniMaxApi.uploadFile(miniMaxFileUploadRequestBO, apiKey);
        if (responseBO != null) {
            MiniMaxFileUploadResponseBO.BaseResp baseResp = responseBO.getBaseResp();
            if (baseResp != null) {
                Integer statusCode = baseResp.getStatusCode();
                String statusMsg = baseResp.getStatusMsg();
                switch (statusCode) {
                    case 0 -> {
                        if (STATUS_SUCCESS.equals(statusMsg)) {
                            return Result.SUCCESS(responseBO.getFile().getFileId());
                        } else {
                            String str = "MINIMAX文件上传失败，原因：" + statusMsg + " fileId="
                                    + miniMaxFileUploadRequestBO.getFile().getOriginalFilename();
                            log.error("[{}]文件上传失败，原因：{}", methodName, str);
                            return Result.ERROR(str);
                        }
                    }
                    case 1008 -> {
                        return Result.ERROR("minimax需要充值");
                    }
                }
            }
        }
        return Result.ERROR("minimax其他错误");
    }

    /**
     * 声音克隆
     */
    public static String voiceCloning(MiniMaxVoiceCloneRequestBO requestBO, String userId) {
        String methodName = "voiceCloning";
        log.info("[{}]开始声音克隆，用户ID：{}", methodName, userId);
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            log.error("[{}] Minimax API配置错误，请联系管理员", methodName);
            return "Minimax API配置错误，请联系管理员";
        }

        MiniMaxVoiceCloneResponseBO responseBO = MiniMaxApi.voiceClone(requestBO, apiKey);
        if (responseBO != null) {
            MiniMaxVoiceCloneResponseBO.BaseResp baseResp = responseBO.getBaseResp();
            if (baseResp != null) {
                Integer statusCode = baseResp.getStatusCode();
                String statusMsg = baseResp.getStatusMsg();
                switch (statusCode) {
                    case 0 -> {
                        if (STATUS_SUCCESS.equals(statusMsg)) {
                            // 如果示例音频不为空，下载音频文件
                            String ossUrl = null;
                            if (responseBO.getDemoAudio() != null) {
                                ossUrl = DigitalFileUtil.uploadDigitalResource(responseBO.getDemoAudio(), null, userId,
                                        null, 10, true);
                            }
                            return ossUrl;
                        } else {
                            String str = "MINIMAX声音克隆失败，原因：" + statusMsg;
                            log.error("[{}]声音克隆失败，原因：{}", methodName, str);
                            return str;
                        }
                    }
                    case 1008 -> {
                        // String dateKey = RedisUtil.REDIS_MINIMAX_ACCOUNT_PREFIX + "-" +
                        // "voice-clone";
                        // String value = RedisUtil.getValue(dateKey);
                        // if (StringUtils.isBlank(value)) {
                        // BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "MINIMAX需要充值！",
                        // "voiceClone.resp " + JSON.toJSONString(responseBO));
                        // RedisUtil.setValue(dateKey, "1");
                        // }
                        return null;
                    }
                    default -> {
                        log.error("[{}]MINIMAX API 声音克隆请求失败：statusCode={}, statusMsg={}", methodName, statusCode,
                                statusMsg);
                        return null;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 声音克隆
     */
    public static Result<String> voiceCloningNew(MiniMaxVoiceCloneRequestBO requestBO, String userId) {
        String methodName = "voiceCloning";
        log.info("[{}]开始声音克隆，用户ID：{}", methodName, userId);
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            return Result.ERROR("Minimax API配置错误，请联系管理员");
        }

        MiniMaxVoiceCloneResponseBO responseBO = MiniMaxApi.voiceClone(requestBO, apiKey);
        if (responseBO != null) {
            MiniMaxVoiceCloneResponseBO.BaseResp baseResp = responseBO.getBaseResp();
            if (baseResp != null) {
                Integer statusCode = baseResp.getStatusCode();
                String statusMsg = baseResp.getStatusMsg();
                log.error("[{}]MINIMAX API 声音克隆请求结果：statusCode={}, statusMsg={}", methodName, statusCode, statusMsg);
                switch (statusCode) {
                    case 0 -> {
                        if (STATUS_SUCCESS.equals(statusMsg)) {
                            // 如果示例音频不为空，下载音频文件
                            String ossUrl = null;
                            if (responseBO.getDemoAudio() != null) {
                                ossUrl = DigitalFileUtil.uploadDigitalResource(responseBO.getDemoAudio(), null, userId,
                                        null, 10, true);
                            }
                            return Result.SUCCESS(ossUrl);
                        } else {
                            String str = "MINIMAX声音克隆失败，原因：" + statusMsg;
                            log.error("[{}]声音克隆失败，原因：{}", methodName, str);
                            return Result.ERROR(str);
                        }
                    }
                    case 1008 -> {
                        return Result.ERROR("minimax需要充值");
                    }
                    default -> {
                        log.error("[{}]MINIMAX API 声音克隆请求失败：statusCode={}, statusMsg={}", methodName, statusCode,
                                statusMsg);
                        return Result.ERROR("系统异常，请稍后重试" + "MINIMAX API 错误信息：" + responseBO.getBaseResp().getStatusMsg()
                                + "--" + responseBO.getBaseResp().getStatusCode());
                    }
                }
            }
        }
        return null;
    }

    /**
     * 上传数字人相关文件-系统
     * 
     * @deprecated 请使用
     *             {@link DigitalFileUtil#uploadDigitalResource(Object, String, String, String, Integer, Boolean)}
     */
    @Deprecated
    public static String uploadDigitalResource(byte[] file, String fileName, String userId, String groupId,
            Integer type, Boolean isSystem) {
        return DigitalFileUtil.uploadDigitalResource(file, fileName, userId, groupId, type, isSystem);
    }

    /**
     * 上传数字人相关文件-用户
     * 
     * @deprecated 请使用
     *             {@link DigitalFileUtil#uploadDigitalResource(Object, String, String, String, Integer, Boolean)}
     */
    @Deprecated
    public static String uploadDigitalResource(byte[] file, String fileName, String userId, String groupId,
            Integer type) {
        return DigitalFileUtil.uploadDigitalResource(file, fileName, userId, groupId, type, null);
    }

    /**
     * 获取可用Voice ID
     */
    public static MiniMaxGetVoiceIdResponseBO getVoiceId(String voiceType) {
        String methodName = "getVoiceId";
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            log.error("[{}] Minimax API配置错误，请联系管理员", methodName);
            return null;
        }
        try {
            MiniMaxGetVoiceIdRequestBO requestBO = new MiniMaxGetVoiceIdRequestBO();
            requestBO.setVoiceType(voiceType);
            MiniMaxGetVoiceIdResponseBO responseBO = MiniMaxApi.getVoiceId(requestBO, apiKey);

            MiniMaxGetVoiceIdResponseBO.BaseResp baseResp = responseBO.getBaseResp();
            if (baseResp != null) {
                Integer statusCode = baseResp.getStatusCode();
                String statusMsg = baseResp.getStatusMsg();
                switch (statusCode) {
                    case 0 -> {
                        if (STATUS_SUCCESS.equals(statusMsg)) {
                            return responseBO;
                        } else {
                            String str = "MINIMAX声音克隆失败，原因：" + statusMsg;
                            log.error(str);
                            return null;
                        }
                    }
                    case 1008 -> {
                        // String dateKey = RedisUtil.REDIS_MINIMAX_ACCOUNT_PREFIX + "-" + "voice-id";
                        // String value = RedisUtil.getValue(dateKey);
                        // if (StringUtils.isBlank(value)) {
                        // BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "MINIMAX需要充值！",
                        // "getVoiceId.resp " + JSON.toJSONString(responseBO));
                        // RedisUtil.setValue(dateKey, "1");
                        // }
                        return null;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[{}] 获取可用Voice ID失败：{}", methodName, e.getMessage(), e);
            return null;
        }

    }

    /**
     * 一站式语音生成服务：生成并处理音频数据
     *
     * @param requestBO 语音生成请求参数
     * @param userId    用户ID
     * @param fileName  文件名（可选，如果为null则使用UUID生成）
     * @param type      文件类型，指定存储目录(如1-头像,2-语音包,3-视频,4-临时文件等)
     * @return Result对象，成功时包含MiniMaxVoiceGenerationResponseBO（已设置audioUrl），失败时包含错误信息
     */
    public static Result<MiniMaxVoiceGenerationResponseBO> generateAndProcessAudio(
            MiniMaxVoiceGenerationRequestBO requestBO,
            String userId, String fileName, Integer type) {
        String methodName = "generateAndProcessAudio";
        log.info("[{}] 开始语音生成，用户ID：{}", methodName, userId);

        // 1. 验证配置
        String apiKey = getMinimaxApiKey();
        if (apiKey == null) {
            log.error("[{}] Minimax API配置错误，请联系管理员", methodName);
            return Result.ERROR("Minimax API配置错误，请联系管理员");
        }

        String groupId = getMinimaxGroupId();
        // if (groupId == null) {
        //     log.error("[{}] Minimax GroupId配置错误，请联系管理员", methodName);
        //     return Result.ERROR("Minimax GroupId配置错误，请联系管理员");
        // }

        try {
            // 2. 调用MiniMax API（已处理HTTP错误和业务错误）
            MiniMaxVoiceGenerationResponseBO responseBO = MiniMaxApi.voiceGeneration(requestBO, apiKey, groupId);
            if (responseBO == null) {
                return Result.ERROR("语音生成失败，请稍后重试");
            }

            // 3. 验证响应数据完整性
            if (!responseBO.isSuccess()) {
                String errorMessage = responseBO.getErrorMessage();
                log.error("[{}] 语音生成业务失败：{}", methodName, errorMessage);
                return Result.ERROR("语音生成失败：" + errorMessage);
            }

            if (!responseBO.hasAudioData()) {
                log.error("[{}] 语音生成音频数据为空", methodName);
                return Result.ERROR("语音生成音频数据为空");
            }

            // 4. 处理音频数据：hex -> bytes -> OSS上传
            byte[] audioBytes = org.apache.commons.codec.binary.Hex.decodeHex(responseBO.getData().getAudio());

            // 生成文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "minimax-" + userId + "-" + UUID.randomUUID().toString();
            }

            // 设置默认文件类型
            if (type == null) {
                type = 4; // 音频临时文件目录
            }

            // 上传到OSS
            String audioUrl = DigitalFileUtil.uploadDigitalResource(
                    audioBytes,
                    fileName,
                    userId,
                    null,
                    type,
                    false);

            if (audioUrl == null) {
                return Result.ERROR("音频文件上传失败，请稍后重试");
            }

            // 5. 设置audioUrl
            responseBO.setAudioUrl(audioUrl);

            // 6. 处理字幕文件（如果存在）
            if (responseBO.getData() != null && responseBO.getData().getSubtitleFile() != null) {
                try {
                    String subtitleUrl = responseBO.getData().getSubtitleFile();
                    List<AudioGenerationResponseVO.SubtitleInfo> subtitles = processSubtitles(subtitleUrl);

                    if (!subtitles.isEmpty()) {
                        log.info("[{}] 字幕处理成功: userId={}, subtitleCount={}", methodName, userId, subtitles.size());
                        // 字幕信息已通过processSubtitles处理，将在convertToResponseDTO中使用
                    } else {
                        log.warn("[{}] 字幕处理返回空列表: userId={}, subtitleUrl={}", methodName, userId, subtitleUrl);
                    }
                } catch (Exception e) {
                    log.warn("[{}] 字幕处理失败但不影响音频生成: userId={}, error={}", methodName, userId, e.getMessage());
                }
            }

            log.info("[{}] 语音生成成功，用户ID：{}，音频URL：{}，时长：{}ms",
                    methodName, userId, audioUrl, responseBO.getAudioDuration());

            return Result.SUCCESS("语音生成成功", responseBO);

        } catch (Exception e) {
            log.error("[{}] 语音生成异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("语音生成异常：" + e.getMessage());
        }
    }

    /**
     * 获取字幕内容
     *
     * @param subtitleUrl 字幕文件URL
     * @return 字幕内容，获取失败返回null
     */
    public static String fetchSubtitleContent(String subtitleUrl) {
        try {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder().url(subtitleUrl).build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return response.body().string();
                }
            }
        } catch (Exception e) {
            log.error("字幕获取失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 解析字幕JSON内容
     *
     * @param jsonContent JSON格式的字幕内容
     * @return 字幕信息列表，解析失败返回空列表
     */
    public static List<AudioGenerationResponseVO.SubtitleInfo> parseSubtitleJson(String jsonContent) {
        try {
            if (jsonContent != null && !jsonContent.trim().isEmpty()) {
                JSONArray array = JSONArray.parseArray(jsonContent);
                List<AudioGenerationResponseVO.SubtitleInfo> subtitles = new ArrayList<>();

                for (int i = 0; i < array.size(); i++) {
                    JSONObject item = array.getJSONObject(i);

                    // 验证必要字段是否存在（使用官方格式：time_begin, time_end）
                    if (!item.containsKey("text") || !item.containsKey("time_begin") || !item.containsKey("time_end")) {
                        log.warn("字幕项缺少必要字段，跳过解析: {}", item.toJSONString());
                        return Collections.emptyList();
                    }

                    String text = item.getString("text");
                    // 官方格式使用 time_begin 和 time_end，单位为毫秒（浮点数）
                    Double timeBegin = item.getDouble("time_begin");
                    Double timeEnd = item.getDouble("time_end");

                    // 获取可选字段
                    Integer textBegin = item.getInteger("text_begin");
                    Integer textEnd = item.getInteger("text_end");
                    Object timestampedWords = item.get("timestamped_words");

                    // 验证必要字段值是否有效
                    if (text == null || timeBegin == null || timeEnd == null) {
                        log.warn("字幕项字段值无效，跳过解析: {}", item.toJSONString());
                        return Collections.emptyList();
                    }

                    AudioGenerationResponseVO.SubtitleInfo subtitle =
                        AudioGenerationResponseVO.SubtitleInfo.builder()
                            .text(text)
                            .timeBegin(timeBegin)
                            .timeEnd(timeEnd)
                            .textBegin(textBegin)
                            .textEnd(textEnd)
                            .timestampedWords(timestampedWords)
                            .build();
                    subtitles.add(subtitle);
                }
                return subtitles;
            }
        } catch (Exception e) {
            log.error("字幕解析失败: {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 处理字幕：获取并解析字幕内容
     *
     * @param subtitleUrl 字幕文件URL
     * @return 字幕信息列表，处理失败返回空列表
     */
    public static List<AudioGenerationResponseVO.SubtitleInfo> processSubtitles(String subtitleUrl) {
        if (subtitleUrl == null || subtitleUrl.trim().isEmpty()) {
            return Collections.emptyList();
        }

        String content = fetchSubtitleContent(subtitleUrl);
        return parseSubtitleJson(content);
    }

}
