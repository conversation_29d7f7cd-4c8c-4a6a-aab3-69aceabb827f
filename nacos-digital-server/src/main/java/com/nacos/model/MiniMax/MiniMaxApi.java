package com.nacos.model.MiniMax;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxFileUploadResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceCloneRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceCloneResponseBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationRequestBO;
import com.nacos.model.MiniMax.model.MiniMaxVoiceGenerationResponseBO;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.concurrent.TimeUnit;

@Slf4j
@Schema(title = "MINIMAX Api")
public class MiniMaxApi {

    // 声音复刻 URl
    private static final String MINIMAX_API_VOICE_CLONE_URL = "https://api.minimaxi.com/v1/voice_clone";
    // 上传文件 URl
    private static final String MINIMAX_API_FILES_UPLOAD_URL = "https://api.minimaxi.com/v1/files/upload";
    // 获取可用Voice ID URL
    private static final String MINIMAX_API_GET_VOICE_URL = "https://api.minimaxi.com/v1/get_voice";
    // 语音生成 URL
    private static final String MINIMAX_API_T2A_V2_URL = "https://api.minimaxi.com/v1/t2a_v2";

    // 创建 OkHttpClient 实例
    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    // MINIMAX 上传文件 API
    public static MiniMaxFileUploadResponseBO uploadFile(MiniMaxFileUploadRequestBO miniMaxFileUploadRequestBO,
            String apiKey) {
        String methodName = "uploadFile";
        try {
            // 验证API密钥
            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("[{}] API密钥为空，无法进行鉴权", methodName);
                return null;
            }

            // 构建multipart请求体
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("purpose", miniMaxFileUploadRequestBO.getPurpose());

            // 添加文件
            RequestBody fileBody = RequestBody.create(
                    miniMaxFileUploadRequestBO.getFile().getBytes(),
                    MediaType.parse("application/octet-stream"));
            builder.addFormDataPart("file", miniMaxFileUploadRequestBO.getFile().getOriginalFilename(), fileBody);

            Request request = new Request.Builder()
                    .url(MINIMAX_API_FILES_UPLOAD_URL)
                    .post(builder.build())
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .build();

            try (var response = client.newCall(request).execute()) {
                log.info("[{}]上传文件请求状态码Code：" + response.code(), methodName);
                assert response.body() != null;
                String responseBody = response.body().string();
                System.out.println("==MINIMAX API 文件上传响应====" + responseBody);
                if (response.isSuccessful()) {
                    return JSON.parseObject(responseBody, new TypeReference<MiniMaxFileUploadResponseBO>() {
                    });
                }
                // 使用 ObjectMapper 解析 JSON
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(responseBody);
                int statusCode = rootNode.path("base_resp").path("status_code").asInt();
                String statusMsg = rootNode.path("base_resp").path("status_msg").asText();
                log.error("[{}]上传文件请求失败：statusCode={}, statusMsg={}", methodName, statusCode, statusMsg);
                return null;
            }
        } catch (Exception e) {
            log.error("[{}]上传文件请求失败：{}", methodName, e.getMessage());
            return null;
        }
    }

    // MINIMAX 快速复刻音色 API
    public static MiniMaxVoiceCloneResponseBO voiceClone(MiniMaxVoiceCloneRequestBO miniMaxVoiceCloneRequestBO,
            String apiKey) {
        String methodName = "voiceClone";
        try {
            // 验证API密钥
            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("[{}] API密钥为空，无法进行鉴权", methodName);
                return null;
            }

            // 构建请求体
            JSONObject jsonObject = JSONObject.parseObject(
                    JSONObject.toJSONString(miniMaxVoiceCloneRequestBO, JSONWriter.Feature.WriteMapNullValue));
            RequestBody body = RequestBody.create(
                    jsonObject.toString(),
                    MediaType.parse("application/json; charset=utf-8"));

            Request request = new Request.Builder()
                    .url(MINIMAX_API_VOICE_CLONE_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json")
                    .build();
            System.out.println("MINIMAX API 声音复刻请求体参数body：" + jsonObject.toString());
            try (var response = client.newCall(request).execute()) {
                log.info("[{}]MINIMAX API 声音复刻响应状态码Code：" + response.code(), methodName);
                assert response.body() != null;
                String responseBody = response.body().string();
                log.info("[{}]MINIMAX API 声音复刻响应：{}", methodName, responseBody);
                if (response.isSuccessful()) {
                    return JSON.parseObject(responseBody, new TypeReference<>() {
                    });
                }
                // 使用 ObjectMapper 解析 JSON
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(responseBody);
                int statusCode = rootNode.path("base_resp").path("status_code").asInt();
                String statusMsg = rootNode.path("base_resp").path("status_msg").asText();
                log.error("[{}]MINIMAX API 声音复刻请求失败：statusCode={}, statusMsg={}", methodName, statusCode, statusMsg);
                return JSON.parseObject(responseBody, new TypeReference<>() {
                });
            }
        } catch (Exception e) {
            log.error("[{}]MINIMAX API 声音复刻请求失败：{}", methodName, e.getMessage());
            return null;
        }
    }

    // MINIMAX 获取可用Voice ID API
    public static MiniMaxGetVoiceIdResponseBO getVoiceId(MiniMaxGetVoiceIdRequestBO miniMaxGetVoiceIdRequestBO,
            String apiKey) {
        String methodName = "getVoiceId";
        try {
            // 验证API密钥
            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("[{}] API密钥为空，无法进行鉴权", methodName);
                return null;
            }

            // 构建请求体
            JSONObject jsonObject = JSONObject.parseObject(
                    JSONObject.toJSONString(miniMaxGetVoiceIdRequestBO, JSONWriter.Feature.WriteMapNullValue));
            RequestBody body = RequestBody.create(
                    jsonObject.toString(),
                    MediaType.parse("application/json; charset=utf-8"));

            Request request = new Request.Builder()
                    .url(MINIMAX_API_GET_VOICE_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json")
                    .build();

            log.info("[{}] MINIMAX API 获取可用Voice ID请求参数：{}", methodName, jsonObject.toString());

            try (var response = client.newCall(request).execute()) {
                log.info("[{}] MINIMAX API 获取可用Voice ID请求状态码：{}", methodName, response.code());
                assert response.body() != null;
                String responseBody = response.body().string();

                if (response.isSuccessful()) {
                    return JSON.parseObject(responseBody, new TypeReference<MiniMaxGetVoiceIdResponseBO>() {
                    });
                }

                // 请求失败时的错误处理
                try {
                    // 使用 ObjectMapper 解析 JSON
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode rootNode = objectMapper.readTree(responseBody);
                    int statusCode = rootNode.path("base_resp").path("status_code").asInt();
                    String statusMsg = rootNode.path("base_resp").path("status_msg").asText();
                    log.error("[{}] MINIMAX API 获取可用Voice ID请求失败：statusCode={}, statusMsg={}", methodName, statusCode, statusMsg);
                } catch (Exception parseException) {
                    log.error("[{}] 解析错误响应失败：{}", methodName, parseException.getMessage());
                }
                return null;
            }
        } catch (Exception e) {
            log.error("[{}] MINIMAX API 获取可用Voice ID请求失败：{}", methodName, e.getMessage());
            return null;
        }
    }

    // MINIMAX 语音生成 API
    public static MiniMaxVoiceGenerationResponseBO voiceGeneration(MiniMaxVoiceGenerationRequestBO requestBO,
            String apiKey, String groupId) {
        String methodName = "voiceGeneration";
        try {
            // 验证API密钥
            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("[{}] API密钥为空，无法进行鉴权", methodName);
                return null;
            }

            // 如果未设置stream参数，默认为非流式输出
            if (requestBO.getStream() == null) {
                requestBO.setStream(false);
            }

            // 构建请求体
            JSONObject jsonObject = JSONObject
                    .parseObject(JSONObject.toJSONString(requestBO, JSONWriter.Feature.WriteMapNullValue));
            log.info("[{}] 请求参数：{}", methodName, jsonObject);

            RequestBody body = RequestBody.create(
                    jsonObject.toString(),
                    MediaType.parse("application/json; charset=utf-8"));

            Request request = new Request.Builder()
                    .url(MINIMAX_API_T2A_V2_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json")
                    .build();

            try (var response = client.newCall(request).execute()) {
                int statusCode = response.code();
                log.info("[{}] 请求状态码：{}", methodName, statusCode);

                assert response.body() != null;
                String responseBody = response.body().string();

                if (!response.isSuccessful()) {
                    // 处理HTTP错误状态码
                    String httpErrorMessage = getHttpErrorMessage(statusCode, responseBody);
                    log.error("[{}] HTTP请求失败，状态码：{}，错误信息：{}", methodName, statusCode, httpErrorMessage);
                    return null;
                }

                // 解析响应
                MiniMaxVoiceGenerationResponseBO responseBO = JSON.parseObject(responseBody,
                        MiniMaxVoiceGenerationResponseBO.class);
                if (responseBO == null) {
                    log.error("[{}] 响应解析失败，响应内容：{}", methodName, responseBody);
                    return null;
                }

                // 检查业务响应状态
                if (responseBO.getBaseResp() != null) {
                    Long respStatusCode = responseBO.getBaseResp().getStatusCode();
                    String statusMsg = responseBO.getBaseResp().getStatusMsg();
                    log.info("[{}] 业务状态码：{}, 状态消息：{}", methodName, respStatusCode, statusMsg);

                    // 检查业务错误
                    if (respStatusCode != null && respStatusCode != 0L) {
                        String errorMessage = MiniMaxApiUtil.getErrorMessageByCode(String.valueOf(respStatusCode));
                        log.error("[{}] 业务请求失败，错误码：{}，错误信息：{}，原始消息：{}",
                                methodName, respStatusCode, errorMessage, statusMsg);
                        return null;
                    }
                }

                return responseBO;
            }
        } catch (Exception e) {
            log.error("[{}] 请求异常：{}", methodName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据HTTP状态码获取错误信息
     *
     * @param statusCode HTTP状态码
     * @param responseBody 响应体内容
     * @return 错误描述
     */
    private static String getHttpErrorMessage(int statusCode, String responseBody) {
        String baseMessage = switch (statusCode) {
            case 400 -> "请求参数错误，请检查请求格式和参数";
            case 401 -> "认证失败，请检查API密钥是否正确";
            case 403 -> "访问被拒绝，请检查API权限";
            case 404 -> "请求的资源不存在，请检查API端点";
            case 429 -> "请求频率超限，请稍后重试";
            case 500 -> "MiniMax服务器内部错误，请稍后重试";
            case 502 -> "网关错误，请稍后重试";
            case 503 -> "服务暂时不可用，请稍后重试";
            case 504 -> "网关超时，请稍后重试";
            default -> "HTTP请求失败，状态码: " + statusCode;
        };

        // 如果响应体包含错误信息，尝试解析
        if (responseBody != null && !responseBody.isEmpty()) {
            try {
                JSONObject errorJson = JSON.parseObject(responseBody);
                if (errorJson.containsKey("error")) {
                    String errorDetail = errorJson.getString("error");
                    return baseMessage + "，详细信息: " + errorDetail;
                }
                if (errorJson.containsKey("message")) {
                    String errorDetail = errorJson.getString("message");
                    return baseMessage + "，详细信息: " + errorDetail;
                }
            } catch (Exception e) {
                // 解析失败，返回基础错误信息
                log.debug("解析HTTP错误响应失败: {}", e.getMessage());
            }
        }

        return baseMessage;
    }
}
