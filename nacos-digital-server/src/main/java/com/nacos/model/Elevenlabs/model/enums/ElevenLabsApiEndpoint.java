package com.nacos.model.Elevenlabs.model.enums;

import lombok.Getter;

/**
 * ElevenLabs API 端点枚举
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
public enum ElevenLabsApiEndpoint {
    
    /**
     * 获取语音列表
     */
    VOICES("/v2/voices", "获取语音列表"),
    
    /**
     * 获取单个语音详情
     */
    VOICE_DETAIL("/v2/voices/{voice_id}", "获取单个语音详情"),
    
    /**
     * 语音合成
     */
    TEXT_TO_SPEECH("/v1/text-to-speech/{voice_id}", "语音合成"),
    
    /**
     * 语音克隆
     */
    VOICE_CLONE("/v2/voices/add", "语音克隆"),
    
    /**
     * 删除语音
     */
    DELETE_VOICE("/v2/voices/{voice_id}", "删除语音"),
    
    /**
     * 获取用户信息
     */
    USER_INFO("/v2/user", "获取用户信息"),
    
    /**
     * 获取用户订阅信息
     */
    USER_SUBSCRIPTION("/v2/user/subscription", "获取用户订阅信息");
    
    /**
     * API端点路径
     */
    private final String path;
    
    /**
     * 端点描述
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param path API端点路径
     * @param description 端点描述
     */
    ElevenLabsApiEndpoint(String path, String description) {
        this.path = path;
        this.description = description;
    }
    
    /**
     * 获取完整的URL
     * 
     * @param baseUrl 基础URL
     * @return 完整的URL
     */
    public String getFullUrl(String baseUrl) {
        return baseUrl + this.path;
    }
    
    /**
     * 替换路径中的参数
     * 
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 替换后的路径
     */
    public String replacePath(String paramName, String paramValue) {
        return this.path.replace("{" + paramName + "}", paramValue);
    }
    
    /**
     * 获取带参数的完整URL
     * 
     * @param baseUrl 基础URL
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 完整的URL
     */
    public String getFullUrlWithParam(String baseUrl, String paramName, String paramValue) {
        return baseUrl + replacePath(paramName, paramValue);
    }
}
