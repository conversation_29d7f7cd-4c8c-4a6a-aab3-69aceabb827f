package com.nacos.model.Elevenlabs.model.enums;

/**
 * ElevenLabs API 常量类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
public final class ElevenLabsConstants {
    
    /**
     * 私有构造函数，防止实例化
     */
    private ElevenLabsConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ==================== API 配置 ====================
    
    /**
     * ElevenLabs API 基础URL
     */
    public static final String BASE_URL = "https://api.elevenlabs.io";
    
    /**
     * API Key 请求头名称
     */
    public static final String API_KEY_HEADER = "xi-api-key";
    
    /**
     * 内容类型
     */
    public static final String CONTENT_TYPE = "application/json";
    
    /**
     * 接受类型
     */
    public static final String ACCEPT_TYPE = "application/json";
    
    // ==================== 超时配置 ====================
    
    /**
     * 连接超时时间（秒）
     */
    public static final int CONNECT_TIMEOUT_SECONDS = 30;
    
    /**
     * 读取超时时间（秒）
     */
    public static final int READ_TIMEOUT_SECONDS = 30;
    
    /**
     * 写入超时时间（秒）
     */
    public static final int WRITE_TIMEOUT_SECONDS = 30;
    
    // ==================== 分页配置 ====================
    
    /**
     * 默认页面大小
     */
    public static final int DEFAULT_PAGE_SIZE = 50;
    
    /**
     * 最大页面大小
     */
    public static final int MAX_PAGE_SIZE = 100;
    
    /**
     * 最小页面大小
     */
    public static final int MIN_PAGE_SIZE = 1;
    
    // ==================== 提供商标识 ====================
    
    /**
     * 提供商名称
     */
    public static final String PROVIDER_NAME = "ELEVENLABS";
    
    /**
     * 提供商类型标识
     */
    public static final String PROVIDER_TYPE = "elevenlabs";
    
    // ==================== 状态标识 ====================
    
    /**
     * 成功状态
     */
    public static final String STATUS_SUCCESS = "success";
    
    /**
     * 失败状态
     */
    public static final String STATUS_FAIL = "fail";
    
    // ==================== 默认值 ====================
    
    /**
     * 默认语言
     */
    public static final String DEFAULT_LANGUAGE = "en-US";
    
    /**
     * 默认语音类型
     */
    public static final int DEFAULT_VOICE_TYPE = 1;
    
    // ==================== 字段长度限制 ====================
    
    /**
     * 语音名称最大长度
     */
    public static final int MAX_VOICE_NAME_LENGTH = 200;
    
    /**
     * 描述最大长度
     */
    public static final int MAX_DESCRIPTION_LENGTH = 500;
    
    /**
     * 语音ID最大长度
     */
    public static final int MAX_VOICE_ID_LENGTH = 100;
}
