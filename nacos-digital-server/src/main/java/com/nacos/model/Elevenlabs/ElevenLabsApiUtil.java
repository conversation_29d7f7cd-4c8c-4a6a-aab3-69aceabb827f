package com.nacos.model.Elevenlabs;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.utils.BThirdPartyKey;
import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.enums.DictConfigEnum;
import com.nacos.model.Elevenlabs.model.ElevenLabsTextToSpeechRequestBO;
import com.nacos.model.Elevenlabs.model.ElevenLabsTextToSpeechResponseBO;
import com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListRequestBO;
import com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO;
import com.nacos.model.Elevenlabs.model.enums.ElevenLabsConstants;
import com.nacos.result.Result;
import com.nacos.utils.DigitalFileUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ElevenLabs API 业务逻辑工具类
 * 提供高级业务方法，包括分页数据获取、数据转换映射、错误处理等
 */
@Slf4j
@Schema(title = "ElevenLabs API 业务逻辑工具类")
public class ElevenLabsApiUtil {

    public static final String STATUS_FAIL = ElevenLabsConstants.STATUS_FAIL;
    public static final String STATUS_SUCCESS = ElevenLabsConstants.STATUS_SUCCESS;
    public static final String PROVIDER_NAME = ElevenLabsConstants.PROVIDER_NAME;

    /**
     * 获取ElevenLabs API Key
     * @return API Key，获取失败返回null
     */
    private static String getElevenLabsApiKey() {
        try {
            HashMap<Long, String> dictConfigMap = BThirdPartyKey
                    .getSecretKeyInfo(DictConfigEnum.ELEVENLABS_API_KEY.getDictType());
            if (dictConfigMap == null || dictConfigMap.isEmpty()) {
                log.error("ElevenLabs API配置错误：无法获取到有效的secretKey");
                return null;
            }
            String apiKey = dictConfigMap.get(DictConfigEnum.ELEVENLABS_API_KEY.getDictKey());
            if (!StringUtils.hasText(apiKey)) {
                log.error("ElevenLabs API配置错误：secretKey为空");
                return null;
            }
            return apiKey;
        } catch (Exception e) {
            log.error("获取ElevenLabs API Key失败：{}", e.getMessage(), e);
            return null;
        }
    }

    // ElevenLabs 分类映射到系统语音类型
    private static final Map<String, Integer> CATEGORY_MAPPING = Map.of(
        "premade", 1,      // 预制语音 -> 系统音色
        "cloned", 2,       // 克隆语音 -> 克隆音色
        "generated", 3,    // 生成语音 -> 生成音色
        "professional", 4  // 专业语音 -> 专业音色
    );

    /**
     * 获取所有语音数据（支持分页）
     * 自动从Redis获取API Key
     *
     * @return 所有语音数据列表
     */
    public static List<ElevenLabsVoiceListResponseBO.Voice> getAllVoices() {
        String methodName = "getAllVoices";
        log.info("[{}]开始获取所有 ElevenLabs 语音数据", methodName);

        String apiKey = getElevenLabsApiKey();
        if (apiKey == null) {
            return null;
        }
        
        List<ElevenLabsVoiceListResponseBO.Voice> allVoices = new ArrayList<>();
        String nextPageToken = null;
        int pageCount = 0;
        
        try {
            do {
                pageCount++;
                log.debug("[{}]获取第{}页数据，token: {}", methodName, pageCount, nextPageToken);
                
                // 构建请求参数
                ElevenLabsVoiceListRequestBO requestBO = new ElevenLabsVoiceListRequestBO();
                requestBO.setNextPageToken(nextPageToken);
                requestBO.setPageSize(ElevenLabsConstants.MAX_PAGE_SIZE); // 使用最大页面大小提高效率
                requestBO.setIncludeTotalCount(true);
                
                // 调用 API
                ElevenLabsVoiceListResponseBO responseBO = ElevenLabsApi.getVoicesList(requestBO, apiKey);
                
                if (responseBO == null) {
                    log.error("[{}]第{}页响应为空", methodName, pageCount);
                    break;
                }
                
                if (responseBO.getVoices() != null && !responseBO.getVoices().isEmpty()) {
                    allVoices.addAll(responseBO.getVoices());
                    log.debug("[{}]第{}页获取到{}个语音", methodName, pageCount, responseBO.getVoices().size());
                }
                
                // 检查是否有更多数据
                if (Boolean.TRUE.equals(responseBO.getHasMore()) && 
                    StringUtils.hasText(responseBO.getNextPageToken())) {
                    nextPageToken = responseBO.getNextPageToken();
                } else {
                    nextPageToken = null;
                }
                
                // 防止无限循环
                if (pageCount > 100) {
                    log.warn("[{}]分页数量超过100页，停止获取", methodName);
                    break;
                }
                
            } while (nextPageToken != null);
            
            log.info("[{}]完成获取，共{}页，总计{}个语音", methodName, pageCount, allVoices.size());
            return allVoices;
            
        } catch (Exception e) {
            log.error("[{}]获取语音数据失败: {}", methodName, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 将 ElevenLabs 语音数据转换为系统统一格式
     * 
     * @param voice ElevenLabs 语音数据
     * @return 系统语音数据格式
     */
    public static DigitalVoiceStylesPO convertToDigitalVoiceStyle(ElevenLabsVoiceListResponseBO.Voice voice) {
        String methodName = "convertToDigitalVoiceStyle";
        
        if (!isValidVoice(voice)) {
            log.warn("[{}]无效的语音数据: {}", methodName, voice != null ? voice.getVoiceId() : "null");
            return null;
        }
        
        try {
            DigitalVoiceStylesPO voiceStyle = new DigitalVoiceStylesPO();
            
            // 基础字段映射
            voiceStyle.setVoiceId(voice.getVoiceId().trim());
            voiceStyle.setProvider(PROVIDER_NAME);
            voiceStyle.setVoiceName(cleanVoiceName(voice.getName()));
            voiceStyle.setDescription(cleanDescription(voice.getDescription()));
            voiceStyle.setDemoAudio(voice.getPreviewUrl());
            
            // 分类映射
            voiceStyle.setVoiceType(mapCategoryToVoiceType(voice.getCategory()));
            
            // 语言映射
            voiceStyle.setLanguage(extractLanguage(voice));
            
            // 性别映射（优先使用labels中的性别信息）
            voiceStyle.setGender(extractGender(voice));
            
            // 扩展属性构建
            voiceStyle.setAttributes(buildExtendedAttributes(voice));
            
            // 时间字段
            voiceStyle.setLastSyncTime(LocalDateTime.now());
            
            log.debug("[{}]成功转换语音: {} -> {}", methodName, voice.getVoiceId(), voiceStyle.getVoiceName());
            return voiceStyle;
            
        } catch (Exception e) {
            log.error("[{}]转换语音数据失败: voiceId={}, error={}", methodName, 
                voice != null ? voice.getVoiceId() : "null", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证语音数据是否有效
     * 
     * @param voice 语音数据
     * @return 是否有效
     */
    public static boolean isValidVoice(ElevenLabsVoiceListResponseBO.Voice voice) {
        return voice != null 
            && StringUtils.hasText(voice.getVoiceId())
            && StringUtils.hasText(voice.getName());
    }

    /**
     * 清理语音名称
     * 
     * @param name 原始名称
     * @return 清理后的名称
     */
    private static String cleanVoiceName(String name) {
        if (name == null) return null;
        String cleaned = name.trim().replaceAll("\\s+", " ");
        return cleaned.length() > ElevenLabsConstants.MAX_VOICE_NAME_LENGTH ?
            cleaned.substring(0, ElevenLabsConstants.MAX_VOICE_NAME_LENGTH) : cleaned;
    }

    /**
     * 清理描述信息
     * 
     * @param description 原始描述
     * @return 清理后的描述
     */
    private static String cleanDescription(String description) {
        if (description == null) return null;
        String cleaned = description.trim();
        return cleaned.length() > ElevenLabsConstants.MAX_DESCRIPTION_LENGTH ?
            cleaned.substring(0, ElevenLabsConstants.MAX_DESCRIPTION_LENGTH) + "..." : cleaned;
    }

    /**
     * 分类映射到语音类型
     *
     * @param category ElevenLabs 分类
     * @return 系统语音类型
     */
    private static Integer mapCategoryToVoiceType(String category) {
        if (category == null) return ElevenLabsConstants.DEFAULT_VOICE_TYPE;

        // 使用映射表进行转换
        Integer voiceType = CATEGORY_MAPPING.get(category.toLowerCase());
        return voiceType != null ? voiceType : ElevenLabsConstants.DEFAULT_VOICE_TYPE; // 默认为系统音色
    }

    /**
     * 从语音数据中提取语言信息
     * 按优先级顺序：sharing.labels.locale > sharing.labels.language > labels.language > verified_languages
     *
     * @param voice ElevenLabs语音数据
     * @return 语言代码，如果没有有效语言则返回null
     */
    private static String extractLanguage(ElevenLabsVoiceListResponseBO.Voice voice) {
        if (voice == null) {
            return null;
        }

        // 1. 优先使用sharing.labels.locale（最完整的locale信息）
        if (voice.getSharing() != null &&
            voice.getSharing().getLabels() != null &&
            voice.getSharing().getLabels().getLocale() != null) {
            return voice.getSharing().getLabels().getLocale();
        }

        // 2. 其次使用sharing.labels.language（共享标签中的语言）
        if (voice.getSharing() != null &&
            voice.getSharing().getLabels() != null &&
            voice.getSharing().getLabels().getLanguage() != null) {
            return voice.getSharing().getLabels().getLanguage();
        }

        // 3. 再次使用labels.language（主要语言标识）
        if (voice.getLabels() != null && voice.getLabels().getLanguage() != null) {
            return voice.getLabels().getLanguage();
        }

        // 4. 最后从verified_languages中提取
        List<ElevenLabsVoiceListResponseBO.VerifiedLanguage> verifiedLanguages = voice.getVerifiedLanguages();
        if (verifiedLanguages != null && !verifiedLanguages.isEmpty()) {
            // 优先选择中文
            for (ElevenLabsVoiceListResponseBO.VerifiedLanguage lang : verifiedLanguages) {
                if ((lang.getLocale() != null && lang.getLocale().startsWith("zh")) ||
                    (lang.getLanguage() != null && lang.getLanguage().startsWith("zh"))) {
                    return lang.getLocale() != null ? lang.getLocale() : lang.getLanguage();
                }
            }

            // 其次选择英语
            for (ElevenLabsVoiceListResponseBO.VerifiedLanguage lang : verifiedLanguages) {
                if ((lang.getLocale() != null && lang.getLocale().startsWith("en")) ||
                    (lang.getLanguage() != null && lang.getLanguage().startsWith("en"))) {
                    return lang.getLocale() != null ? lang.getLocale() : lang.getLanguage();
                }
            }

            // 返回第一个可用语言
            ElevenLabsVoiceListResponseBO.VerifiedLanguage firstLang = verifiedLanguages.get(0);
            if (firstLang.getLocale() != null) {
                return firstLang.getLocale();
            } else if (firstLang.getLanguage() != null) {
                return firstLang.getLanguage();
            }
        }

        // 如果都没有有效值，返回null
        return null;
    }

    /**
     * 从语音数据中提取性别信息
     * 按优先级顺序：sharing.labels.gender > labels.gender > 推断
     *
     * @param voice ElevenLabs语音数据
     * @return 性别信息，如果没有有效性别则返回null
     */
    private static String extractGender(ElevenLabsVoiceListResponseBO.Voice voice) {
        if (voice == null) {
            return null;
        }

        // 1. 优先使用sharing.labels.gender
        if (voice.getSharing() != null &&
            voice.getSharing().getLabels() != null &&
            voice.getSharing().getLabels().getGender() != null) {
            return capitalizeGender(voice.getSharing().getLabels().getGender());
        }

        // 2. 其次使用labels.gender
        if (voice.getLabels() != null && voice.getLabels().getGender() != null) {
            return capitalizeGender(voice.getLabels().getGender());
        }

        // 3. 最后通过名称和描述推断
        return inferGender(voice.getName(), voice.getDescription());
    }

    /**
     * 标准化性别格式（首字母大写）
     *
     * @param gender 原始性别字符串
     * @return 标准化后的性别
     */
    private static String capitalizeGender(String gender) {
        if (gender == null || gender.trim().isEmpty()) {
            return null;
        }
        String trimmed = gender.trim().toLowerCase();
        return trimmed.substring(0, 1).toUpperCase() + trimmed.substring(1);
    }

    /**
     * 推断语音性别
     *
     * @param voiceName 语音名称
     * @param description 语音描述
     * @return 推断的性别
     */
    private static String inferGender(String voiceName, String description) {
        if (voiceName == null && description == null) return null;

        String text = (voiceName + " " + (description != null ? description : "")).toLowerCase();

        // 简单的关键词匹配
        if (text.contains("male") && !text.contains("female")) {
            return "Male";
        } else if (text.contains("female")) {
            return "Female";
        } else if (text.contains("woman") || text.contains("girl")) {
            return "Female";
        } else if (text.contains("man") || text.contains("boy")) {
            return "Male";
        }

        return null; // 无法确定
    }

    /**
     * 构建扩展属性 JSON
     * 将所有无法直接映射到DigitalVoiceStylesPO基础字段的数据都放入attributes
     *
     * @param voice ElevenLabs 语音数据
     * @return 扩展属性 JSON 字符串
     */
    private static String buildExtendedAttributes(ElevenLabsVoiceListResponseBO.Voice voice) {
        JSONObject attributes = new JSONObject();

        try {
            // 1. 音频样本信息（samples）
            if (voice.getSamples() != null && !voice.getSamples().isEmpty()) {
                JSONArray samples = new JSONArray();
                for (ElevenLabsVoiceListResponseBO.Sample sample : voice.getSamples()) {
                    JSONObject sampleObj = new JSONObject();
                    sampleObj.put("sample_id", sample.getSampleId());
                    sampleObj.put("file_name", sample.getFileName());
                    sampleObj.put("mime_type", sample.getMimeType());
                    sampleObj.put("size_bytes", sample.getSizeBytes());
                    sampleObj.put("hash", sample.getHash());
                    sampleObj.put("duration_secs", sample.getDurationSecs());
                    sampleObj.put("remove_background_noise", sample.getRemoveBackgroundNoise());
                    sampleObj.put("has_isolated_audio", sample.getHasIsolatedAudio());
                    sampleObj.put("has_isolated_audio_preview", sample.getHasIsolatedAudioPreview());
                    sampleObj.put("speaker_separation", sample.getSpeakerSeparation());
                    sampleObj.put("trim_start", sample.getTrimStart());
                    sampleObj.put("trim_end", sample.getTrimEnd());
                    samples.add(sampleObj);
                }
                attributes.put("samples", samples);
            }

            // 2. 语音设置（settings）
            if (voice.getSettings() != null) {
                JSONObject settings = new JSONObject();
                settings.put("stability", voice.getSettings().getStability());
                settings.put("similarity_boost", voice.getSettings().getSimilarityBoost());
                settings.put("style", voice.getSettings().getStyle());
                settings.put("speed", voice.getSettings().getSpeed());
                settings.put("use_speaker_boost", voice.getSettings().getUseSpeakerBoost());
                attributes.put("settings", settings);
            }

            // 3. 可用层级（available_for_tiers）
            if (voice.getAvailableForTiers() != null) {
                attributes.put("available_for_tiers", voice.getAvailableForTiers());
            }

            // 4. 安全控制（safety_control）
            if (voice.getSafetyControl() != null) {
                attributes.put("safety_control", voice.getSafetyControl());
            }

            // 5. 高质量模型ID（high_quality_base_model_ids）
            if (voice.getHighQualityBaseModelIds() != null) {
                attributes.put("high_quality_base_model_ids", voice.getHighQualityBaseModelIds());
            }

            // 6. 语音状态标识
            attributes.put("is_owner", voice.getIsOwner());
            attributes.put("is_legacy", voice.getIsLegacy());
            attributes.put("is_mixed", voice.getIsMixed());

            // 7. 权限信息（permission_on_resource）
            if (voice.getPermissionOnResource() != null) {
                attributes.put("permission_on_resource", voice.getPermissionOnResource());
            }

            // 8. 创建时间（created_at_unix）
            if (voice.getCreatedAtUnix() != null) {
                attributes.put("created_at_unix", voice.getCreatedAtUnix());
            }

            // 9. 微调信息（fine_tuning）
            if (voice.getFineTuning() != null) {
                JSONObject fineTuning = new JSONObject();
                fineTuning.put("is_allowed_to_fine_tune", voice.getFineTuning().getIsAllowedToFineTune());
                fineTuning.put("state", voice.getFineTuning().getState());
                fineTuning.put("verification_failures", voice.getFineTuning().getVerificationFailures());
                fineTuning.put("verification_attempts_count", voice.getFineTuning().getVerificationAttemptsCount());
                fineTuning.put("manual_verification_requested", voice.getFineTuning().getManualVerificationRequested());
                fineTuning.put("language", voice.getFineTuning().getLanguage());
                fineTuning.put("progress", voice.getFineTuning().getProgress());
                fineTuning.put("message", voice.getFineTuning().getMessage());
                fineTuning.put("dataset_duration_seconds", voice.getFineTuning().getDatasetDurationSeconds());
                fineTuning.put("verification_attempts", voice.getFineTuning().getVerificationAttempts());
                fineTuning.put("slice_ids", voice.getFineTuning().getSliceIds());
                fineTuning.put("manual_verification", voice.getFineTuning().getManualVerification());
                fineTuning.put("max_verification_attempts", voice.getFineTuning().getMaxVerificationAttempts());
                fineTuning.put("next_max_verification_attempts_reset_unix_ms", voice.getFineTuning().getNextMaxVerificationAttemptsResetUnixMs());
                attributes.put("fine_tuning", fineTuning);
            }

            // 10. 语音验证信息（voice_verification）
            if (voice.getVoiceVerification() != null) {
                JSONObject voiceVerification = new JSONObject();
                voiceVerification.put("requires_verification", voice.getVoiceVerification().getRequiresVerification());
                voiceVerification.put("is_verified", voice.getVoiceVerification().getIsVerified());
                voiceVerification.put("verification_failures", voice.getVoiceVerification().getVerificationFailures());
                voiceVerification.put("verification_attempts_count", voice.getVoiceVerification().getVerificationAttemptsCount());
                voiceVerification.put("language", voice.getVoiceVerification().getLanguage());
                voiceVerification.put("verification_attempts", voice.getVoiceVerification().getVerificationAttempts());
                attributes.put("voice_verification", voiceVerification);
            }

            // 11. 支持的语言列表（verified_languages）
            if (voice.getVerifiedLanguages() != null && !voice.getVerifiedLanguages().isEmpty()) {
                JSONArray verifiedLanguages = new JSONArray();
                for (ElevenLabsVoiceListResponseBO.VerifiedLanguage lang : voice.getVerifiedLanguages()) {
                    JSONObject langObj = new JSONObject();
                    langObj.put("language", lang.getLanguage());
                    langObj.put("locale", lang.getLocale());
                    langObj.put("accent", lang.getAccent());
                    langObj.put("model_id", lang.getModelId());
                    langObj.put("preview_url", lang.getPreviewUrl());
                    verifiedLanguages.add(langObj);
                }
                attributes.put("verified_languages", verifiedLanguages);
            }

            // 12. 标签信息（labels）
            if (voice.getLabels() != null) {
                JSONObject labels = new JSONObject();
                labels.put("language", voice.getLabels().getLanguage());
                labels.put("locale", voice.getLabels().getLocale());
                labels.put("accent", voice.getLabels().getAccent());
                labels.put("descriptive", voice.getLabels().getDescriptive());
                labels.put("age", voice.getLabels().getAge());
                labels.put("gender", voice.getLabels().getGender());
                labels.put("use_case", voice.getLabels().getUseCase());
                attributes.put("labels", labels);
            }

            // 13. 共享信息（sharing）
            if (voice.getSharing() != null) {
                JSONObject sharing = new JSONObject();
                sharing.put("status", voice.getSharing().getStatus());
                sharing.put("history_item_sample_id", voice.getSharing().getHistoryItemSampleId());
                sharing.put("date_unix", voice.getSharing().getDateUnix());
                sharing.put("whitelisted_emails", voice.getSharing().getWhitelistedEmails());
                sharing.put("public_owner_id", voice.getSharing().getPublicOwnerId());
                sharing.put("original_voice_id", voice.getSharing().getOriginalVoiceId());
                sharing.put("financial_rewards_enabled", voice.getSharing().getFinancialRewardsEnabled());
                sharing.put("free_users_allowed", voice.getSharing().getFreeUsersAllowed());
                sharing.put("live_moderation_enabled", voice.getSharing().getLiveModerationEnabled());
                sharing.put("rate", voice.getSharing().getRate());
                sharing.put("fiat_rate", voice.getSharing().getFiatRate());
                sharing.put("notice_period", voice.getSharing().getNoticePeriod());
                sharing.put("disable_at_unix", voice.getSharing().getDisableAtUnix());
                sharing.put("voice_mixing_allowed", voice.getSharing().getVoiceMixingAllowed());
                sharing.put("featured", voice.getSharing().getFeatured());
                sharing.put("category", voice.getSharing().getCategory());
                sharing.put("reader_app_enabled", voice.getSharing().getReaderAppEnabled());
                sharing.put("image_url", voice.getSharing().getImageUrl());
                sharing.put("ban_reason", voice.getSharing().getBanReason());
                sharing.put("liked_by_count", voice.getSharing().getLikedByCount());
                sharing.put("cloned_by_count", voice.getSharing().getClonedByCount());
                sharing.put("name", voice.getSharing().getName());
                sharing.put("description", voice.getSharing().getDescription());
                sharing.put("review_status", voice.getSharing().getReviewStatus());
                sharing.put("review_message", voice.getSharing().getReviewMessage());
                sharing.put("enabled_in_library", voice.getSharing().getEnabledInLibrary());
                sharing.put("instagram_username", voice.getSharing().getInstagramUsername());
                sharing.put("twitter_username", voice.getSharing().getTwitterUsername());
                sharing.put("youtube_username", voice.getSharing().getYoutubeUsername());
                sharing.put("tiktok_username", voice.getSharing().getTiktokUsername());
                sharing.put("moderation_check", voice.getSharing().getModerationCheck());
                sharing.put("reader_restricted_on", voice.getSharing().getReaderRestrictedOn());

                // 共享标签信息（sharing.labels）
                if (voice.getSharing().getLabels() != null) {
                    JSONObject sharingLabels = new JSONObject();
                    sharingLabels.put("language", voice.getSharing().getLabels().getLanguage());
                    sharingLabels.put("locale", voice.getSharing().getLabels().getLocale());
                    sharingLabels.put("accent", voice.getSharing().getLabels().getAccent());
                    sharingLabels.put("descriptive", voice.getSharing().getLabels().getDescriptive());
                    sharingLabels.put("age", voice.getSharing().getLabels().getAge());
                    sharingLabels.put("gender", voice.getSharing().getLabels().getGender());
                    sharingLabels.put("use_case", voice.getSharing().getLabels().getUseCase());
                    sharing.put("labels", sharingLabels);
                }

                attributes.put("sharing", sharing);
            }



            // 14. 提供商标识
            attributes.put("provider_type", ElevenLabsConstants.PROVIDER_TYPE);

            return attributes.toJSONString();

        } catch (Exception e) {
            log.warn("构建扩展属性失败: {}", e.getMessage());
            // 返回基本的提供商信息
            JSONObject fallback = new JSONObject();
            fallback.put("provider_type", ElevenLabsConstants.PROVIDER_TYPE);
            return fallback.toJSONString();
        }
    }

    /**
     * 根据错误码获取错误信息
     *
     * @param errorCode 错误码
     * @return 错误描述
     */
    public static String getErrorMessageByCode(String errorCode) {
        if (errorCode == null) {
            return "未知错误";
        }

        return switch (errorCode) {
            case "401" -> "认证失败，请检查 API Key 是否正确或权限是否足够";
            case "422" -> "请求参数错误，请检查请求参数格式";
            case "429" -> "请求频率超限，请稍后重试";
            case "500" -> "ElevenLabs 服务器内部错误";
            case "503" -> "ElevenLabs 服务暂时不可用";
            default -> "未知错误，错误码: " + errorCode;
        };
    }

    /**
     * 检查API密钥权限
     * 通过尝试获取语音列表来验证API密钥是否具有必要权限
     *
     * @return 权限检查结果信息
     */
    public static String checkApiKeyPermissions() {
        String methodName = "checkApiKeyPermissions";
        log.info("[{}]开始检查 ElevenLabs API 密钥权限", methodName);

        String apiKey = getElevenLabsApiKey();
        if (apiKey == null) {
            return "无法获取API密钥，请检查配置";
        }

        try {
            // 构建最小请求参数
            ElevenLabsVoiceListRequestBO requestBO = new ElevenLabsVoiceListRequestBO();
            requestBO.setPageSize(1); // 只获取1个结果来测试权限

            // 调用 API
            ElevenLabsVoiceListResponseBO responseBO = ElevenLabsApi.getVoicesList(requestBO, apiKey);

            if (responseBO != null) {
                log.info("[{}]API密钥权限检查通过", methodName);
                return "API密钥权限正常，可以访问语音列表";
            } else {
                log.warn("[{}]API密钥权限检查失败，响应为空", methodName);
                return "API密钥权限检查失败，可能缺少 voices_read 权限";
            }

        } catch (Exception e) {
            log.error("[{}]API密钥权限检查异常: {}", methodName, e.getMessage(), e);
            return "API密钥权限检查异常: " + e.getMessage();
        }
    }

    /**
     * 数据去重处理
     *
     * @param voices 语音列表
     * @return 去重后的语音列表
     */
    public static List<DigitalVoiceStylesPO> deduplicateVoices(List<DigitalVoiceStylesPO> voices) {
        if (voices == null || voices.isEmpty()) {
            return new ArrayList<>();
        }

        List<DigitalVoiceStylesPO> uniqueVoices = new ArrayList<>();
        List<String> seenVoiceIds = new ArrayList<>();

        for (DigitalVoiceStylesPO voice : voices) {
            if (voice.getVoiceId() != null && !seenVoiceIds.contains(voice.getVoiceId())) {
                uniqueVoices.add(voice);
                seenVoiceIds.add(voice.getVoiceId());
            }
        }

        log.info("去重处理：原始{}个，去重后{}个", voices.size(), uniqueVoices.size());
        return uniqueVoices;
    }

    /**
     * 验证数据一致性
     *
     * @param voices 语音列表
     * @throws IllegalArgumentException 数据验证失败时抛出
     */
    public static void validateDataConsistency(List<DigitalVoiceStylesPO> voices) {
        if (voices == null) return;

        for (DigitalVoiceStylesPO voice : voices) {
            // 检查必填字段
            if (!StringUtils.hasText(voice.getVoiceId())) {
                throw new IllegalArgumentException("VoiceId 不能为空");
            }
            if (!StringUtils.hasText(voice.getVoiceName())) {
                throw new IllegalArgumentException("VoiceName 不能为空");
            }
            if (!StringUtils.hasText(voice.getProvider())) {
                throw new IllegalArgumentException("Provider 不能为空");
            }
            if (voice.getVoiceType() == null) {
                throw new IllegalArgumentException("VoiceType 不能为空");
            }

            // 检查字段长度
            if (voice.getVoiceId().length() > 100) {
                throw new IllegalArgumentException("VoiceId 长度不能超过100");
            }
            if (voice.getVoiceName().length() > 200) {
                throw new IllegalArgumentException("VoiceName 长度不能超过200");
            }

            // 检查枚举值
            if (voice.getVoiceType() < 1 || voice.getVoiceType() > 4) {
                throw new IllegalArgumentException("VoiceType 必须为1-4之间的值");
            }
        }
    }

    /**
     * 文本转语音
     *
     * @param voiceId 语音ID
     * @param request 文本转语音请求参数
     * @param fileName 文件名（不含扩展名）
     * @param userId 用户ID
     * @return 文本转语音响应结果
     */
    public static Result<ElevenLabsTextToSpeechResponseBO> textToSpeech(
            String voiceId,
            ElevenLabsTextToSpeechRequestBO request,
            String fileName,
            String userId) {

        String methodName = "textToSpeech";
        log.info("[{}]开始处理ElevenLabs文本转语音请求，voiceId={}, userId={}", methodName, voiceId, userId);

        try {
            // 1. 参数验证
            if (!StringUtils.hasText(voiceId)) {
                log.error("[{}]语音ID不能为空", methodName);
                return Result.ERROR("语音ID不能为空");
            }

            if (request == null || !StringUtils.hasText(request.getText())) {
                log.error("[{}]请求参数或文本内容不能为空", methodName);
                return Result.ERROR("请求参数或文本内容不能为空");
            }

            if (!StringUtils.hasText(fileName)) {
                log.error("[{}]文件名不能为空", methodName);
                return Result.ERROR("文件名不能为空");
            }

            if (!StringUtils.hasText(userId)) {
                log.error("[{}]用户ID不能为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            // 2. 自动获取API Key
            String apiKey = getElevenLabsApiKey();
            if (apiKey == null) {
                log.error("[{}]无法获取ElevenLabs API Key", methodName);
                return Result.ERROR("ElevenLabs API配置错误，无法获取API Key");
            }

            // 3. 记录请求时间戳
            long requestTimestamp = System.currentTimeMillis();

            // 4. 调用ElevenLabsApi.textToSpeech()获取音频数据
            log.info("[{}]调用ElevenLabs API进行文本转语音，文本长度: {}", methodName, request.getText().length());
            byte[] audioData = ElevenLabsApi.textToSpeech(voiceId, request, apiKey);

            if (audioData == null || audioData.length == 0) {
                log.error("[{}]ElevenLabs API返回的音频数据为空", methodName);
                return Result.ERROR("文本转语音失败，API返回的音频数据为空");
            }

            log.info("[{}]成功获取音频数据，大小: {} bytes", methodName, audioData.length);

            // 5. 将音频字节数组上传到OSS获取URL
            String ossUrl = DigitalFileUtil.uploadDigitalResource(
                audioData,
                fileName,
                userId,
                null,
                5,    // 音频目录类型
                false // 用户文件
            );

            if (ossUrl == null) {
                log.error("[{}]音频文件上传到OSS失败", methodName);
                return Result.ERROR("音频文件上传失败");
            }

            log.info("[{}]音频文件上传成功，OSS URL: {}", methodName, ossUrl);

            // 6. 构建ElevenLabsTextToSpeechResponseBO响应
            ElevenLabsTextToSpeechResponseBO response = ElevenLabsTextToSpeechResponseBO.fromAudioData(
                audioData,
                ossUrl,
                request.getOutputFormat(),
                voiceId,
                request.getModelId()
            );

            // 设置请求时间戳和结果ID
            response.withRequestTimestamp(requestTimestamp)
                   .withResultId("elevenlabs-" + userId + "-" + System.currentTimeMillis());

            log.info("[{}]ElevenLabs文本转语音处理完成，音频时长: {} ms", methodName, response.getDuration());

            // 7. 包装为Result<T>格式返回
            return Result.SUCCESS(response);

        } catch (Exception e) {
            log.error("[{}]ElevenLabs文本转语音处理异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("文本转语音处理异常: " + e.getMessage());
        }
    }


}
