package com.nacos.model.Elevenlabs.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ElevenLabs 文本转语音响应模型
 * 
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ElevenLabs 文本转语音响应")
public class ElevenLabsTextToSpeechResponseBO {
    
    @Schema(description = "处理状态", example = "success")
    private String status;
    
    @Schema(description = "错误信息（如果有）", example = "")
    private String errorMessage;
    
    @Schema(description = "OSS存储URL")
    private String url;
    
    @Schema(description = "音频时长（毫秒）", example = "3500")
    private long duration;
    
    @Schema(description = "音频格式", example = "mp3_44100_128")
    private String format;
    
    @Schema(description = "音频长度（字节）", example = "84320")
    private long audioLength;
    
    @Schema(description = "结果ID", example = "elevenlabs-12345-abcdef")
    private String resultId;
    
    @Schema(description = "语音ID", example = "21m00Tcm4TlvDq8ikWAM")
    private String voiceId;
    
    @Schema(description = "模型ID", example = "eleven_multilingual_v2")
    private String modelId;
    
    @Schema(description = "请求时间戳", example = "1704672000000")
    private Long requestTimestamp;
    
    @Schema(description = "响应时间戳", example = "1704672005000")
    private Long responseTimestamp;
    
    /**
     * 创建成功响应
     * 
     * @param url OSS存储URL
     * @param format 音频格式
     * @param audioLength 音频长度（字节）
     * @param duration 音频时长（毫秒）
     * @param voiceId 语音ID
     * @param modelId 模型ID
     * @return 成功响应对象
     */
    public static ElevenLabsTextToSpeechResponseBO createSuccessResponse(
            String url, 
            String format, 
            long audioLength, 
            long duration,
            String voiceId,
            String modelId) {
        
        return ElevenLabsTextToSpeechResponseBO.builder()
                .status("success")
                .url(url)
                .format(format)
                .audioLength(audioLength)
                .duration(duration)
                .voiceId(voiceId)
                .modelId(modelId)
                .responseTimestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应
     * 
     * @param errorMessage 错误信息
     * @param voiceId 语音ID（可选）
     * @param modelId 模型ID（可选）
     * @return 失败响应对象
     */
    public static ElevenLabsTextToSpeechResponseBO createErrorResponse(
            String errorMessage, 
            String voiceId, 
            String modelId) {
        
        return ElevenLabsTextToSpeechResponseBO.builder()
                .status("fail")
                .errorMessage(errorMessage)
                .voiceId(voiceId)
                .modelId(modelId)
                .responseTimestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应（简化版）
     * 
     * @param errorMessage 错误信息
     * @return 失败响应对象
     */
    public static ElevenLabsTextToSpeechResponseBO createErrorResponse(String errorMessage) {
        return createErrorResponse(errorMessage, null, null);
    }
    
    /**
     * 从音频字节数组创建响应
     * 
     * @param audioData 音频字节数组
     * @param url OSS存储URL
     * @param format 音频格式
     * @param voiceId 语音ID
     * @param modelId 模型ID
     * @return 响应对象
     */
    public static ElevenLabsTextToSpeechResponseBO fromAudioData(
            byte[] audioData,
            String url,
            String format,
            String voiceId,
            String modelId) {
        
        if (audioData == null || audioData.length == 0) {
            return createErrorResponse("音频数据为空", voiceId, modelId);
        }
        
        // 估算音频时长（这里使用简单估算，实际可能需要更精确的计算）
        long estimatedDuration = estimateAudioDuration(audioData.length, format);
        
        return createSuccessResponse(url, format, audioData.length, estimatedDuration, voiceId, modelId);
    }
    
    /**
     * 估算音频时长
     * 
     * @param audioLength 音频长度（字节）
     * @param format 音频格式
     * @return 估算的时长（毫秒）
     */
    private static long estimateAudioDuration(long audioLength, String format) {
        // 简单的时长估算，基于不同格式的比特率
        // 这是一个粗略估算，实际应用中可能需要更精确的计算
        
        if (format == null) {
            return 0;
        }
        
        // 根据格式估算比特率（每秒字节数）
        long bytesPerSecond;
        if (format.contains("mp3_44100_128")) {
            bytesPerSecond = 16000; // 128kbps ≈ 16KB/s
        } else if (format.contains("mp3_22050_32")) {
            bytesPerSecond = 4000;  // 32kbps ≈ 4KB/s
        } else if (format.contains("pcm_16000")) {
            bytesPerSecond = 32000; // 16kHz 16bit mono ≈ 32KB/s
        } else if (format.contains("pcm_24000")) {
            bytesPerSecond = 48000; // 24kHz 16bit mono ≈ 48KB/s
        } else {
            bytesPerSecond = 16000; // 默认值
        }
        
        return (audioLength * 1000) / bytesPerSecond;
    }
    
    /**
     * 检查响应是否成功
     * 
     * @return true if successful, false otherwise
     */
    public boolean isSuccess() {
        return "success".equals(status);
    }
    
    /**
     * 设置请求时间戳
     * 
     * @param timestamp 时间戳
     * @return 当前对象（支持链式调用）
     */
    public ElevenLabsTextToSpeechResponseBO withRequestTimestamp(long timestamp) {
        this.requestTimestamp = timestamp;
        return this;
    }
    
    /**
     * 设置结果ID
     * 
     * @param resultId 结果ID
     * @return 当前对象（支持链式调用）
     */
    public ElevenLabsTextToSpeechResponseBO withResultId(String resultId) {
        this.resultId = resultId;
        return this;
    }
}
