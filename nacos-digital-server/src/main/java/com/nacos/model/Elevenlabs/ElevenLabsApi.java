package com.nacos.model.Elevenlabs;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.nacos.model.Elevenlabs.model.ElevenLabsTextToSpeechRequestBO;
import com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListRequestBO;
import com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO;
import com.nacos.model.Elevenlabs.model.enums.ElevenLabsApiEndpoint;
import com.nacos.model.Elevenlabs.model.enums.ElevenLabsConstants;
import com.nacos.model.Elevenlabs.model.enums.ElevenLabsErrorCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.concurrent.TimeUnit;

/**
 * ElevenLabs API 客户端
 * 负责底层 HTTP 请求处理
 */
@Slf4j
@Schema(title = "ElevenLabs API 客户端")
public class ElevenLabsApi {

    // 创建 OkHttpClient 实例
    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(ElevenLabsConstants.READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(ElevenLabsConstants.WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .connectTimeout(ElevenLabsConstants.CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .build();

    /**
     * 获取语音列表
     * 
     * @param requestBO 请求参数
     * @param apiKey    ElevenLabs API Key
     * @return 语音列表响应
     */
    public static ElevenLabsVoiceListResponseBO getVoicesList(ElevenLabsVoiceListRequestBO requestBO, String apiKey) {
        String methodName = "getVoicesList";
        try {
            // 构建查询参数
            HttpUrl.Builder urlBuilder = HttpUrl
                    .parse(ElevenLabsApiEndpoint.VOICES.getFullUrl(ElevenLabsConstants.BASE_URL)).newBuilder();

            if (requestBO != null) {
                // 添加分页参数
                if (requestBO.getNextPageToken() != null && !requestBO.getNextPageToken().isEmpty()) {
                    urlBuilder.addQueryParameter("next_page_token", requestBO.getNextPageToken());
                }
                if (requestBO.getPageSize() != null) {
                    urlBuilder.addQueryParameter("page_size", requestBO.getPageSize().toString());
                }

                // 添加搜索和排序参数
                if (requestBO.getSearch() != null && !requestBO.getSearch().isEmpty()) {
                    urlBuilder.addQueryParameter("search", requestBO.getSearch());
                }
                if (requestBO.getSort() != null && !requestBO.getSort().isEmpty()) {
                    urlBuilder.addQueryParameter("sort", requestBO.getSort());
                }
                if (requestBO.getSortDirection() != null && !requestBO.getSortDirection().isEmpty()) {
                    urlBuilder.addQueryParameter("sort_direction", requestBO.getSortDirection());
                }

                // 添加过滤参数
                if (requestBO.getVoiceType() != null && !requestBO.getVoiceType().isEmpty()) {
                    urlBuilder.addQueryParameter("voice_type", requestBO.getVoiceType());
                }
                if (requestBO.getCategory() != null && !requestBO.getCategory().isEmpty()) {
                    urlBuilder.addQueryParameter("category", requestBO.getCategory());
                }
                if (requestBO.getFineTuningState() != null && !requestBO.getFineTuningState().isEmpty()) {
                    urlBuilder.addQueryParameter("fine_tuning_state", requestBO.getFineTuningState());
                }
                if (requestBO.getCollectionId() != null && !requestBO.getCollectionId().isEmpty()) {
                    urlBuilder.addQueryParameter("collection_id", requestBO.getCollectionId());
                }
                if (requestBO.getIncludeTotalCount() != null) {
                    urlBuilder.addQueryParameter("include_total_count", requestBO.getIncludeTotalCount().toString());
                }
            }

            // 构建请求
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .addHeader(ElevenLabsConstants.API_KEY_HEADER, apiKey)
                    .addHeader("Accept", ElevenLabsConstants.ACCEPT_TYPE)
                    .addHeader("Content-Type", ElevenLabsConstants.CONTENT_TYPE)
                    .build();

            log.info("[{}]发送请求到 ElevenLabs API: {}", methodName, request.url());
            log.debug("[{}]请求详细信息: {},body: {}", methodName, request, request.body());

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                int statusCode = response.code();
                log.info("[{}]ElevenLabs API 响应状态码: {}", methodName, statusCode);

                if (response.body() == null) {
                    log.error("[{}]ElevenLabs API 响应体为空", methodName);
                    return null;
                }

                String responseBody = response.body().string();
                log.debug("[{}]ElevenLabs API 响应内容: {}", methodName, responseBody);

                if (response.isSuccessful()) {
                    // 成功响应，解析JSON
                    try {
                        ElevenLabsVoiceListResponseBO result = JSON.parseObject(responseBody,
                                new TypeReference<ElevenLabsVoiceListResponseBO>() {
                                });
                        log.info("[{}]成功获取语音列表，数量: {}", methodName,
                                result != null && result.getVoices() != null ? result.getVoices().size() : 0);
                        return result;
                    } catch (Exception e) {
                        log.error("[{}]解析响应JSON失败: {}", methodName, e.getMessage(), e);
                        return null;
                    }
                } else {
                    // 错误响应，记录详细错误信息
                    String errorMessage = getErrorMessage(statusCode, responseBody);

                    // 针对401权限错误提供更友好的提示
                    if (statusCode == 401) {
                        if (responseBody != null && responseBody.contains("missing_permissions")) {
                            log.error(
                                    "[{}]ElevenLabs API 权限不足: API密钥缺少必要权限。请检查API密钥是否具有 voices_read 权限。statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                        } else {
                            log.error("[{}]ElevenLabs API 认证失败: API密钥无效或已过期。请检查API密钥配置。statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                        }
                    } else {
                        log.error("[{}]ElevenLabs API 请求失败: statusCode={}, error={}",
                                methodName, statusCode, errorMessage);
                    }
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("[{}]ElevenLabs API 请求异常: {}", methodName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据状态码和响应体获取错误信息
     *
     * @param statusCode   HTTP状态码
     * @param responseBody 响应体
     * @return 错误信息
     */
    private static String getErrorMessage(int statusCode, String responseBody) {
        try {
            // 尝试解析错误响应
            if (responseBody != null && !responseBody.isEmpty()) {
                // ElevenLabs 错误响应格式通常为 {"detail": {"status": "error", "message": "..."}}
                if (responseBody.contains("detail") && responseBody.contains("message")) {
                    return responseBody;
                }
            }
        } catch (Exception e) {
            log.warn("解析错误响应失败: {}", e.getMessage());
        }

        // 根据状态码和响应内容返回详细错误信息
        return ElevenLabsErrorCode.getDetailedErrorMessage(statusCode, responseBody);
    }

    /**
     * 验证 API Key 是否有效
     *
     * @param apiKey ElevenLabs API Key
     * @return 是否有效
     */
    public static boolean validateApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }

        try {
            // 发送一个简单的请求来验证 API Key
            ElevenLabsVoiceListRequestBO requestBO = new ElevenLabsVoiceListRequestBO();
            requestBO.setPageSize(ElevenLabsConstants.MIN_PAGE_SIZE); // 只获取1个结果来验证

            ElevenLabsVoiceListResponseBO response = getVoicesList(requestBO, apiKey);
            return response != null;
        } catch (Exception e) {
            log.warn("验证 API Key 时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 文本转语音
     *
     * @param voiceId 语音ID
     * @param request 文本转语音请求参数
     * @param apiKey  ElevenLabs API Key
     * @return 音频字节数组，失败返回null
     */
    public static byte[] textToSpeech(String voiceId, ElevenLabsTextToSpeechRequestBO request, String apiKey) {
        String methodName = "textToSpeech";

        try {
            // 参数验证
            if (voiceId == null || voiceId.trim().isEmpty()) {
                log.error("[{}]语音ID不能为空", methodName);
                return null;
            }

            if (request == null || request.getText() == null || request.getText().trim().isEmpty()) {
                log.error("[{}]请求参数或文本内容不能为空", methodName);
                return null;
            }

            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("[{}]API Key不能为空", methodName);
                return null;
            }

            // 构建请求URL
            String url = ElevenLabsApiEndpoint.TEXT_TO_SPEECH.getFullUrlWithParam(
                    ElevenLabsConstants.BASE_URL, "voice_id", voiceId);

            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();

            // 添加查询参数
            if (request.getOutputFormat() != null && !request.getOutputFormat().isEmpty()) {
                urlBuilder.addQueryParameter("output_format", request.getOutputFormat());
            }

            if (request.getEnableLogging() != null) {
                urlBuilder.addQueryParameter("enable_logging", request.getEnableLogging().toString());
            }

            if (request.getOptimizeStreamingLatency() != null) {
                urlBuilder.addQueryParameter("optimize_streaming_latency",
                        request.getOptimizeStreamingLatency().toString());
            }

            // 构建请求体JSON
            String requestBodyJson = JSON.toJSONString(request);
            RequestBody requestBody = RequestBody.create(requestBodyJson,
                    MediaType.parse(ElevenLabsConstants.CONTENT_TYPE));

            // 构建HTTP请求
            Request httpRequest = new Request.Builder()
                    .url(urlBuilder.build())
                    .post(requestBody)
                    .addHeader(ElevenLabsConstants.API_KEY_HEADER, apiKey)
                    .addHeader("Accept", "audio/mpeg")
                    .addHeader("Content-Type", ElevenLabsConstants.CONTENT_TYPE)
                    .build();

            log.info("[{}]发送TTS请求到 ElevenLabs API: {}, voiceId: {}, textLength: {}",
                    methodName, httpRequest.url(), voiceId, request.getText().length());

            // 执行请求
            try (Response response = client.newCall(httpRequest).execute()) {
                int statusCode = response.code();
                log.info("[{}]ElevenLabs TTS API 响应状态码: {}", methodName, statusCode);

                if (response.body() == null) {
                    log.error("[{}]ElevenLabs TTS API 响应体为空", methodName);
                    return null;
                }

                if (response.isSuccessful()) {
                    // 成功响应，获取音频数据
                    byte[] audioData = response.body().bytes();
                    log.info("[{}]成功获取音频数据，大小: {} bytes", methodName, audioData.length);
                    return audioData;
                } else {
                    // 错误响应，记录详细错误信息
                    String responseBody = response.body().string();
                    String errorMessage = getErrorMessage(statusCode, responseBody);

                    // 针对不同错误码提供详细提示
                    switch (statusCode) {
                        case 401:
                            log.error("[{}]ElevenLabs TTS API 认证失败: API密钥无效或已过期。请检查API密钥配置。statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                            break;
                        case 422:
                            log.error("[{}]ElevenLabs TTS API 参数错误: 请检查语音ID、文本内容或其他参数。statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                            break;
                        case 429:
                            log.warn("[{}]ElevenLabs TTS API 请求频率限制: 请稍后重试。statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                            break;
                        case 500:
                            log.error("[{}]ElevenLabs TTS API 服务器内部错误: statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                            break;
                        default:
                            log.error("[{}]ElevenLabs TTS API 请求失败: statusCode={}, error={}",
                                    methodName, statusCode, errorMessage);
                            break;
                    }
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("[{}]ElevenLabs TTS API 请求异常: {}", methodName, e.getMessage(), e);
            return null;
        }
    }
}
