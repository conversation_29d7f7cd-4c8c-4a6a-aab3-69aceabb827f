package com.nacos.model.Elevenlabs.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * ElevenLabs 语音列表查询响应
 */
@Data
@Schema(title = "ElevenLabs 语音列表查询响应")
public class ElevenLabsVoiceListResponseBO {

    @Schema(title = "语音列表", description = "语音信息列表")
    private List<Voice> voices;

    @Schema(title = "是否有更多数据", description = "是否还有下一页数据")
    @JSONField(name = "has_more")
    private Boolean hasMore;

    @Schema(title = "总数量", description = "语音总数量")
    @JSONField(name = "total_count")
    private Integer totalCount;

    @Schema(title = "下一页令牌", description = "获取下一页数据的令牌")
    @JSONField(name = "next_page_token")
    private String nextPageToken;

    /**
     * 语音信息
     */
    @Data
    public static class Voice {
        @Schema(title = "语音ID", description = "语音唯一标识")
        @JSONField(name = "voice_id")
        private String voiceId;

        @Schema(title = "语音名称", description = "语音显示名称")
        private String name;

        @Schema(title = "音频样本", description = "语音样本列表")
        private List<Sample> samples;

        @Schema(title = "分类", description = "语音分类：generated, cloned, premade, professional")
        private String category;

        @Schema(title = "描述", description = "语音描述信息")
        private String description;

        @Schema(title = "预览URL", description = "语音预览音频URL")
        @JSONField(name = "preview_url")
        private String previewUrl;

        @Schema(title = "可用层级", description = "可用的订阅层级列表")
        @JSONField(name = "available_for_tiers")
        private List<String> availableForTiers;

        @Schema(title = "语音设置", description = "语音合成参数设置")
        private VoiceSettings settings;

        @Schema(title = "支持的语言", description = "语音支持的语言列表")
        @JSONField(name = "verified_languages")
        private List<VerifiedLanguage> verifiedLanguages;

        @Schema(title = "安全控制级别", description = "安全控制级别")
        @JSONField(name = "safety_control")
        private String safetyControl;

        @Schema(title = "是否为所有者", description = "当前用户是否为语音所有者")
        @JSONField(name = "is_owner")
        private Boolean isOwner;

        @Schema(title = "是否为遗留语音", description = "是否为遗留版本语音")
        @JSONField(name = "is_legacy")
        private Boolean isLegacy;

        @Schema(title = "是否为混合语音", description = "是否为混合语音")
        @JSONField(name = "is_mixed")
        private Boolean isMixed;

        @Schema(title = "创建时间", description = "语音创建时间（Unix时间戳）")
        @JSONField(name = "created_at_unix")
        private Long createdAtUnix;

        @Schema(title = "高质量模型ID", description = "高质量基础模型ID列表")
        @JSONField(name = "high_quality_base_model_ids")
        private List<String> highQualityBaseModelIds;

        @Schema(title = "资源权限", description = "对该资源的权限")
        @JSONField(name = "permission_on_resource")
        private String permissionOnResource;

        @Schema(title = "共享信息", description = "语音共享相关信息")
        private Sharing sharing;

        @Schema(title = "标签信息", description = "语音标签信息")
        private Labels labels;

        @Schema(title = "微调信息", description = "语音微调相关信息")
        @JSONField(name = "fine_tuning")
        private FineTuning fineTuning;

        @Schema(title = "语音验证信息", description = "语音验证相关信息")
        @JSONField(name = "voice_verification")
        private VoiceVerification voiceVerification;
    }

    /**
     * 音频样本信息
     */
    @Data
    public static class Sample {
        @Schema(title = "样本ID", description = "音频样本唯一标识")
        @JSONField(name = "sample_id")
        private String sampleId;

        @Schema(title = "文件名", description = "音频文件名")
        @JSONField(name = "file_name")
        private String fileName;

        @Schema(title = "MIME类型", description = "音频文件MIME类型")
        @JSONField(name = "mime_type")
        private String mimeType;

        @Schema(title = "文件大小", description = "音频文件大小（字节）")
        @JSONField(name = "size_bytes")
        private Long sizeBytes;

        @Schema(title = "文件哈希", description = "音频文件哈希值")
        private String hash;

        @Schema(title = "音频时长", description = "音频时长（秒）")
        @JSONField(name = "duration_secs")
        private Double durationSecs;

        @Schema(title = "是否移除背景噪音", description = "是否已移除背景噪音")
        @JSONField(name = "remove_background_noise")
        private Boolean removeBackgroundNoise;

        @Schema(title = "是否有隔离音频", description = "是否有隔离音频")
        @JSONField(name = "has_isolated_audio")
        private Boolean hasIsolatedAudio;

        @Schema(title = "是否有隔离音频预览", description = "是否有隔离音频预览")
        @JSONField(name = "has_isolated_audio_preview")
        private Boolean hasIsolatedAudioPreview;

        @Schema(title = "说话人分离", description = "说话人分离信息")
        @JSONField(name = "speaker_separation")
        private Object speakerSeparation;

        @Schema(title = "修剪开始", description = "修剪开始时间")
        @JSONField(name = "trim_start")
        private Object trimStart;

        @Schema(title = "修剪结束", description = "修剪结束时间")
        @JSONField(name = "trim_end")
        private Object trimEnd;
    }

    /**
     * 语音设置参数
     */
    @Data
    public static class VoiceSettings {
        @Schema(title = "稳定性", description = "语音稳定性参数 (0.0-1.0)")
        private Double stability;

        @Schema(title = "相似度增强", description = "相似度增强参数 (0.0-1.0)")
        @JSONField(name = "similarity_boost")
        private Double similarityBoost;

        @Schema(title = "风格强度", description = "风格强度参数 (0.0-1.0)")
        private Double style;

        @Schema(title = "语速", description = "语音速度 (0.25-4.0)")
        private Double speed;

        @Schema(title = "扬声器增强", description = "是否启用扬声器增强")
        @JSONField(name = "use_speaker_boost")
        private Boolean useSpeakerBoost;
    }

    /**
     * 支持的语言信息
     */
    @Data
    public static class VerifiedLanguage {
        @Schema(title = "语言代码", description = "语言代码，如 'en', 'zh'")
        private String language;

        @Schema(title = "模型ID", description = "语言模型ID")
        @JSONField(name = "model_id")
        private String modelId;

        @Schema(title = "口音", description = "语音口音")
        private String accent;

        @Schema(title = "地区代码", description = "地区代码，如 'en-US', 'zh-CN'")
        private String locale;

        @Schema(title = "预览URL", description = "该语言的预览音频URL")
        @JSONField(name = "preview_url")
        private String previewUrl;
    }

    /**
     * 共享信息
     */
    @Data
    public static class Sharing {
        @Schema(title = "共享状态", description = "共享状态")
        private String status;

        @Schema(title = "历史项样本ID", description = "历史项样本ID")
        @JSONField(name = "history_item_sample_id")
        private String historyItemSampleId;

        @Schema(title = "日期时间戳", description = "Unix时间戳")
        @JSONField(name = "date_unix")
        private Long dateUnix;

        @Schema(title = "白名单邮箱", description = "白名单邮箱列表")
        @JSONField(name = "whitelisted_emails")
        private Object whitelistedEmails;

        @Schema(title = "公开所有者ID", description = "公开所有者ID")
        @JSONField(name = "public_owner_id")
        private String publicOwnerId;

        @Schema(title = "原始语音ID", description = "原始语音ID")
        @JSONField(name = "original_voice_id")
        private String originalVoiceId;

        @Schema(title = "财务奖励启用", description = "是否启用财务奖励")
        @JSONField(name = "financial_rewards_enabled")
        private Boolean financialRewardsEnabled;

        @Schema(title = "免费用户允许", description = "是否允许免费用户使用")
        @JSONField(name = "free_users_allowed")
        private Boolean freeUsersAllowed;

        @Schema(title = "实时审核启用", description = "是否启用实时审核")
        @JSONField(name = "live_moderation_enabled")
        private Boolean liveModerationEnabled;

        @Schema(title = "费率", description = "使用费率")
        private Double rate;

        @Schema(title = "法币费率", description = "法币费率")
        @JSONField(name = "fiat_rate")
        private Double fiatRate;

        @Schema(title = "通知期", description = "通知期（天）")
        @JSONField(name = "notice_period")
        private Integer noticePeriod;

        @Schema(title = "禁用时间", description = "禁用时间Unix时间戳")
        @JSONField(name = "disable_at_unix")
        private Long disableAtUnix;

        @Schema(title = "语音混合允许", description = "是否允许语音混合")
        @JSONField(name = "voice_mixing_allowed")
        private Boolean voiceMixingAllowed;

        @Schema(title = "是否为精选", description = "是否为精选语音")
        private Boolean featured;

        @Schema(title = "共享分类", description = "共享分类")
        private String category;

        @Schema(title = "阅读器应用启用", description = "是否在阅读器应用中启用")
        @JSONField(name = "reader_app_enabled")
        private Boolean readerAppEnabled;

        @Schema(title = "图片URL", description = "图片URL")
        @JSONField(name = "image_url")
        private String imageUrl;

        @Schema(title = "封禁原因", description = "封禁原因")
        @JSONField(name = "ban_reason")
        private String banReason;

        @Schema(title = "点赞数", description = "被点赞的次数")
        @JSONField(name = "liked_by_count")
        private Integer likedByCount;

        @Schema(title = "克隆数", description = "被克隆的次数")
        @JSONField(name = "cloned_by_count")
        private Integer clonedByCount;

        @Schema(title = "名称", description = "共享语音名称")
        private String name;

        @Schema(title = "描述", description = "共享语音描述")
        private String description;

        @Schema(title = "标签信息", description = "共享语音的标签信息")
        private Labels labels;

        @Schema(title = "审核状态", description = "审核状态")
        @JSONField(name = "review_status")
        private String reviewStatus;

        @Schema(title = "审核消息", description = "审核消息")
        @JSONField(name = "review_message")
        private String reviewMessage;

        @Schema(title = "库中启用", description = "是否在库中启用")
        @JSONField(name = "enabled_in_library")
        private Boolean enabledInLibrary;

        @Schema(title = "Instagram用户名", description = "Instagram用户名")
        @JSONField(name = "instagram_username")
        private String instagramUsername;

        @Schema(title = "Twitter用户名", description = "Twitter用户名")
        @JSONField(name = "twitter_username")
        private String twitterUsername;

        @Schema(title = "YouTube用户名", description = "YouTube用户名")
        @JSONField(name = "youtube_username")
        private String youtubeUsername;

        @Schema(title = "TikTok用户名", description = "TikTok用户名")
        @JSONField(name = "tiktok_username")
        private String tiktokUsername;

        @Schema(title = "审核检查", description = "审核检查")
        @JSONField(name = "moderation_check")
        private Object moderationCheck;

        @Schema(title = "阅读器限制时间", description = "阅读器限制时间")
        @JSONField(name = "reader_restricted_on")
        private Object readerRestrictedOn;
    }

    /**
     * 标签信息
     */
    @Data
    public static class Labels {
        @Schema(title = "语言代码", description = "语言代码，如 'en', 'zh'")
        private String language;

        @Schema(title = "地区代码", description = "地区代码，如 'en-US', 'zh-CN'")
        private String locale;

        @Schema(title = "口音", description = "语音口音")
        private String accent;

        @Schema(title = "描述性标签", description = "描述性标签")
        private String descriptive;

        @Schema(title = "年龄", description = "年龄标签")
        private String age;

        @Schema(title = "性别", description = "性别标签")
        private String gender;

        @Schema(title = "使用场景", description = "使用场景标签")
        @JSONField(name = "use_case")
        private String useCase;
    }

    /**
     * 微调信息
     */
    @Data
    public static class FineTuning {
        @Schema(title = "是否允许微调", description = "是否允许对该语音进行微调")
        @JSONField(name = "is_allowed_to_fine_tune")
        private Boolean isAllowedToFineTune;

        @Schema(title = "微调状态", description = "各模型的微调状态")
        private Object state;

        @Schema(title = "验证失败", description = "验证失败列表")
        @JSONField(name = "verification_failures")
        private Object verificationFailures;

        @Schema(title = "验证尝试次数", description = "验证尝试次数")
        @JSONField(name = "verification_attempts_count")
        private Integer verificationAttemptsCount;

        @Schema(title = "手动验证请求", description = "是否请求手动验证")
        @JSONField(name = "manual_verification_requested")
        private Boolean manualVerificationRequested;

        @Schema(title = "语言", description = "微调语言")
        private String language;

        @Schema(title = "进度", description = "微调进度")
        private Object progress;

        @Schema(title = "消息", description = "微调消息")
        private Object message;

        @Schema(title = "数据集时长", description = "数据集时长（秒）")
        @JSONField(name = "dataset_duration_seconds")
        private Double datasetDurationSeconds;

        @Schema(title = "验证尝试", description = "验证尝试")
        @JSONField(name = "verification_attempts")
        private Object verificationAttempts;

        @Schema(title = "切片ID", description = "切片ID列表")
        @JSONField(name = "slice_ids")
        private Object sliceIds;

        @Schema(title = "手动验证", description = "手动验证")
        @JSONField(name = "manual_verification")
        private Object manualVerification;

        @Schema(title = "最大验证尝试次数", description = "最大验证尝试次数")
        @JSONField(name = "max_verification_attempts")
        private Integer maxVerificationAttempts;

        @Schema(title = "下次重置时间", description = "下次最大验证尝试次数重置时间")
        @JSONField(name = "next_max_verification_attempts_reset_unix_ms")
        private Long nextMaxVerificationAttemptsResetUnixMs;
    }

    /**
     * 语音验证信息
     */
    @Data
    public static class VoiceVerification {
        @Schema(title = "需要验证", description = "是否需要验证")
        @JSONField(name = "requires_verification")
        private Boolean requiresVerification;

        @Schema(title = "已验证", description = "是否已验证")
        @JSONField(name = "is_verified")
        private Boolean isVerified;

        @Schema(title = "验证失败", description = "验证失败列表")
        @JSONField(name = "verification_failures")
        private Object verificationFailures;

        @Schema(title = "验证尝试次数", description = "验证尝试次数")
        @JSONField(name = "verification_attempts_count")
        private Integer verificationAttemptsCount;

        @Schema(title = "语言", description = "验证语言")
        private String language;

        @Schema(title = "验证尝试", description = "验证尝试")
        @JSONField(name = "verification_attempts")
        private Object verificationAttempts;
    }
}
