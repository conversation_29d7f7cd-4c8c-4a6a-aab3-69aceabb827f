package com.nacos.model.Elevenlabs.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ElevenLabs 语音列表查询请求参数
 */
@Data
@Schema(title = "ElevenLabs 语音列表查询请求参数")
public class ElevenLabsVoiceListRequestBO {

    @Schema(title = "分页令牌", description = "用于获取下一页数据的令牌")
    @JSONField(name = "next_page_token")
    private String nextPageToken;

    @Schema(title = "每页数量", description = "每页返回的语音数量，默认10，最大100")
    @JSONField(name = "page_size")
    private Integer pageSize = 10;

    @Schema(title = "搜索关键词", description = "支持名称、描述、标签、分类搜索")
    private String search;

    @Schema(title = "排序字段", description = "可选 'created_at_unix' 或 'name'")
    private String sort;

    @Schema(title = "排序方向", description = "'asc' 升序或 'desc' 降序")
    @JSONField(name = "sort_direction")
    private String sortDirection;

    @Schema(title = "语音类型过滤", description = "personal, community, default, workspace, non-default")
    @JSONField(name = "voice_type")
    private String voiceType;

    @Schema(title = "分类过滤", description = "premade, cloned, generated, professional")
    private String category;

    @Schema(title = "微调状态过滤", description = "微调状态过滤条件")
    @JSONField(name = "fine_tuning_state")
    private String fineTuningState;

    @Schema(title = "集合ID过滤", description = "按集合ID过滤语音")
    @JSONField(name = "collection_id")
    private String collectionId;

    @Schema(title = "是否包含总数", description = "是否在响应中包含总数量，默认true")
    @JSONField(name = "include_total_count")
    private Boolean includeTotalCount = true;
}
