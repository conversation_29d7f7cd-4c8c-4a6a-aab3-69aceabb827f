package com.nacos.model.Elevenlabs.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ElevenLabs 文本转语音请求模型
 * 
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ElevenLabs 文本转语音请求")
public class ElevenLabsTextToSpeechRequestBO {

    @NotBlank(message = "待合成的文本不能为空")
    @Size(max = 10000, message = "待合成的文本长度不能超过10000字符")
    @Schema(description = "要转换为语音的文本内容", example = "你好，这是一段测试文本", required = true)
    private String text;

    @Schema(description = "模型ID", example = "eleven_multilingual_v2")
    @JSONField(name = "model_id")
    @Builder.Default
    private String modelId = "eleven_multilingual_v2";

    @Schema(description = "语言代码", example = "zh-CN")
    @JSONField(name = "language_code")
    private String languageCode;

    @Schema(description = "语音设置参数")
    @JSONField(name = "voice_settings")
    private VoiceSettings voiceSettings;

    @Schema(description = "输出格式", example = "mp3_44100_128")
    @Builder.Default
    private String outputFormat = "mp3_44100_128";

    @Schema(description = "是否启用日志", example = "true")
    @JSONField(name = "enable_logging")
    @Builder.Default
    private Boolean enableLogging = true;

    @Schema(description = "延迟优化级别 (0-4)", example = "0")
    @JSONField(name = "optimize_streaming_latency")
    private Integer optimizeStreamingLatency;

    @Schema(description = "发音词典定位器列表")
    @JSONField(name = "pronunciation_dictionary_locators")
    private List<PronunciationDictionaryLocator> pronunciationDictionaryLocators;

    @Schema(description = "随机种子", example = "0")
    private Integer seed;

    @Schema(description = "前置文本")
    @JSONField(name = "previous_text")
    private String previousText;

    @Schema(description = "后续文本")
    @JSONField(name = "next_text")
    private String nextText;

    @Schema(description = "前置请求ID列表")
    @JSONField(name = "previous_request_ids")
    private List<String> previousRequestIds;

    @Schema(description = "后续请求ID列表")
    @JSONField(name = "next_request_ids")
    private List<String> nextRequestIds;

    @Schema(description = "应用文本标准化", example = "auto")
    @JSONField(name = "apply_text_normalization")
    @Builder.Default
    private String applyTextNormalization = "auto";

    @Schema(description = "应用语言文本标准化", example = "false")
    @JSONField(name = "apply_language_text_normalization")
    @Builder.Default
    private Boolean applyLanguageTextNormalization = false;

    @Schema(description = "使用PVC作为IVC", example = "false")
    @JSONField(name = "use_pvc_as_ivc")
    @Builder.Default
    private Boolean usePvcAsIvc = false;

    /**
     * 语音设置参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "语音设置参数")
    public static class VoiceSettings {
        @Schema(description = "稳定性参数 (0.0-1.0)", example = "0.5")
        @Builder.Default
        private Double stability = 0.5;

        @Schema(description = "相似度增强参数 (0.0-1.0)", example = "0.5")
        @JSONField(name = "similarity_boost")
        @Builder.Default
        private Double similarityBoost = 0.5;

        @Schema(description = "风格强度参数 (0.0-1.0)", example = "0.5")
        @Builder.Default
        private Double style = 0.5;

        @Schema(description = "语音速度 (0.25-4.0)", example = "1.0")
        @Builder.Default
        private Double speed = 1.0;

        @Schema(description = "是否启用扬声器增强", example = "true")
        @JSONField(name = "use_speaker_boost")
        @Builder.Default
        private Boolean useSpeakerBoost = true;
    }

    /**
     * 发音词典定位器
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "发音词典定位器")
    public static class PronunciationDictionaryLocator {
        @Schema(description = "发音词典ID")
        @JSONField(name = "pronunciation_dictionary_id")
        private String pronunciationDictionaryId;

        @Schema(description = "版本ID")
        @JSONField(name = "version_id")
        private String versionId;
    }
}
