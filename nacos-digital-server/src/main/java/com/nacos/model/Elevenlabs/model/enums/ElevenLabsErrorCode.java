package com.nacos.model.Elevenlabs.model.enums;

import lombok.Getter;

/**
 * ElevenLabs API 错误码枚举
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
public enum ElevenLabsErrorCode {
    
    /**
     * 认证失败
     */
    UNAUTHORIZED(401, "认证失败，请检查 API Key 是否正确"),

    /**
     * 权限不足
     */
    PERMISSION_DENIED(401, "API密钥权限不足，请检查是否具有必要的权限（如 voices_read）"),
    
    /**
     * 请求参数错误
     */
    UNPROCESSABLE_ENTITY(422, "请求参数错误，请检查请求参数格式"),
    
    /**
     * 请求频率超限
     */
    TOO_MANY_REQUESTS(429, "请求频率超限，请稍后重试"),
    
    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500, "ElevenLabs 服务器内部错误"),
    
    /**
     * 服务暂时不可用
     */
    SERVICE_UNAVAILABLE(503, "ElevenLabs 服务暂时不可用"),
    
    /**
     * 未知错误
     */
    UNKNOWN_ERROR(-1, "未知错误");
    
    /**
     * HTTP状态码
     */
    private final int statusCode;
    
    /**
     * 错误信息
     */
    private final String message;
    
    /**
     * 构造函数
     * 
     * @param statusCode HTTP状态码
     * @param message 错误信息
     */
    ElevenLabsErrorCode(int statusCode, String message) {
        this.statusCode = statusCode;
        this.message = message;
    }
    
    /**
     * 根据状态码获取错误枚举
     * 
     * @param statusCode HTTP状态码
     * @return 对应的错误枚举
     */
    public static ElevenLabsErrorCode fromStatusCode(int statusCode) {
        for (ElevenLabsErrorCode errorCode : values()) {
            if (errorCode.getStatusCode() == statusCode) {
                return errorCode;
            }
        }
        return UNKNOWN_ERROR;
    }
    
    /**
     * 根据状态码获取错误信息
     *
     * @param statusCode HTTP状态码
     * @return 错误信息
     */
    public static String getErrorMessage(int statusCode) {
        ElevenLabsErrorCode errorCode = fromStatusCode(statusCode);
        if (errorCode == UNKNOWN_ERROR && statusCode != -1) {
            return "未知错误，状态码: " + statusCode;
        }
        return errorCode.getMessage();
    }

    /**
     * 根据状态码和响应内容获取详细错误信息
     *
     * @param statusCode HTTP状态码
     * @param responseBody 响应体内容
     * @return 详细错误信息
     */
    public static String getDetailedErrorMessage(int statusCode, String responseBody) {
        // 对于401错误，检查是否为权限问题
        if (statusCode == 401 && responseBody != null && responseBody.contains("missing_permissions")) {
            return PERMISSION_DENIED.getMessage();
        }

        // 其他情况使用通用错误信息
        return getErrorMessage(statusCode);
    }
    
    /**
     * 检查状态码是否为成功状态
     * 
     * @param statusCode HTTP状态码
     * @return 是否成功
     */
    public static boolean isSuccess(int statusCode) {
        return statusCode >= 200 && statusCode < 300;
    }
    
    /**
     * 检查状态码是否为客户端错误
     * 
     * @param statusCode HTTP状态码
     * @return 是否为客户端错误
     */
    public static boolean isClientError(int statusCode) {
        return statusCode >= 400 && statusCode < 500;
    }
    
    /**
     * 检查状态码是否为服务器错误
     * 
     * @param statusCode HTTP状态码
     * @return 是否为服务器错误
     */
    public static boolean isServerError(int statusCode) {
        return statusCode >= 500 && statusCode < 600;
    }
}
