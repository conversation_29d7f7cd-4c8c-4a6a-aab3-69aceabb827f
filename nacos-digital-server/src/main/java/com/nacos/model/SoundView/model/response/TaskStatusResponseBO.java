package com.nacos.model.SoundView.model.response;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 羚羊平台任务状态查询响应模型
 * 对应API: /open/v1/video/info/get
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "任务状态查询响应结果")
public class TaskStatusResponseBO {

    /**
     * 返回码
     */
    @Schema(description = "返回码", example = "0")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "SUCCESS")
    @JSONField(name = "message")
    private String message;

    /**
     * 返回结果
     */
    @Schema(description = "返回结果")
    @JSONField(name = "result")
    private TaskStatusResult result;

    /**
     * 任务状态结果内部类
     */
    @Data
    @Schema(description = "任务状态结果详情")
    public static class TaskStatusResult {

        /**
         * 任务ID
         */
        @Schema(description = "任务ID", example = "12345")
        @JSONField(name = "id")
        private String id;

        /**
         * 视频名称
         */
        @Schema(description = "视频名称", example = "测试视频")
        @JSONField(name = "name")
        private String name;

        /**
         * 封面URL
         */
        @Schema(description = "封面URL", example = "https://example.com/cover.jpg")
        @JSONField(name = "coverUrl")
        private String coverUrl;

        /**
         * 目标视频URL
         */
        @Schema(description = "目标视频URL", example = "https://example.com/translated_video.mp4")
        @JSONField(name = "destFileTmpUrl")
        private String destFileTmpUrl;

        /**
         * 目标字幕URL
         */
        @Schema(description = "目标字幕URL", example = "https://example.com/subtitle.srt")
        @JSONField(name = "destStrTmpUrl")
        private String destStrTmpUrl;

        /**
         * 转写内容
         */
        @Schema(description = "转写内容", example = "原始转写内容")
        @JSONField(name = "originalRtasrInfo")
        private String originalRtasrInfo;

        /**
         * 目标TTS文本内容
         */
        @Schema(description = "目标TTS文本内容", example = "目标TTS文本内容")
        @JSONField(name = "destRtasrInfo")
        private String destRtasrInfo;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态", example = "completed")
        @JSONField(name = "status")
        private String status;

        /**
         * 进度百分比
         */
        @Schema(description = "进度百分比", example = "100")
        @JSONField(name = "progress")
        private Integer progress;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息", example = "处理失败原因")
        @JSONField(name = "errorMessage")
        private String errorMessage;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2025-01-25 10:30:00")
        @JSONField(name = "createTime")
        private String createTime;

        /**
         * 完成时间
         */
        @Schema(description = "完成时间", example = "2025-01-25 10:35:00")
        @JSONField(name = "finishTime")
        private String finishTime;

        // ==================== 业务方法 ====================

        /**
         * 检查任务是否完成
         */
        public boolean isCompleted() {
            return "completed".equalsIgnoreCase(status);
        }

        /**
         * 检查任务是否失败
         */
        public boolean isFailed() {
            return "failed".equalsIgnoreCase(status);
        }

        /**
         * 检查任务是否处理中
         */
        public boolean isProcessing() {
            return status != null && 
                   (status.equalsIgnoreCase("processing") || 
                    status.equalsIgnoreCase("submitted") ||
                    status.equalsIgnoreCase("waiting"));
        }

        /**
         * 检查任务是否为最终状态
         */
        public boolean isFinalStatus() {
            return isCompleted() || isFailed() || 
                   "cancelled".equalsIgnoreCase(status) ||
                   "timeout".equalsIgnoreCase(status);
        }

        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            if (status == null) {
                return "未知状态";
            }
            switch (status.toLowerCase()) {
                case "submitted": return "已提交";
                case "waiting": return "等待中";
                case "processing": return "处理中";
                case "completed": return "已完成";
                case "failed": return "失败";
                case "cancelled": return "已取消";
                case "timeout": return "超时";
                default: return status;
            }
        }

        /**
         * 获取进度百分比
         */
        public Integer getProgressPercentage() {
            if (progress != null) {
                return progress;
            }
            
            // 根据状态估算进度
            if (status == null) {
                return 0;
            }
            switch (status.toLowerCase()) {
                case "submitted": return 5;
                case "waiting": return 10;
                case "processing": return 50;
                case "completed": return 100;
                case "failed":
                case "cancelled":
                case "timeout": return -1;
                default: return 0;
            }
        }

        /**
         * 检查是否有结果文件
         */
        public boolean hasResultFiles() {
            return (destFileTmpUrl != null && !destFileTmpUrl.trim().isEmpty()) ||
                   (destStrTmpUrl != null && !destStrTmpUrl.trim().isEmpty());
        }

        /**
         * 检查是否有视频结果
         */
        public boolean hasVideoResult() {
            return destFileTmpUrl != null && !destFileTmpUrl.trim().isEmpty();
        }

        /**
         * 检查是否有字幕结果
         */
        public boolean hasSubtitleResult() {
            return destStrTmpUrl != null && !destStrTmpUrl.trim().isEmpty();
        }

        /**
         * 获取任务摘要信息
         */
        public String getSummary() {
            return String.format("任务[%s]: %s - %s (%d%%)", 
                id, name != null ? name : "未知", 
                getStatusDescription(), getProgressPercentage());
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return code == null || code != 0;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "未知错误";
    }

    /**
     * 获取任务状态
     */
    public String getTaskStatus() {
        return result != null ? result.getStatus() : null;
    }

    /**
     * 检查任务是否完成
     */
    public boolean isTaskCompleted() {
        return isSuccess() && result != null && result.isCompleted();
    }

    /**
     * 检查任务是否失败
     */
    public boolean isTaskFailed() {
        return hasError() || (result != null && result.isFailed());
    }

    /**
     * 检查任务是否处理中
     */
    public boolean isTaskProcessing() {
        return isSuccess() && result != null && result.isProcessing();
    }

    /**
     * 获取视频结果URL
     */
    public String getVideoResultUrl() {
        return result != null ? result.getDestFileTmpUrl() : null;
    }

    /**
     * 获取字幕结果URL
     */
    public String getSubtitleResultUrl() {
        return result != null ? result.getDestStrTmpUrl() : null;
    }

    /**
     * 获取原始文本
     */
    public String getOriginalText() {
        return result != null ? result.getOriginalRtasrInfo() : null;
    }

    /**
     * 获取翻译文本
     */
    public String getTranslatedText() {
        return result != null ? result.getDestRtasrInfo() : null;
    }

    /**
     * 获取进度百分比
     */
    public Integer getProgress() {
        return result != null ? result.getProgressPercentage() : 0;
    }

    /**
     * 获取响应摘要信息
     */
    public String getSummary() {
        if (isSuccess() && result != null) {
            return result.getSummary();
        } else {
            return String.format("查询失败: code=%d, message=%s", code, message);
        }
    }

    /**
     * 创建成功响应
     */
    public static TaskStatusResponseBO success(TaskStatusResult result) {
        TaskStatusResponseBO response = new TaskStatusResponseBO();
        response.setCode(0);
        response.setMessage("SUCCESS");
        response.setResult(result);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static TaskStatusResponseBO failure(Integer code, String message) {
        TaskStatusResponseBO response = new TaskStatusResponseBO();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    @Override
    public String toString() {
        return "TaskStatusResponseBO{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
