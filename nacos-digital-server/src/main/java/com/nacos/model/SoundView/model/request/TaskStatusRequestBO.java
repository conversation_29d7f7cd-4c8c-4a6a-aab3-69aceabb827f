package com.nacos.model.SoundView.model.request;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 羚羊平台任务状态查询请求模型
 * 对应API: /open/v1/video/info/get
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "任务状态查询请求参数")
public class TaskStatusRequestBO {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", required = true, example = "12345")
    @JSONField(name = "id")
    @NotNull(message = "任务ID不能为空")
    private Long id;

    // ==================== 构造方法 ====================

    public TaskStatusRequestBO() {
    }

    public TaskStatusRequestBO(Long id) {
        this.id = id;
    }

    public TaskStatusRequestBO(String taskId) {
        try {
            this.id = Long.parseLong(taskId);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("任务ID格式错误: " + taskId);
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return id != null && id > 0;
    }

    /**
     * 获取任务ID字符串
     */
    public String getTaskIdString() {
        return id != null ? id.toString() : null;
    }

    /**
     * 设置任务ID（字符串）
     */
    public void setTaskIdString(String taskId) {
        if (taskId != null && !taskId.trim().isEmpty()) {
            try {
                this.id = Long.parseLong(taskId.trim());
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("任务ID格式错误: " + taskId);
            }
        }
    }

    /**
     * 检查任务ID是否有效
     */
    public boolean isTaskIdValid() {
        return id != null && id > 0;
    }

    /**
     * 获取请求摘要信息
     */
    public String getSummary() {
        return "查询任务状态: " + (id != null ? id : "未知");
    }

    @Override
    public String toString() {
        return "TaskStatusRequestBO{" +
                "id=" + id +
                '}';
    }
}
