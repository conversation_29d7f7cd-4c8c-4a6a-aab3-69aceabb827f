package com.nacos.model.SoundView.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 视频翻译任务状态枚举
 * 基于羚羊平台的任务状态定义，支持完整的任务生命周期管理
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(name = "视频翻译状态枚举", description = "视频翻译任务的状态类型")
public enum VideoTranslateStatusEnum {

    /**
     * 已提交 - 任务已提交到羚羊平台，等待处理
     */
    SUBMITTED("submitted", "已提交", 5, false, false),

    /**
     * 处理中 - 羚羊平台正在处理视频翻译
     */
    PROCESSING("processing", "处理中", 50, false, false),

    /**
     * 已完成 - 视频翻译完成，可获取结果
     */
    COMPLETED("completed", "已完成", 100, true, false),

    /**
     * 失败 - 视频翻译处理失败
     */
    FAILED("failed", "失败", -1, true, true),

    /**
     * 已取消 - 用户主动取消任务
     */
    CANCELLED("cancelled", "已取消", -1, true, true),

    /**
     * 超时 - 任务处理超时
     */
    TIMEOUT("timeout", "超时", -1, true, true),

    /**
     * 等待中 - 任务在队列中等待处理
     */
    WAITING("waiting", "等待中", 10, false, false),

    /**
     * 准备中 - 正在准备处理资源
     */
    PREPARING("preparing", "准备中", 20, false, false),

    /**
     * 上传中 - 正在上传视频文件
     */
    UPLOADING("uploading", "上传中", 15, false, false);

    /**
     * 状态代码（与羚羊平台保持一致）
     */
    @Schema(description = "状态代码")
    private final String code;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述")
    private final String description;

    /**
     * 进度百分比（-1表示失败状态）
     */
    @Schema(description = "进度百分比")
    private final int progress;

    /**
     * 是否为最终状态
     */
    @Schema(description = "是否为最终状态")
    private final boolean isFinalStatus;

    /**
     * 是否为失败状态
     */
    @Schema(description = "是否为失败状态")
    private final boolean isFailureStatus;

    VideoTranslateStatusEnum(String code, String description, int progress, boolean isFinalStatus, boolean isFailureStatus) {
        this.code = code;
        this.description = description;
        this.progress = progress;
        this.isFinalStatus = isFinalStatus;
        this.isFailureStatus = isFailureStatus;
    }

    // ==================== Getter方法 ====================

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public int getProgress() {
        return progress;
    }

    public boolean isFinalStatus() {
        return isFinalStatus;
    }

    public boolean isFailureStatus() {
        return isFailureStatus;
    }

    // ==================== 业务方法 ====================

    /**
     * 根据状态代码获取枚举
     */
    public static VideoTranslateStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (VideoTranslateStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为处理中状态
     */
    public boolean isProcessing() {
        return this == SUBMITTED || this == PROCESSING || this == WAITING || 
               this == PREPARING || this == UPLOADING;
    }

    /**
     * 检查是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }

    /**
     * 检查是否为失败状态
     */
    public boolean isFailed() {
        return isFailureStatus;
    }

    /**
     * 检查状态转换是否有效
     */
    public boolean canTransitionTo(VideoTranslateStatusEnum targetStatus) {
        if (targetStatus == null) {
            return false;
        }

        // 最终状态不能转换到其他状态
        if (this.isFinalStatus) {
            return false;
        }

        // 定义有效的状态转换路径
        switch (this) {
            case SUBMITTED:
                return targetStatus == WAITING || targetStatus == PREPARING || 
                       targetStatus == UPLOADING || targetStatus == PROCESSING ||
                       targetStatus == CANCELLED || targetStatus == FAILED;
                       
            case WAITING:
                return targetStatus == PREPARING || targetStatus == UPLOADING ||
                       targetStatus == PROCESSING || targetStatus == CANCELLED || 
                       targetStatus == FAILED || targetStatus == TIMEOUT;
                       
            case PREPARING:
            case UPLOADING:
                return targetStatus == PROCESSING || targetStatus == CANCELLED || 
                       targetStatus == FAILED || targetStatus == TIMEOUT;
                       
            case PROCESSING:
                return targetStatus == COMPLETED || targetStatus == FAILED || 
                       targetStatus == TIMEOUT || targetStatus == CANCELLED;
                       
            default:
                return false;
        }
    }

    /**
     * 获取状态对应的进度描述
     */
    public String getProgressDescription() {
        if (progress < 0) {
            return "处理失败";
        } else if (progress == 0) {
            return "未开始";
        } else if (progress == 100) {
            return "已完成";
        } else {
            return "进行中 " + progress + "%";
        }
    }

    /**
     * 获取下一个可能的状态列表
     */
    public VideoTranslateStatusEnum[] getNextPossibleStatuses() {
        return java.util.Arrays.stream(values())
                .filter(this::canTransitionTo)
                .toArray(VideoTranslateStatusEnum[]::new);
    }

    /**
     * 检查是否可以取消
     */
    public boolean isCancellable() {
        return !isFinalStatus && this != CANCELLED;
    }

    /**
     * 检查是否可以重试
     */
    public boolean isRetryable() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 获取状态的CSS样式类名（用于前端显示）
     */
    public String getCssClass() {
        if (isSuccess()) {
            return "status-success";
        } else if (isFailed()) {
            return "status-error";
        } else if (isProcessing()) {
            return "status-processing";
        } else {
            return "status-default";
        }
    }

    /**
     * 获取状态的颜色代码（用于前端显示）
     */
    public String getColorCode() {
        if (isSuccess()) {
            return "#52c41a"; // 绿色
        } else if (isFailed()) {
            return "#ff4d4f"; // 红色
        } else if (isProcessing()) {
            return "#1890ff"; // 蓝色
        } else {
            return "#d9d9d9"; // 灰色
        }
    }

    /**
     * 获取完整的状态信息描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(description);
        
        if (progress >= 0) {
            sb.append(" (").append(progress).append("%)");
        }
        
        if (isFinalStatus) {
            sb.append(" [最终状态]");
        }
        
        return sb.toString();
    }
}
