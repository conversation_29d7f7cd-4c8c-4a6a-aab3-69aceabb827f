package com.nacos.model.SoundView.model.request;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 羚羊平台语种列表查询请求模型
 * 对应API: /open/v1/lang/list
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "语种列表查询请求参数")
public class LanguageListRequestBO {

    /**
     * 类型 - TTS支持的语种列表
     */
    @Schema(description = "类型", required = true, example = "3")
    @JSONField(name = "type")
    private Integer type = 3;

    // ==================== 构造方法 ====================

    public LanguageListRequestBO() {
        this.type = 3;
    }

    public LanguageListRequestBO(Integer type) {
        this.type = type != null ? type : 3;
    }

    // ==================== 业务方法 ====================

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return type != null && type == 3;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (type == null) {
            type = 3;
        }
    }

    /**
     * 获取请求摘要信息
     */
    public String getSummary() {
        return String.format("查询语种列表: type=%d", type != null ? type : 3);
    }

    @Override
    public String toString() {
        return "LanguageListRequestBO{" +
                "type=" + type +
                '}';
    }
}
