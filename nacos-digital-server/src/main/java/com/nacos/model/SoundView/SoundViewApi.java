package com.nacos.model.SoundView;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.business.utils.BThirdPartyKey;
import com.nacos.enums.DictConfigEnum;
import com.nacos.model.SoundView.model.request.*;
import com.nacos.model.SoundView.model.response.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * 讯飞羚羊平台API核心调用类
 * 提供MD5签名认证和API调用功能，包括视频翻译、状态查询、语种音色获取等核心方法
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Slf4j
@Schema(title = "讯飞羚羊平台API调用类")
public class SoundViewApi {

    // 羚羊平台基础URL
    private static final String LINGYANG_BASE_URL = "https://www.lingyangplat.com/antelope-open";

    // API端点
    private static final String VIDEO_TRANSLATE_CREATE_URL = LINGYANG_BASE_URL + "/open/v1/video/info/create";
    private static final String VIDEO_TRANSLATE_GET_URL = LINGYANG_BASE_URL + "/open/v1/video/info/get";
    private static final String LANGUAGE_LIST_URL = LINGYANG_BASE_URL + "/open/v1/lang/list";
    private static final String VOICE_LIST_URL = LINGYANG_BASE_URL + "/open/v1/timbre/info/list";

    // 固定请求头参数
    private static final String CHANNEL = "app02";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    // 创建 OkHttpClient 实例
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(2, TimeUnit.MINUTES)
            .writeTimeout(2, TimeUnit.MINUTES)
            .connectTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * 创建视频翻译任务
     * 
     * @param request 视频翻译请求参数
     * @return 视频翻译响应结果
     */
    public static VideoTranslateResponseBO createVideoTranslateTask(VideoTranslateRequestBO request) {
        String methodName = "createVideoTranslateTask";
        try {
            log.info("[{}] 开始创建视频翻译任务: {}", methodName, request.getSummary());

            // 获取API密钥
            String[] apiKeys = getLingyangApiKeys();
            if (apiKeys == null) {
                log.error("[{}] 获取羚羊平台API密钥失败", methodName);
                return VideoTranslateResponseBO.failure(100002, "API密钥配置错误");
            }

            String appKey = apiKeys[0];
            String appSecret = apiKeys[1];

            // 参数验证
            if (!request.isValid()) {
                log.error("[{}] 请求参数验证失败", methodName);
                return VideoTranslateResponseBO.failure(100001, "请求参数无效");
            }

            // 设置默认值
            request.setDefaults();

            // 构建请求
            String requestBody = JSON.toJSONString(request);
            String timestamp = generateTimestamp();
            String sign = generateMD5Sign(appSecret, requestBody, timestamp, appKey);

            Request httpRequest = new Request.Builder()
                    .url(VIDEO_TRANSLATE_CREATE_URL)
                    .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                    .addHeader("time", timestamp)
                    .addHeader("appKey", appKey)
                    .addHeader("sign", sign)
                    .addHeader("channel", CHANNEL)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = client.newCall(httpRequest).execute()) {
                log.info("[{}] 视频翻译任务创建请求状态码: {}", methodName, response.code());
                
                if (response.body() == null) {
                    log.error("[{}] 响应体为空", methodName);
                    return VideoTranslateResponseBO.failure(999999, "响应体为空");
                }

                String responseBody = response.body().string();
                log.info("[{}] 羚羊平台响应: {}", methodName, responseBody);

                if (response.isSuccessful()) {
                    VideoTranslateResponseBO result = JSON.parseObject(responseBody, 
                        new TypeReference<VideoTranslateResponseBO>() {});
                    log.info("[{}] 视频翻译任务创建成功: {}", methodName, result.getSummary());
                    return result;
                } else {
                    log.error("[{}] 视频翻译任务创建失败: code={}, body={}", methodName, response.code(), responseBody);
                    return VideoTranslateResponseBO.failure(response.code(), "API调用失败");
                }
            }

        } catch (Exception e) {
            log.error("[{}] 视频翻译任务创建异常: {}", methodName, e.getMessage(), e);
            return VideoTranslateResponseBO.failure(999999, "系统内部错误: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     * 
     * @param request 任务状态查询请求参数
     * @return 任务状态响应结果
     */
    public static TaskStatusResponseBO getTaskStatus(TaskStatusRequestBO request) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 开始查询任务状态: {}", methodName, request.getSummary());

            // 获取API密钥
            String[] apiKeys = getLingyangApiKeys();
            if (apiKeys == null) {
                log.error("[{}] 获取羚羊平台API密钥失败", methodName);
                return TaskStatusResponseBO.failure(100002, "API密钥配置错误");
            }

            String appKey = apiKeys[0];
            String appSecret = apiKeys[1];

            // 参数验证
            if (!request.isValid()) {
                log.error("[{}] 请求参数验证失败", methodName);
                return TaskStatusResponseBO.failure(100001, "请求参数无效");
            }

            // 构建请求
            String requestBody = JSON.toJSONString(request);
            String timestamp = generateTimestamp();
            String sign = generateMD5Sign(appSecret, requestBody, timestamp, appKey);

            Request httpRequest = new Request.Builder()
                    .url(VIDEO_TRANSLATE_GET_URL)
                    .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                    .addHeader("time", timestamp)
                    .addHeader("appKey", appKey)
                    .addHeader("sign", sign)
                    .addHeader("channel", CHANNEL)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = client.newCall(httpRequest).execute()) {
                log.info("[{}] 任务状态查询请求状态码: {}", methodName, response.code());
                
                if (response.body() == null) {
                    log.error("[{}] 响应体为空", methodName);
                    return TaskStatusResponseBO.failure(999999, "响应体为空");
                }

                String responseBody = response.body().string();
                log.debug("[{}] 羚羊平台响应: {}", methodName, responseBody);

                if (response.isSuccessful()) {
                    TaskStatusResponseBO result = JSON.parseObject(responseBody, 
                        new TypeReference<TaskStatusResponseBO>() {});
                    log.info("[{}] 任务状态查询成功: {}", methodName, result.getSummary());
                    return result;
                } else {
                    log.error("[{}] 任务状态查询失败: code={}, body={}", methodName, response.code(), responseBody);
                    return TaskStatusResponseBO.failure(response.code(), "API调用失败");
                }
            }

        } catch (Exception e) {
            log.error("[{}] 任务状态查询异常: {}", methodName, e.getMessage(), e);
            return TaskStatusResponseBO.failure(999999, "系统内部错误: " + e.getMessage());
        }
    }

    /**
     * 获取语种列表
     * 
     * @param request 语种列表查询请求参数
     * @return 语种列表响应结果
     */
    public static LanguageListResponseBO getLanguageList(LanguageListRequestBO request) {
        String methodName = "getLanguageList";
        try {
            log.info("[{}] 开始获取语种列表: {}", methodName, request.getSummary());

            // 获取API密钥
            String[] apiKeys = getLingyangApiKeys();
            if (apiKeys == null) {
                log.error("[{}] 获取羚羊平台API密钥失败", methodName);
                return LanguageListResponseBO.failure(100002, "API密钥配置错误");
            }

            String appKey = apiKeys[0];
            String appSecret = apiKeys[1];

            // 设置默认值
            request.setDefaults();

            // 构建请求
            String requestBody = JSON.toJSONString(request);
            String timestamp = generateTimestamp();
            String sign = generateMD5Sign(appSecret, requestBody, timestamp, appKey);

            Request httpRequest = new Request.Builder()
                    .url(LANGUAGE_LIST_URL)
                    .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                    .addHeader("time", timestamp)
                    .addHeader("appKey", appKey)
                    .addHeader("sign", sign)
                    .addHeader("channel", CHANNEL)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = client.newCall(httpRequest).execute()) {
                log.info("[{}] 语种列表查询请求状态码: {}", methodName, response.code());
                
                if (response.body() == null) {
                    log.error("[{}] 响应体为空", methodName);
                    return LanguageListResponseBO.failure(999999, "响应体为空");
                }

                String responseBody = response.body().string();
                log.debug("[{}] 羚羊平台响应: {}", methodName, responseBody);

                if (response.isSuccessful()) {
                    LanguageListResponseBO result = JSON.parseObject(responseBody, 
                        new TypeReference<LanguageListResponseBO>() {});
                    log.info("[{}] 语种列表获取成功: {}", methodName, result.getSummary());
                    return result;
                } else {
                    log.error("[{}] 语种列表获取失败: code={}, body={}", methodName, response.code(), responseBody);
                    return LanguageListResponseBO.failure(response.code(), "API调用失败");
                }
            }

        } catch (Exception e) {
            log.error("[{}] 语种列表获取异常: {}", methodName, e.getMessage(), e);
            return LanguageListResponseBO.failure(999999, "系统内部错误: " + e.getMessage());
        }
    }

    /**
     * 获取音色列表
     *
     * @param request 音色列表查询请求参数
     * @return 音色列表响应结果
     */
    public static VoiceListResponseBO getVoiceList(VoiceListRequestBO request) {
        String methodName = "getVoiceList";
        try {
            log.info("[{}] 开始获取音色列表: {}", methodName, request.getSummary());

            // 获取API密钥
            String[] apiKeys = getLingyangApiKeys();
            if (apiKeys == null) {
                log.error("[{}] 获取羚羊平台API密钥失败", methodName);
                return VoiceListResponseBO.failure(100002, "API密钥配置错误");
            }

            String appKey = apiKeys[0];
            String appSecret = apiKeys[1];

            // 参数验证
            if (!request.isValid()) {
                log.error("[{}] 请求参数验证失败", methodName);
                return VoiceListResponseBO.failure(100001, "请求参数无效");
            }

            // 设置默认值
            request.setDefaults();

            // 构建请求
            String requestBody = JSON.toJSONString(request);
            String timestamp = generateTimestamp();
            String sign = generateMD5Sign(appSecret, requestBody, timestamp, appKey);

            Request httpRequest = new Request.Builder()
                    .url(VOICE_LIST_URL)
                    .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                    .addHeader("time", timestamp)
                    .addHeader("appKey", appKey)
                    .addHeader("sign", sign)
                    .addHeader("channel", CHANNEL)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = client.newCall(httpRequest).execute()) {
                log.info("[{}] 音色列表查询请求状态码: {}", methodName, response.code());

                if (response.body() == null) {
                    log.error("[{}] 响应体为空", methodName);
                    return VoiceListResponseBO.failure(999999, "响应体为空");
                }

                String responseBody = response.body().string();
                log.debug("[{}] 羚羊平台响应: {}", methodName, responseBody);

                if (response.isSuccessful()) {
                    VoiceListResponseBO result = JSON.parseObject(responseBody,
                        new TypeReference<VoiceListResponseBO>() {});
                    log.info("[{}] 音色列表获取成功: {}", methodName, result.getSummary());
                    return result;
                } else {
                    log.error("[{}] 音色列表获取失败: code={}, body={}", methodName, response.code(), responseBody);
                    return VoiceListResponseBO.failure(response.code(), "API调用失败");
                }
            }

        } catch (Exception e) {
            log.error("[{}] 音色列表获取异常: {}", methodName, e.getMessage(), e);
            return VoiceListResponseBO.failure(999999, "系统内部错误: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取羚羊平台API密钥
     *
     * @return [appKey, appSecret] 数组，获取失败返回null
     */
    private static String[] getLingyangApiKeys() {
        return SoundViewApiUtil.getLingyangApiKeys();
    }

    /**
     * 生成时间戳（14位字符串，yyyyMMddHHmmss）
     *
     * @return 时间戳字符串
     */
    private static String generateTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    /**
     * 生成MD5签名
     * 签名算法：md5(appSecret + "_" + body + "_" + time + "_" + appKey + "_" + appSecret)
     *
     * @param appSecret 应用密钥
     * @param body 请求体JSON字符串
     * @param timestamp 时间戳
     * @param appKey 应用Key
     * @return MD5签名（大写）
     */
    private static String generateMD5Sign(String appSecret, String body, String timestamp, String appKey) {
        String signString = String.join("_", appSecret, body, timestamp, appKey, appSecret);
        return SecureUtil.md5(signString).toUpperCase();
    }

    // ==================== 便捷方法 ====================

    /**
     * 创建视频翻译任务（便捷方法）
     *
     * @param videoUrl 视频URL
     * @param originalLang 原语种
     * @param destLang 目标语种
     * @param timbre 音色ID
     * @param videoName 视频名称
     * @return 视频翻译响应结果
     */
    public static VideoTranslateResponseBO createVideoTranslateTask(String videoUrl, String originalLang,
                                                                   String destLang, String timbre, String videoName) {
        VideoTranslateRequestBO request = new VideoTranslateRequestBO();
        request.setFileUrl(videoUrl);
        request.setOriginalLang(originalLang);
        request.setDestLang(destLang);
        request.setTimbre(timbre);
        request.setVideoName(videoName);
        request.setFormat("mp4");
        request.setSize("0");
        request.setTimeSpan("00:00:00");

        return createVideoTranslateTask(request);
    }

    /**
     * 查询任务状态（便捷方法）
     *
     * @param taskId 任务ID
     * @return 任务状态响应结果
     */
    public static TaskStatusResponseBO getTaskStatus(Long taskId) {
        TaskStatusRequestBO request = new TaskStatusRequestBO(taskId);
        return getTaskStatus(request);
    }

    /**
     * 查询任务状态（便捷方法）
     *
     * @param taskId 任务ID字符串
     * @return 任务状态响应结果
     */
    public static TaskStatusResponseBO getTaskStatus(String taskId) {
        TaskStatusRequestBO request = new TaskStatusRequestBO(taskId);
        return getTaskStatus(request);
    }

    /**
     * 获取语种列表（便捷方法）
     *
     * @return 语种列表响应结果
     */
    public static LanguageListResponseBO getLanguageList() {
        LanguageListRequestBO request = new LanguageListRequestBO();
        return getLanguageList(request);
    }

    /**
     * 获取音色列表（便捷方法）
     *
     * @param lang 语种代码
     * @return 音色列表响应结果
     */
    public static VoiceListResponseBO getVoiceList(String lang) {
        VoiceListRequestBO request = new VoiceListRequestBO();
        return getVoiceList(request);
    }

    /**
     * 获取音色列表（便捷方法，带筛选）
     *
     * @param lang 语种代码
     * @param gender 性别筛选（1:女 2:男）
     * @return 音色列表响应结果
     */
    public static VoiceListResponseBO getVoiceList(String lang, Integer gender) {
        VoiceListRequestBO request = new VoiceListRequestBO();
        return getVoiceList(request);
    }

    // ==================== 配置检查方法 ====================

    /**
     * 检查API配置是否有效
     *
     * @return 配置是否有效
     */
    public static boolean isConfigValid() {
        return SoundViewApiUtil.isConfigValid();
    }

    /**
     * 获取API配置信息
     *
     * @return 配置信息描述
     */
    public static String getConfigInfo() {
        return SoundViewApiUtil.getConfigInfo();
    }
}
