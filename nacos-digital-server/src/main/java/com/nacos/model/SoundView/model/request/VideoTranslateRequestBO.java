package com.nacos.model.SoundView.model.request;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 羚羊平台视频翻译请求模型
 * 对应API: /open/v1/video/info/create
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "视频翻译请求参数")
public class VideoTranslateRequestBO {

    /**
     * 原视频URL地址
     */
    @Schema(description = "原视频URL地址", required = true, example = "https://example.com/video.mp4")
    @JSONField(name = "fileUrl")
    @NotBlank(message = "视频URL不能为空")
    private String fileUrl;

    /**
     * 原语种
     */
    @Schema(description = "原语种", required = true, example = "cn")
    @JSONField(name = "originalLang")
    @NotBlank(message = "原语种不能为空")
    @Pattern(regexp = "^(cn|en|ja|ko|fr|de|es|ru|it|pt|ar|th|vi|hi|ms)$", message = "不支持的原语种")
    private String originalLang;

    /**
     * 目标语种
     */
    @Schema(description = "目标语种", required = true, example = "en")
    @JSONField(name = "destLang")
    @NotBlank(message = "目标语种不能为空")
    @Pattern(regexp = "^(cn|en|ja|ko|fr|de|es|ru|it|pt|ar|th|vi|hi|ms)$", message = "不支持的目标语种")
    private String destLang;

    /**
     * 音色ID
     */
    @Schema(description = "音色ID", required = true, example = "246")
    @JSONField(name = "timbre")
    @NotBlank(message = "音色ID不能为空")
    private String timbre;

    /**
     * 视频名称
     */
    @Schema(description = "视频名称", required = true, example = "测试视频")
    @JSONField(name = "videoName")
    @NotBlank(message = "视频名称不能为空")
    @Size(max = 100, message = "视频名称不能超过100个字符")
    private String videoName;

    /**
     * 视频格式
     */
    @Schema(description = "视频格式", required = true, example = "mp4")
    @JSONField(name = "format")
    @NotBlank(message = "视频格式不能为空")
    @Pattern(regexp = "^(mp4|avi|mov|wmv|flv|mkv)$", message = "不支持的视频格式")
    private String format;

    /**
     * 视频大小（字节）
     */
    @Schema(description = "视频大小（字节）", required = true, example = "1024000")
    @JSONField(name = "size")
    @NotBlank(message = "视频大小不能为空")
    private String size;

    /**
     * 视频时长
     */
    @Schema(description = "视频时长", required = true, example = "00:01:30")
    @JSONField(name = "timeSpan")
    @NotBlank(message = "视频时长不能为空")
    @Pattern(regexp = "^\\d{2}:\\d{2}:\\d{2}$", message = "视频时长格式错误，应为HH:mm:ss")
    private String timeSpan;

    /**
     * 选择模型（可选）
     */
    @Schema(description = "选择模型", required = false, example = "1")
    @JSONField(name = "llmModel")
    private Integer llmModel = 1;

    /**
     * 背景音乐选择（可选）
     */
    @Schema(description = "背景音乐选择", required = false, example = "1")
    @JSONField(name = "bgmOperateType")
    private Integer bgmOperateType = 1;

    // ==================== 业务方法 ====================

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return fileUrl != null && !fileUrl.trim().isEmpty() &&
               originalLang != null && !originalLang.trim().isEmpty() &&
               destLang != null && !destLang.trim().isEmpty() &&
               timbre != null && !timbre.trim().isEmpty() &&
               videoName != null && !videoName.trim().isEmpty() &&
               format != null && !format.trim().isEmpty() &&
               size != null && !size.trim().isEmpty() &&
               timeSpan != null && !timeSpan.trim().isEmpty();
    }

    /**
     * 检查是否为相同语种
     */
    public boolean isSameLanguage() {
        return originalLang != null && originalLang.equals(destLang);
    }

    /**
     * 获取语言对描述
     */
    public String getLanguagePairDescription() {
        return originalLang + " → " + destLang;
    }

    /**
     * 获取视频大小（字节数值）
     */
    public Long getVideoSizeBytes() {
        try {
            return Long.parseLong(size);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取视频时长（秒）
     */
    public Integer getVideoDurationSeconds() {
        if (timeSpan == null || !timeSpan.matches("^\\d{2}:\\d{2}:\\d{2}$")) {
            return null;
        }
        
        String[] parts = timeSpan.split(":");
        try {
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            return hours * 3600 + minutes * 60 + seconds;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取音色ID（数值）
     */
    public Long getTimbreId() {
        try {
            return Long.parseLong(timbre);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (llmModel == null) {
            llmModel = 1;
        }
        if (bgmOperateType == null) {
            bgmOperateType = 1;
        }
        if (format == null || format.trim().isEmpty()) {
            format = "mp4";
        }
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileUrl == null) {
            return null;
        }
        
        int lastDotIndex = fileUrl.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileUrl.length() - 1) {
            return fileUrl.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return format != null ? format.toLowerCase() : null;
    }

    /**
     * 检查视频大小是否在合理范围内
     */
    public boolean isVideoSizeValid() {
        Long sizeBytes = getVideoSizeBytes();
        if (sizeBytes == null) {
            return false;
        }
        
        // 视频大小应在1KB到5GB之间
        return sizeBytes >= 1024 && sizeBytes <= 5L * 1024 * 1024 * 1024;
    }

    /**
     * 检查视频时长是否在合理范围内
     */
    public boolean isVideoDurationValid() {
        Integer durationSeconds = getVideoDurationSeconds();
        if (durationSeconds == null) {
            return false;
        }
        
        // 视频时长应在1秒到2小时之间
        return durationSeconds >= 1 && durationSeconds <= 7200;
    }

    /**
     * 获取请求摘要信息
     */
    public String getSummary() {
        return String.format("视频翻译: %s (%s) %s, 时长: %s, 大小: %s", 
            videoName, getLanguagePairDescription(), format, timeSpan, size);
    }
}
