package com.nacos.model.SoundView;

import com.business.utils.BThirdPartyKey;
import com.nacos.enums.DictConfigEnum;
import com.nacos.model.SoundView.model.request.LanguageListRequestBO;
import com.nacos.model.SoundView.model.request.VoiceListRequestBO;
import com.nacos.model.SoundView.model.request.VideoTranslateRequestBO;
import com.nacos.model.SoundView.model.response.LanguageListResponseBO;
import com.nacos.model.SoundView.model.response.VoiceListResponseBO;
import com.nacos.model.SoundView.model.response.VideoTranslateResponseBO;
import com.nacos.model.SoundView.model.response.TaskStatusResponseBO;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 羚羊平台API工具类
 * 提供羚羊平台API的便捷调用方法，包括语种列表、音色列表等功能
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Slf4j
@Schema(title = "羚羊平台API工具类")
public class SoundViewApiUtil {

    public static final String STATUS_FAIL = "fail";
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_PROCESSING = "processing";

    /**
     * 获取羚羊平台API密钥
     *
     * @return [AppKey, AppSecret] 数组，获取失败返回null
     */
    public static String[] getLingyangApiKeys() {
        try {
            HashMap<Long, String> dictConfigMap = BThirdPartyKey
                    .getSecretKeyInfo(DictConfigEnum.XUNFEI_LINGYANG_APP_KEY.getDictType());
            if (dictConfigMap == null || dictConfigMap.isEmpty()) {
                log.error("羚羊平台API配置错误：无法获取到有效的配置信息");
                return null;
            }

            String appKey = dictConfigMap.get(DictConfigEnum.XUNFEI_LINGYANG_APP_KEY.getDictKey());
            String appSecret = dictConfigMap.get(DictConfigEnum.XUNFEI_LINGYANG_APP_SECRET.getDictKey());

            if (!StringUtils.hasText(appKey)) {
                log.error("羚羊平台API配置错误：AppKey为空");
                return null;
            }

            if (!StringUtils.hasText(appSecret)) {
                log.error("羚羊平台API配置错误：AppSecret为空");
                return null;
            }

            return new String[]{appKey, appSecret};
        } catch (Exception e) {
            log.error("获取羚羊平台API密钥失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据羚羊平台API错误码返回对应的错误描述
     * 参考官方文档提供用户友好的错误信息和解决建议
     *
     * @param errorCode 错误码
     * @return 错误描述
     */
    public static String getErrorMessageByCode(String errorCode) {
        if (errorCode == null) {
            return "未知错误";
        }

        return switch (errorCode) {
            case "0" -> "接口调用成功";
            case "100001" -> "参数错误，请参考API文档检查参数类型、是否必填等";
            case "100002" -> "应用不合法，请检查分配的应用appKey是否正确";
            case "100003" -> "time时间无效，请确保传入的time值和服务端时间误差小于30分钟";
            case "100004" -> "token无效，Token过期或无效，请重新获取token";
            case "100005" -> "Sign签名无效，请检查加密方法，检查appKey和appSecret是否匹配";
            case "200005" -> "解密异常，请检查加密方式";
            case "999999" -> "系统内部发生错误，请优先检查所传参数";
            default -> "未知错误，错误码: " + errorCode + "，请联系技术支持";
        };
    }

    /**
     * 获取支持的语种列表（便捷方法）
     *
     * @return 语种列表结果，包装为Result对象
     */
    public static Result<List<LanguageListResponseBO.LanguageListResult.LanguageInfo>> getLanguageListWithResult() {
        String methodName = "getLanguageListWithResult";
        try {
            log.info("[{}] 获取羚羊平台支持的语种列表", methodName);

            // 检查API配置
            if (!isConfigValid()) {
                log.error("[{}] 羚羊平台API配置错误，请联系管理员", methodName);
                return Result.ERROR("羚羊平台API配置错误，请联系管理员");
            }

            LanguageListRequestBO request = new LanguageListRequestBO();
            LanguageListResponseBO response = SoundViewApi.getLanguageList(request);

            if (response == null || !response.isSuccess()) {
                String errorMsg = response != null ? response.getErrorMessage() : "API调用失败";
                String userFriendlyMsg = getErrorMessageByCode(response != null ? String.valueOf(response.getCode()) : null);
                log.error("[{}] 获取语种列表失败: {}", methodName, errorMsg);
                return Result.ERROR("获取语种列表失败: " + userFriendlyMsg);
            }

            log.info("[{}] 获取语种列表成功，共{}种语言", methodName, response.getLanguageList().size());
            return Result.SUCCESS(response.getLanguageList());

        } catch (Exception e) {
            log.error("[{}] 获取语种列表异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("获取语种列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定语种的音色列表（便捷方法）
     *
     * @param languageCodes 语种代码数组，为空时获取所有语种的音色
     * @param offset 偏移量，默认0
     * @param limit 每页数量，默认10
     * @return 音色列表结果，包装为Result对象
     */
    public static Result<List<VoiceListResponseBO.VoiceInfo>> getVoiceListWithResult(String[] languageCodes, Integer offset, Integer limit) {
        String methodName = "getVoiceListWithResult";
        try {
            log.info("[{}] 获取音色列表: languages={}, offset={}, limit={}",
                methodName, languageCodes != null ? String.join(",", languageCodes) : "all", offset, limit);

            // 检查API配置
            if (!isConfigValid()) {
                log.error("[{}] 羚羊平台API配置错误，请联系管理员", methodName);
                return Result.ERROR("羚羊平台API配置错误，请联系管理员");
            }

            VoiceListRequestBO request = new VoiceListRequestBO();
            request.setLangList(languageCodes);
            request.setOffset(offset != null ? offset : 0);
            request.setLimit(limit != null ? limit : 10);

            VoiceListResponseBO response = SoundViewApi.getVoiceList(request);

            if (response == null || !response.isSuccess()) {
                String errorMsg = response != null ? response.getErrorMessage() : "API调用失败";
                String userFriendlyMsg = getErrorMessageByCode(response != null ? String.valueOf(response.getCode()) : null);
                log.error("[{}] 获取音色列表失败: {}", methodName, errorMsg);
                return Result.ERROR("获取音色列表失败: " + userFriendlyMsg);
            }

            log.info("[{}] 获取音色列表成功，共{}个音色", methodName,
                response.getVoiceList().size());
            return Result.SUCCESS(response.getVoiceList());

        } catch (Exception e) {
            log.error("[{}] 获取音色列表异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("获取音色列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定语种的音色列表（便捷方法）
     *
     * @param languageCode 语种代码
     * @return 音色列表结果
     */
    public static Result<List<VoiceListResponseBO.VoiceInfo>> getVoiceListWithResult(String languageCode) {
        return getVoiceListWithResult(new String[]{languageCode}, 0, 10);
    }

    /**
     * 获取所有语种的音色列表（便捷方法）
     *
     * @return 音色列表结果
     */
    public static Result<List<VoiceListResponseBO.VoiceInfo>> getAllVoiceListWithResult() {
        return getVoiceListWithResult(null, 0, 50);
    }

    /**
     * 检查羚羊平台API配置是否有效
     *
     * @return 配置检查结果
     */
    public static Result<Map<String, Object>> checkApiConfig() {
        String methodName = "checkApiConfig";
        try {
            log.info("[{}] 检查羚羊平台API配置", methodName);
            Map<String, Object> result = new HashMap<>();

            // 检查API密钥配置
            String[] apiKeys = getLingyangApiKeys();
            boolean configValid = apiKeys != null;
            result.put("apiConfigValid", configValid);

            if (configValid && apiKeys != null) {
                result.put("appKeyMasked", maskApiKey(apiKeys[0]));
                result.put("configInfo", "羚羊平台API配置有效");
            } else {
                result.put("configInfo", "羚羊平台API配置无效");
            }

            // 测试API连通性
            if (configValid) {
                try {
                    Result<List<LanguageListResponseBO.LanguageListResult.LanguageInfo>> testResult = getLanguageListWithResult();
                    result.put("apiConnectivity", testResult.isSuccess());
                    result.put("connectivityInfo", testResult.isSuccess() ? "API连接正常" : "API连接失败");
                } catch (Exception e) {
                    result.put("apiConnectivity", false);
                    result.put("connectivityInfo", "API连接测试异常: " + e.getMessage());
                }
            } else {
                result.put("apiConnectivity", false);
                result.put("connectivityInfo", "配置无效，无法测试连接");
            }

            boolean overallStatus = configValid && (Boolean) result.get("apiConnectivity");
            result.put("overallStatus", overallStatus);

            log.info("[{}] API配置检查完成: {}", methodName, overallStatus ? "正常" : "异常");
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 检查API配置异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("配置检查失败: " + e.getMessage());
        }
    }

    /**
     * 掩码API密钥，用于日志显示
     *
     * @param apiKey 原始API密钥
     * @return 掩码后的密钥
     */
    public static String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 检查API配置是否有效
     *
     * @return 配置是否有效
     */
    public static boolean isConfigValid() {
        String[] apiKeys = getLingyangApiKeys();
        return apiKeys != null && apiKeys.length == 2 &&
               StringUtils.hasText(apiKeys[0]) && StringUtils.hasText(apiKeys[1]);
    }

    /**
     * 获取API配置信息
     *
     * @return 配置信息描述
     */
    public static String getConfigInfo() {
        String[] apiKeys = getLingyangApiKeys();
        if (apiKeys == null) {
            return "羚羊平台API配置无效";
        }

        String appKey = apiKeys[0];
        String maskedAppKey = maskApiKey(appKey);

        return String.format("羚羊平台API配置有效 (AppKey: %s)", maskedAppKey);
    }

    // ==================== 视频翻译任务业务方法 ====================

    /**
     * 提交视频翻译任务
     *
     * @param videoUrl 视频文件URL
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @param voiceId 音色ID
     * @param taskName 任务名称
     * @param userId 用户ID
     * @return 任务提交结果，包含任务ID等信息
     */
    public static Result<Map<String, Object>> submitVideoTranslation(
            String videoUrl, String sourceLanguage, String targetLanguage,
            String voiceId, String taskName, String userId) {
        String methodName = "submitVideoTranslation";
        try {
            log.info("[{}] 提交视频翻译任务: userId={}, videoUrl={}, {}→{}, voiceId={}",
                methodName, userId, videoUrl, sourceLanguage, targetLanguage, voiceId);

            // 检查API配置
            if (!isConfigValid()) {
                log.error("[{}] 羚羊平台API配置错误，请联系管理员", methodName);
                return Result.ERROR("羚羊平台API配置错误，请联系管理员");
            }

            // 构建请求参数
            VideoTranslateRequestBO request = new VideoTranslateRequestBO();
            request.setFileUrl(videoUrl);
            request.setOriginalLang(sourceLanguage);
            request.setDestLang(targetLanguage);
            request.setTimbre(voiceId);
            request.setVideoName(taskName);

            // 设置默认值（这些参数需要从视频文件中获取或设置默认值）
            request.setFormat("mp4");
            request.setSize("0");
            request.setTimeSpan("00:00:00");
            request.setDefaults();

            // 调用羚羊平台API
            VideoTranslateResponseBO response = SoundViewApi.createVideoTranslateTask(request);

            if (response == null || !response.isSuccess()) {
                String errorMsg = response != null ? response.getErrorMessage() : "API调用失败";
                String userFriendlyMsg = getErrorMessageByCode(response != null ? String.valueOf(response.getCode()) : null);
                log.error("[{}] 提交视频翻译任务失败: userId={}, error={}", methodName, userId, errorMsg);
                return Result.ERROR("提交翻译任务失败: " + userFriendlyMsg);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", response.getTaskId());
            result.put("lingyangTaskId", response.getTaskId());
            result.put("taskName", taskName);
            result.put("status", "submitted");
            result.put("statusDesc", "任务已提交");
            result.put("submitTime", System.currentTimeMillis());

            log.info("[{}] 视频翻译任务提交成功: userId={}, taskId={}", methodName, userId, response.getTaskId());
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 提交视频翻译任务异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("提交翻译任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    public static Result<Map<String, Object>> getTaskStatus(String taskId) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 查询任务状态: taskId={}", methodName, taskId);

            // 检查API配置
            if (!isConfigValid()) {
                log.error("[{}] 羚羊平台API配置错误，请联系管理员", methodName);
                return Result.ERROR("羚羊平台API配置错误，请联系管理员");
            }

            // 调用羚羊平台API
            TaskStatusResponseBO response = SoundViewApi.getTaskStatus(taskId);

            if (response == null || !response.isSuccess()) {
                String errorMsg = response != null ? response.getErrorMessage() : "API调用失败";
                String userFriendlyMsg = getErrorMessageByCode(response != null ? String.valueOf(response.getCode()) : null);
                log.error("[{}] 查询任务状态失败: taskId={}, error={}", methodName, taskId, errorMsg);
                return Result.ERROR("查询任务状态失败: " + userFriendlyMsg);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("lingyangTaskId", taskId);
            result.put("status", response.getTaskStatus());
            result.put("statusDesc", response.getResult() != null ? response.getResult().getStatusDescription() : "未知状态");
            result.put("progress", response.getProgress());
            result.put("resultVideoUrl", response.getVideoResultUrl());
            result.put("subtitleUrl", response.getSubtitleResultUrl());
            result.put("coverUrl", response.getResult() != null ? response.getResult().getCoverUrl() : null);
            result.put("originalText", response.getOriginalText());
            result.put("translatedText", response.getTranslatedText());
            result.put("errorMessage", response.getErrorMessage());

            // 设置状态标识
            result.put("isCompleted", response.isTaskCompleted());
            result.put("isFailed", response.isTaskFailed());
            result.put("isProcessing", response.isTaskProcessing());

            log.info("[{}] 查询任务状态成功: taskId={}, status={}", methodName, taskId, response.getTaskStatus());
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消翻译任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 取消结果
     */
    public static Result<String> cancelTranslationTask(String taskId, String userId) {
        String methodName = "cancelTranslationTask";
        try {
            log.info("[{}] 取消翻译任务: taskId={}, userId={}", methodName, taskId, userId);

            // 检查API配置
            if (!isConfigValid()) {
                log.error("[{}] 羚羊平台API配置错误，请联系管理员", methodName);
                return Result.ERROR("羚羊平台API配置错误，请联系管理员");
            }

            // 先查询任务状态，确认任务存在且可以取消
            Result<Map<String, Object>> statusResult = getTaskStatus(taskId);
            if (!statusResult.isSuccess()) {
                log.error("[{}] 查询任务状态失败，无法取消: taskId={}", methodName, taskId);
                return Result.ERROR("查询任务状态失败，无法取消任务");
            }

            Map<String, Object> statusData = statusResult.getData();
            Boolean isCompleted = (Boolean) statusData.get("isCompleted");
            Boolean isFailed = (Boolean) statusData.get("isFailed");

            if (Boolean.TRUE.equals(isCompleted)) {
                log.warn("[{}] 任务已完成，无法取消: taskId={}", methodName, taskId);
                return Result.ERROR("任务已完成，无法取消");
            }

            if (Boolean.TRUE.equals(isFailed)) {
                log.warn("[{}] 任务已失败，无需取消: taskId={}", methodName, taskId);
                return Result.ERROR("任务已失败，无需取消");
            }

            // 注意：羚羊平台可能没有直接的取消API，这里返回成功表示标记为取消
            // 实际项目中需要根据平台API文档实现具体的取消逻辑
            log.info("[{}] 翻译任务取消成功: taskId={}", methodName, taskId);
            return Result.SUCCESS("任务取消成功");

        } catch (Exception e) {
            log.error("[{}] 取消翻译任务异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查系统环境
     *
     * @return 系统环境检查结果
     */
    public static Result<Map<String, Object>> checkSystemEnvironment() {
        String methodName = "checkSystemEnvironment";
        try {
            log.info("[{}] 检查系统环境", methodName);

            Map<String, Object> result = new HashMap<>();

            // 检查API配置
            Result<Map<String, Object>> configResult = checkApiConfig();
            if (configResult.isSuccess()) {
                result.putAll(configResult.getData());
            } else {
                result.put("apiConfigValid", false);
                result.put("configInfo", "API配置检查失败");
                result.put("apiConnectivity", false);
                result.put("connectivityInfo", "配置无效");
            }

            // 添加系统信息
            result.put("systemTime", System.currentTimeMillis());
            result.put("javaVersion", System.getProperty("java.version"));
            result.put("osName", System.getProperty("os.name"));
            result.put("availableProcessors", Runtime.getRuntime().availableProcessors());
            result.put("maxMemory", Runtime.getRuntime().maxMemory());
            result.put("freeMemory", Runtime.getRuntime().freeMemory());

            boolean overallHealth = (Boolean) result.getOrDefault("overallStatus", false);

            log.info("[{}] 系统环境检查完成: {}", methodName, overallHealth ? "正常" : "异常");
            return overallHealth ? Result.SUCCESS(result) : Result.ERROR("系统环境异常", result);

        } catch (Exception e) {
            log.error("[{}] 检查系统环境异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("系统环境检查失败: " + e.getMessage());
        }
    }
}
