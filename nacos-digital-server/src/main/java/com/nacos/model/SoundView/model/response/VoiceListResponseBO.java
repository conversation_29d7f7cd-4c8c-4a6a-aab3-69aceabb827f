package com.nacos.model.SoundView.model.response;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 羚羊平台音色列表查询响应模型
 * 对应API: /open/v1/voice/list
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "音色列表查询响应结果")
public class VoiceListResponseBO {

    /**
     * 返回码
     */
    @Schema(description = "返回码", example = "0")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "SUCCESS")
    @JSONField(name = "message")
    private String message;

    /**
     * 下次分页的起始位置
     */
    @Schema(description = "下次分页的起始位置", example = "20")
    @JSONField(name = "offset")
    private Integer offset;

    /**
     * 返回结果
     */
    @Schema(description = "返回结果")
    @JSONField(name = "result")
    private List<VoiceInfo> result;

    /**
     * 音色信息内部类
     */
    @Data
    @Schema(description = "音色信息")
    public static class VoiceInfo {

        /**
         * 音色ID
         */
        @Schema(description = "音色ID", example = "246")
        @JSONField(name = "id")
        private String id;

        /**
         * 音色名称
         */
        @Schema(description = "音色名称", example = "晓晓")
        @JSONField(name = "name")
        private String name;

        /**
         * 语种
         */
        @Schema(description = "语种", example = "cn")
        @JSONField(name = "lang")
        private String lang;

        /**
         * 性别代码
         */
        @Schema(description = "性别代码", example = "1")
        @JSONField(name = "gender")
        private Integer gender;

        /**
         * 性别描述
         */
        @Schema(description = "性别描述", example = "女")
        @JSONField(name = "genderStr")
        private String genderStr;

        /**
         * 口音代码
         */
        @Schema(description = "口音代码", example = "1")
        @JSONField(name = "accent")
        private Integer accent;

        /**
         * 口音描述
         */
        @Schema(description = "口音描述", example = "标准")
        @JSONField(name = "accentStr")
        private String accentStr;

        /**
         * 年龄段代码
         */
        @Schema(description = "年龄段代码", example = "2")
        @JSONField(name = "ageRange")
        private Integer ageRange;

        /**
         * 年龄段描述
         */
        @Schema(description = "年龄段描述", example = "青年")
        @JSONField(name = "ageRangeStr")
        private String ageRangeStr;

        /**
         * 情绪代码
         */
        @Schema(description = "情绪代码", example = "1")
        @JSONField(name = "emotion")
        private Integer emotion;

        /**
         * 情绪描述
         */
        @Schema(description = "情绪描述", example = "温柔")
        @JSONField(name = "emotionStr")
        private String emotionStr;

        /**
         * 品类代码列表
         */
        @Schema(description = "品类代码列表")
        @JSONField(name = "categoryList")
        private List<Integer> categoryList;

        /**
         * 品类描述列表
         */
        @Schema(description = "品类描述列表")
        @JSONField(name = "categoryStrListStr")
        private List<String> categoryStrListStr;

        // ==================== 业务方法 ====================

        /**
         * 获取音色ID（数值）
         */
        public Long getVoiceIdLong() {
            try {
                return id != null ? Long.parseLong(id) : null;
            } catch (NumberFormatException e) {
                return null;
            }
        }

        /**
         * 检查是否为女声
         */
        public boolean isFemale() {
            return gender != null && gender == 1;
        }

        /**
         * 检查是否为男声
         */
        public boolean isMale() {
            return gender != null && gender == 2;
        }

        /**
         * 获取性别描述
         */
        public String getGenderDescription() {
            if (genderStr != null && !genderStr.trim().isEmpty()) {
                return genderStr;
            }
            if (gender != null) {
                switch (gender) {
                    case 1: return "女";
                    case 2: return "男";
                    default: return "未知";
                }
            }
            return "未知";
        }

        /**
         * 获取年龄段描述
         */
        public String getAgeDescription() {
            if (ageRangeStr != null && !ageRangeStr.trim().isEmpty()) {
                return ageRangeStr;
            }
            if (ageRange != null) {
                switch (ageRange) {
                    case 1: return "少年";
                    case 2: return "青年";
                    case 3: return "中年";
                    default: return "未知";
                }
            }
            return "未知";
        }

        /**
         * 获取口音描述
         */
        public String getAccentDescription() {
            return accentStr != null && !accentStr.trim().isEmpty() ? accentStr : "标准";
        }

        /**
         * 获取情绪描述
         */
        public String getEmotionDescription() {
            return emotionStr != null && !emotionStr.trim().isEmpty() ? emotionStr : "自然";
        }

        /**
         * 获取完整描述
         */
        public String getFullDescription() {
            StringBuilder sb = new StringBuilder();
            sb.append(name != null ? name : "未知音色");
            
            sb.append(" (").append(getGenderDescription());
            sb.append("·").append(getAgeDescription());
            
            if (!"标准".equals(getAccentDescription())) {
                sb.append("·").append(getAccentDescription());
            }
            
            if (!"自然".equals(getEmotionDescription())) {
                sb.append("·").append(getEmotionDescription());
            }
            
            sb.append(")");
            
            return sb.toString();
        }

        /**
         * 获取品类描述
         */
        public String getCategoryDescription() {
            if (categoryStrListStr != null && !categoryStrListStr.isEmpty()) {
                return String.join("、", categoryStrListStr);
            }
            return "通用";
        }

        /**
         * 检查是否匹配筛选条件
         */
        public boolean matchesFilter(Integer genderFilter, Integer ageRangeFilter, Integer accentFilter) {
            if (genderFilter != null && !genderFilter.equals(gender)) {
                return false;
            }
            if (ageRangeFilter != null && !ageRangeFilter.equals(ageRange)) {
                return false;
            }
            if (accentFilter != null && !accentFilter.equals(accent)) {
                return false;
            }
            return true;
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return code == null || code != 0;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "未知错误";
    }

    /**
     * 获取音色列表
     */
    public List<VoiceInfo> getVoiceList() {
        return result;
    }

    /**
     * 获取音色数量
     */
    public int getVoiceCount() {
        return result != null ? result.size() : 0;
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return isSuccess() && result != null && !result.isEmpty();
    }

    /**
     * 根据ID查找音色
     */
    public VoiceInfo findVoiceById(String voiceId) {
        if (result == null || voiceId == null) {
            return null;
        }
        return result.stream()
                .filter(voice -> voiceId.equals(voice.getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据性别筛选音色
     */
    public List<VoiceInfo> filterByGender(Integer gender) {
        if (result == null || gender == null) {
            return result;
        }
        return result.stream()
                .filter(voice -> gender.equals(voice.getGender()))
                .toList();
    }

    /**
     * 获取女声音色列表
     */
    public List<VoiceInfo> getFemaleVoices() {
        return filterByGender(1);
    }

    /**
     * 获取男声音色列表
     */
    public List<VoiceInfo> getMaleVoices() {
        return filterByGender(2);
    }

    /**
     * 获取默认音色（第一个音色）
     */
    public VoiceInfo getDefaultVoice() {
        return hasData() ? result.get(0) : null;
    }

    /**
     * 检查是否还有更多数据
     */
    public boolean hasMoreData() {
        return offset != null && offset > 0;
    }

    /**
     * 获取下一页偏移量
     */
    public Integer getNextOffset() {
        return offset;
    }

    /**
     * 获取响应摘要信息
     */
    public String getSummary() {
        if (isSuccess()) {
            return String.format("音色列表查询成功: 共%d个音色", getVoiceCount());
        } else {
            return String.format("音色列表查询失败: code=%d, message=%s", code, message);
        }
    }

    /**
     * 创建成功响应
     */
    public static VoiceListResponseBO success(List<VoiceInfo> voices, Integer nextOffset) {
        VoiceListResponseBO response = new VoiceListResponseBO();
        response.setCode(0);
        response.setMessage("SUCCESS");
        response.setResult(voices);
        response.setOffset(nextOffset);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VoiceListResponseBO failure(Integer code, String message) {
        VoiceListResponseBO response = new VoiceListResponseBO();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    @Override
    public String toString() {
        return "VoiceListResponseBO{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", offset=" + offset +
                ", result=" + (result != null ? result.size() + " voices" : "null") +
                '}';
    }
}
