package com.nacos.model.SoundView.model.response;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 羚羊平台语种列表查询响应模型
 * 对应API: /open/v1/language/list
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "语种列表查询响应结果")
public class LanguageListResponseBO {

    /**
     * 返回码
     */
    @Schema(description = "返回码", example = "0")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "SUCCESS")
    @JSONField(name = "message")
    private String message;

    /**
     * 返回结果
     */
    @Schema(description = "返回结果")
    @JSONField(name = "result")
    private LanguageListResult result;

    /**
     * 语种列表结果内部类
     */
    @Data
    @Schema(description = "语种列表结果详情")
    public static class LanguageListResult {

        /**
         * 总数量
         */
        @Schema(description = "总数量", example = "15")
        @JSONField(name = "total")
        private Integer total;

        /**
         * 语种列表
         */
        @Schema(description = "语种列表")
        @JSONField(name = "list")
        private List<LanguageInfo> list;

        /**
         * 语种信息内部类
         */
        @Data
        @Schema(description = "语种信息")
        public static class LanguageInfo {

            /**
             * 语种代码
             */
            @Schema(description = "语种代码", example = "cn")
            @JSONField(name = "code")
            private String code;

            /**
             * 语种名称
             */
            @Schema(description = "语种名称", example = "中文")
            @JSONField(name = "name")
            private String name;

            /**
             * 英文名称
             */
            @Schema(description = "英文名称", example = "Chinese")
            @JSONField(name = "englishName")
            private String englishName;

            /**
             * 是否支持语音识别
             */
            @Schema(description = "是否支持语音识别", example = "true")
            @JSONField(name = "supportSpeechRecognition")
            private Boolean supportSpeechRecognition;

            /**
             * 是否支持机器翻译
             */
            @Schema(description = "是否支持机器翻译", example = "true")
            @JSONField(name = "supportTranslation")
            private Boolean supportTranslation;

            /**
             * 是否支持语音合成
             */
            @Schema(description = "是否支持语音合成", example = "true")
            @JSONField(name = "supportSpeechSynthesis")
            private Boolean supportSpeechSynthesis;

            /**
             * 排序权重
             */
            @Schema(description = "排序权重", example = "100")
            @JSONField(name = "sortOrder")
            private Integer sortOrder;

            // ==================== 业务方法 ====================

            /**
             * 检查是否支持完整的视频翻译功能
             */
            public boolean supportFullVideoTranslation() {
                return Boolean.TRUE.equals(supportSpeechRecognition) &&
                       Boolean.TRUE.equals(supportTranslation) &&
                       Boolean.TRUE.equals(supportSpeechSynthesis);
            }

            /**
             * 获取显示名称（优先中文名称）
             */
            public String getDisplayName() {
                if (name != null && !name.trim().isEmpty()) {
                    return name;
                } else if (englishName != null && !englishName.trim().isEmpty()) {
                    return englishName;
                } else {
                    return code;
                }
            }

            /**
             * 获取支持功能描述
             */
            public String getSupportedFeaturesDescription() {
                StringBuilder sb = new StringBuilder();
                
                if (Boolean.TRUE.equals(supportSpeechRecognition)) {
                    sb.append("语音识别");
                }
                
                if (Boolean.TRUE.equals(supportTranslation)) {
                    if (sb.length() > 0) sb.append("、");
                    sb.append("机器翻译");
                }
                
                if (Boolean.TRUE.equals(supportSpeechSynthesis)) {
                    if (sb.length() > 0) sb.append("、");
                    sb.append("语音合成");
                }
                
                return sb.length() > 0 ? sb.toString() : "无";
            }
        }

        // ==================== 业务方法 ====================

        /**
         * 检查是否有数据
         */
        public boolean hasData() {
            return list != null && !list.isEmpty();
        }

        /**
         * 获取语种数量
         */
        public int getLanguageCount() {
            return list != null ? list.size() : 0;
        }

        /**
         * 根据代码查找语种
         */
        public LanguageInfo findByCode(String code) {
            if (list == null || code == null) {
                return null;
            }
            return list.stream()
                    .filter(lang -> code.equals(lang.getCode()))
                    .findFirst()
                    .orElse(null);
        }

        /**
         * 获取支持完整功能的语种列表
         */
        public List<LanguageInfo> getFullSupportLanguages() {
            if (list == null) {
                return null;
            }
            return list.stream()
                    .filter(LanguageInfo::supportFullVideoTranslation)
                    .toList();
        }

        /**
         * 获取热门语种列表（前5个）
         */
        public List<LanguageInfo> getPopularLanguages() {
            if (list == null) {
                return null;
            }
            return list.stream()
                    .sorted((a, b) -> {
                        int orderA = a.getSortOrder() != null ? a.getSortOrder() : 0;
                        int orderB = b.getSortOrder() != null ? b.getSortOrder() : 0;
                        return Integer.compare(orderB, orderA); // 降序
                    })
                    .limit(5)
                    .toList();
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return code == null || code != 0;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "未知错误";
    }

    /**
     * 获取语种列表
     */
    public List<LanguageListResult.LanguageInfo> getLanguageList() {
        return result != null ? result.getList() : null;
    }

    /**
     * 获取语种总数
     */
    public Integer getTotal() {
        return result != null ? result.getTotal() : 0;
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return isSuccess() && result != null && result.hasData();
    }

    /**
     * 根据代码查找语种
     */
    public LanguageListResult.LanguageInfo findLanguageByCode(String code) {
        return result != null ? result.findByCode(code) : null;
    }

    /**
     * 获取响应摘要信息
     */
    public String getSummary() {
        if (isSuccess()) {
            return String.format("语种列表查询成功: 共%d种语言", getTotal());
        } else {
            return String.format("语种列表查询失败: code=%d, message=%s", code, message);
        }
    }

    /**
     * 创建成功响应
     */
    public static LanguageListResponseBO success(List<LanguageListResult.LanguageInfo> languages) {
        LanguageListResponseBO response = new LanguageListResponseBO();
        response.setCode(0);
        response.setMessage("SUCCESS");
        
        LanguageListResult result = new LanguageListResult();
        result.setTotal(languages != null ? languages.size() : 0);
        result.setList(languages);
        response.setResult(result);
        
        return response;
    }

    /**
     * 创建失败响应
     */
    public static LanguageListResponseBO failure(Integer code, String message) {
        LanguageListResponseBO response = new LanguageListResponseBO();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    @Override
    public String toString() {
        return "LanguageListResponseBO{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
