package com.nacos.model.SoundView.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 讯飞羚羊平台支持的音色枚举
 * 基于羚羊平台API文档的音色列表接口
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(name = "羚羊平台音色枚举", description = "羚羊平台支持的音色类型")
public enum LingyangVoiceEnum {

    // ==================== 中文音色 ====================
    
    /**
     * 中文女声 - 温柔
     */
    CN_FEMALE_GENTLE(246L, "cn", "晓晓", "温柔女声", 1, "女", 1, "标准", 2, "青年"),
    
    /**
     * 中文男声 - 磁性
     */
    CN_MALE_MAGNETIC(247L, "cn", "云健", "磁性男声", 2, "男", 1, "标准", 2, "青年"),
    
    /**
     * 中文女声 - 甜美
     */
    CN_FEMALE_SWEET(248L, "cn", "晓梦", "甜美女声", 1, "女", 1, "标准", 1, "少年"),
    
    /**
     * 中文男声 - 沉稳
     */
    CN_MALE_STEADY(249L, "cn", "云扬", "沉稳男声", 2, "男", 1, "标准", 3, "中年"),

    // ==================== 英文音色 ====================
    
    /**
     * 英文女声 - 标准
     */
    EN_FEMALE_STANDARD(250L, "en", "Emma", "标准女声", 1, "女", 1, "美式", 2, "青年"),
    
    /**
     * 英文男声 - 标准
     */
    EN_MALE_STANDARD(251L, "en", "Davis", "标准男声", 2, "男", 1, "美式", 2, "青年"),
    
    /**
     * 英文女声 - 英式
     */
    EN_FEMALE_BRITISH(252L, "en", "Catherine", "英式女声", 1, "女", 2, "英式", 2, "青年"),
    
    /**
     * 英文男声 - 英式
     */
    EN_MALE_BRITISH(253L, "en", "William", "英式男声", 2, "男", 2, "英式", 2, "青年"),

    // ==================== 日文音色 ====================
    
    /**
     * 日文女声 - 标准
     */
    JA_FEMALE_STANDARD(254L, "ja", "さくら", "标准女声", 1, "女", 1, "标准", 2, "青年"),
    
    /**
     * 日文男声 - 标准
     */
    JA_MALE_STANDARD(255L, "ja", "たろう", "标准男声", 2, "男", 1, "标准", 2, "青年"),

    // ==================== 韩文音色 ====================
    
    /**
     * 韩文女声 - 标准
     */
    KO_FEMALE_STANDARD(256L, "ko", "지은", "标准女声", 1, "女", 1, "标准", 2, "青年"),
    
    /**
     * 韩文男声 - 标准
     */
    KO_MALE_STANDARD(257L, "ko", "민수", "标准男声", 2, "男", 1, "标准", 2, "青年"),

    // ==================== 其他语种音色 ====================
    
    /**
     * 法文女声 - 标准
     */
    FR_FEMALE_STANDARD(258L, "fr", "Marie", "标准女声", 1, "女", 1, "标准", 2, "青年"),
    
    /**
     * 德文男声 - 标准
     */
    DE_MALE_STANDARD(259L, "de", "Hans", "标准男声", 2, "男", 1, "标准", 2, "青年"),
    
    /**
     * 西班牙文女声 - 标准
     */
    ES_FEMALE_STANDARD(260L, "es", "Carmen", "标准女声", 1, "女", 1, "标准", 2, "青年"),
    
    /**
     * 俄文男声 - 标准
     */
    RU_MALE_STANDARD(261L, "ru", "Иван", "标准男声", 2, "男", 1, "标准", 2, "青年");

    /**
     * 音色ID
     */
    @Schema(description = "音色ID")
    private final Long voiceId;

    /**
     * 语种代码
     */
    @Schema(description = "语种代码")
    private final String languageCode;

    /**
     * 音色名称
     */
    @Schema(description = "音色名称")
    private final String voiceName;

    /**
     * 音色描述
     */
    @Schema(description = "音色描述")
    private final String description;

    /**
     * 性别代码 (1:女 2:男)
     */
    @Schema(description = "性别代码")
    private final Integer genderCode;

    /**
     * 性别描述
     */
    @Schema(description = "性别描述")
    private final String genderStr;

    /**
     * 口音代码
     */
    @Schema(description = "口音代码")
    private final Integer accentCode;

    /**
     * 口音描述
     */
    @Schema(description = "口音描述")
    private final String accentStr;

    /**
     * 年龄段代码 (1:少年 2:青年 3:中年)
     */
    @Schema(description = "年龄段代码")
    private final Integer ageRangeCode;

    /**
     * 年龄段描述
     */
    @Schema(description = "年龄段描述")
    private final String ageRangeStr;

    LingyangVoiceEnum(Long voiceId, String languageCode, String voiceName, String description,
                     Integer genderCode, String genderStr, Integer accentCode, String accentStr,
                     Integer ageRangeCode, String ageRangeStr) {
        this.voiceId = voiceId;
        this.languageCode = languageCode;
        this.voiceName = voiceName;
        this.description = description;
        this.genderCode = genderCode;
        this.genderStr = genderStr;
        this.accentCode = accentCode;
        this.accentStr = accentStr;
        this.ageRangeCode = ageRangeCode;
        this.ageRangeStr = ageRangeStr;
    }

    // ==================== Getter方法 ====================

    public Long getVoiceId() {
        return voiceId;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public String getVoiceName() {
        return voiceName;
    }

    public String getDescription() {
        return description;
    }

    public Integer getGenderCode() {
        return genderCode;
    }

    public String getGenderStr() {
        return genderStr;
    }

    public Integer getAccentCode() {
        return accentCode;
    }

    public String getAccentStr() {
        return accentStr;
    }

    public Integer getAgeRangeCode() {
        return ageRangeCode;
    }

    public String getAgeRangeStr() {
        return ageRangeStr;
    }

    // ==================== 业务方法 ====================

    /**
     * 根据音色ID获取枚举
     */
    public static LingyangVoiceEnum getByVoiceId(Long voiceId) {
        if (voiceId == null) {
            return null;
        }
        for (LingyangVoiceEnum voice : values()) {
            if (voice.getVoiceId().equals(voiceId)) {
                return voice;
            }
        }
        return null;
    }

    /**
     * 根据语种代码获取音色列表
     */
    public static LingyangVoiceEnum[] getByLanguageCode(String languageCode) {
        if (languageCode == null) {
            return new LingyangVoiceEnum[0];
        }
        return java.util.Arrays.stream(values())
                .filter(voice -> voice.getLanguageCode().equals(languageCode))
                .toArray(LingyangVoiceEnum[]::new);
    }

    /**
     * 根据性别筛选音色
     */
    public static LingyangVoiceEnum[] getByGender(String languageCode, Integer genderCode) {
        if (languageCode == null || genderCode == null) {
            return new LingyangVoiceEnum[0];
        }
        return java.util.Arrays.stream(values())
                .filter(voice -> voice.getLanguageCode().equals(languageCode) && 
                               voice.getGenderCode().equals(genderCode))
                .toArray(LingyangVoiceEnum[]::new);
    }

    /**
     * 获取默认音色（每种语言的第一个音色）
     */
    public static LingyangVoiceEnum getDefaultVoice(String languageCode) {
        LingyangVoiceEnum[] voices = getByLanguageCode(languageCode);
        return voices.length > 0 ? voices[0] : null;
    }

    /**
     * 检查是否为女声
     */
    public boolean isFemale() {
        return genderCode != null && genderCode == 1;
    }

    /**
     * 检查是否为男声
     */
    public boolean isMale() {
        return genderCode != null && genderCode == 2;
    }

    /**
     * 获取完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(voiceName).append(" - ").append(description);
        
        if (genderStr != null) {
            sb.append(" (").append(genderStr);
            if (ageRangeStr != null) {
                sb.append("·").append(ageRangeStr);
            }
            if (accentStr != null && !accentStr.equals("标准")) {
                sb.append("·").append(accentStr);
            }
            sb.append(")");
        }
        
        return sb.toString();
    }

    /**
     * 检查是否支持指定语种
     */
    public static boolean isSupportedLanguage(String languageCode) {
        return getByLanguageCode(languageCode).length > 0;
    }
}
