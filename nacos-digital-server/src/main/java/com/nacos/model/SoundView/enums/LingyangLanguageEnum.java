package com.nacos.model.SoundView.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 讯飞羚羊平台支持的语种枚举
 * 基于羚羊平台API文档的语种列表接口
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(name = "羚羊平台语种枚举", description = "羚羊平台支持的语种类型")
public enum LingyangLanguageEnum {

    /**
     * 中文（简体）
     */
    CHINESE("cn", "zh", "中文", "Chinese", true, true, true),
    
    /**
     * 英文
     */
    ENGLISH("en", "en", "英文", "English", true, true, true),
    
    /**
     * 日文
     */
    JAPANESE("ja", "ja", "日文", "Japanese", true, true, true),
    
    /**
     * 韩文
     */
    KOREAN("ko", "ko", "韩文", "Korean", true, true, true),
    
    /**
     * 法文
     */
    FRENCH("fr", "fr", "法文", "French", true, true, true),
    
    /**
     * 德文
     */
    GERMAN("de", "de", "德文", "German", true, true, true),
    
    /**
     * 西班牙文
     */
    SPANISH("es", "es", "西班牙文", "Spanish", true, true, true),
    
    /**
     * 俄文
     */
    RUSSIAN("ru", "ru", "俄文", "Russian", true, true, true),
    
    /**
     * 意大利文
     */
    ITALIAN("it", "it", "意大利文", "Italian", true, true, true),
    
    /**
     * 葡萄牙文
     */
    PORTUGUESE("pt", "pt", "葡萄牙文", "Portuguese", true, true, true),
    
    /**
     * 阿拉伯文
     */
    ARABIC("ar", "ar", "阿拉伯文", "Arabic", true, true, true),
    
    /**
     * 泰文
     */
    THAI("th", "th", "泰文", "Thai", true, true, false),
    
    /**
     * 越南文
     */
    VIETNAMESE("vi", "vi", "越南文", "Vietnamese", true, true, false),
    
    /**
     * 印地文
     */
    HINDI("hi", "hi", "印地文", "Hindi", true, true, false),
    
    /**
     * 马来文
     */
    MALAY("ms", "ms", "马来文", "Malay", true, true, false);

    /**
     * 羚羊平台语种代码
     */
    @Schema(description = "羚羊平台语种代码")
    private final String lingyangCode;

    /**
     * 标准语种代码
     */
    @Schema(description = "标准语种代码")
    private final String standardCode;

    /**
     * 中文名称
     */
    @Schema(description = "中文名称")
    private final String chineseName;

    /**
     * 英文名称
     */
    @Schema(description = "英文名称")
    private final String englishName;

    /**
     * 是否支持语音识别
     */
    @Schema(description = "是否支持语音识别")
    private final boolean supportSpeechRecognition;

    /**
     * 是否支持机器翻译
     */
    @Schema(description = "是否支持机器翻译")
    private final boolean supportTranslation;

    /**
     * 是否支持语音合成
     */
    @Schema(description = "是否支持语音合成")
    private final boolean supportSpeechSynthesis;

    LingyangLanguageEnum(String lingyangCode, String standardCode, String chineseName, String englishName,
                        boolean supportSpeechRecognition, boolean supportTranslation, boolean supportSpeechSynthesis) {
        this.lingyangCode = lingyangCode;
        this.standardCode = standardCode;
        this.chineseName = chineseName;
        this.englishName = englishName;
        this.supportSpeechRecognition = supportSpeechRecognition;
        this.supportTranslation = supportTranslation;
        this.supportSpeechSynthesis = supportSpeechSynthesis;
    }

    // ==================== Getter方法 ====================

    public String getLingyangCode() {
        return lingyangCode;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public boolean isSupportSpeechRecognition() {
        return supportSpeechRecognition;
    }

    public boolean isSupportTranslation() {
        return supportTranslation;
    }

    public boolean isSupportSpeechSynthesis() {
        return supportSpeechSynthesis;
    }

    // ==================== 业务方法 ====================

    /**
     * 根据羚羊平台语种代码获取枚举
     */
    public static LingyangLanguageEnum getByLingyangCode(String lingyangCode) {
        if (lingyangCode == null) {
            return null;
        }
        for (LingyangLanguageEnum language : values()) {
            if (language.getLingyangCode().equals(lingyangCode)) {
                return language;
            }
        }
        return null;
    }

    /**
     * 根据标准语种代码获取枚举
     */
    public static LingyangLanguageEnum getByStandardCode(String standardCode) {
        if (standardCode == null) {
            return null;
        }
        for (LingyangLanguageEnum language : values()) {
            if (language.getStandardCode().equals(standardCode)) {
                return language;
            }
        }
        return null;
    }

    /**
     * 检查是否支持完整的视频翻译功能
     */
    public boolean supportFullVideoTranslation() {
        return supportSpeechRecognition && supportTranslation && supportSpeechSynthesis;
    }

    /**
     * 检查两种语言是否支持互译
     */
    public static boolean isSupportedTranslation(String sourceLanguage, String targetLanguage) {
        LingyangLanguageEnum source = getByLingyangCode(sourceLanguage);
        LingyangLanguageEnum target = getByLingyangCode(targetLanguage);
        
        return source != null && target != null && 
               source.isSupportTranslation() && target.isSupportTranslation() &&
               !sourceLanguage.equals(targetLanguage);
    }

    /**
     * 检查语种是否支持语音识别
     */
    public static boolean isSupportedSpeechRecognition(String languageCode) {
        LingyangLanguageEnum language = getByLingyangCode(languageCode);
        return language != null && language.isSupportSpeechRecognition();
    }

    /**
     * 检查语种是否支持语音合成
     */
    public static boolean isSupportedSpeechSynthesis(String languageCode) {
        LingyangLanguageEnum language = getByLingyangCode(languageCode);
        return language != null && language.isSupportSpeechSynthesis();
    }

    /**
     * 获取显示名称（优先中文）
     */
    public String getDisplayName() {
        return chineseName != null && !chineseName.trim().isEmpty() ? chineseName : englishName;
    }

    /**
     * 获取语种描述
     */
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getDisplayName()).append(" (").append(lingyangCode).append(")");
        
        if (supportFullVideoTranslation()) {
            sb.append(" - 完整支持");
        } else {
            sb.append(" - 部分支持");
        }
        
        return sb.toString();
    }

    /**
     * 检查是否为热门语种
     */
    public boolean isPopularLanguage() {
        return this == CHINESE || this == ENGLISH || this == JAPANESE || 
               this == KOREAN || this == FRENCH || this == GERMAN || 
               this == SPANISH || this == RUSSIAN;
    }
}
