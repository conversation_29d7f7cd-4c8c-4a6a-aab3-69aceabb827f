package com.nacos.model.SoundView.model.request;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 羚羊平台音色列表查询请求模型
 * 对应API: /open/v1/timbre/info/list
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "音色列表查询请求参数")
public class VoiceListRequestBO {

    /**
     * 语种列表
     */
    @Schema(description = "语种列表", required = false, example = "[\"cn\",\"en\"]")
    @JSONField(name = "langList")
    private String[] langList;

    /**
     * 偏移量
     */
    @Schema(description = "偏移量", required = true, example = "0")
    @JSONField(name = "offset")
    @NotNull(message = "偏移量不能为空")
    private Integer offset = 0;

    /**
     * 页码
     */
    @Schema(description = "页码", required = false, example = "10")
    @JSONField(name = "limit")
    private Integer limit = 10;



    // ==================== 构造方法 ====================

    public VoiceListRequestBO() {
    }

    public VoiceListRequestBO(String[] langList) {
        this.langList = langList;
        this.offset = 0;
        this.limit = 10;
    }

    public VoiceListRequestBO(String[] langList, Integer offset, Integer limit) {
        this.langList = langList;
        this.offset = offset != null ? offset : 0;
        this.limit = limit != null ? limit : 10;
    }

    // ==================== 业务方法 ====================

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return offset != null && offset >= 0 &&
               limit != null && limit > 0;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (offset == null || offset < 0) {
            offset = 0;
        }
        if (limit == null || limit <= 0) {
            limit = 10;
        }
    }

    /**
     * 获取页码（从1开始）
     */
    public Integer getPageNumber() {
        if (offset == null || limit == null || limit <= 0) {
            return 1;
        }
        return (offset / limit) + 1;
    }

    /**
     * 设置页码（从1开始）
     */
    public void setPageNumber(Integer pageNumber) {
        if (pageNumber != null && pageNumber > 0 && limit != null && limit > 0) {
            this.offset = (pageNumber - 1) * limit;
        }
    }



    /**
     * 检查是否为第一页
     */
    public boolean isFirstPage() {
        return offset == null || offset == 0;
    }

    /**
     * 获取下一页的偏移量
     */
    public Integer getNextOffset() {
        if (offset == null || limit == null) {
            return null;
        }
        return offset + limit;
    }



    /**
     * 获取请求摘要信息
     */
    public String getSummary() {
        String langListStr = langList != null ? String.join(",", langList) : "all";
        return String.format("查询音色列表: langList=[%s], offset=%d, limit=%d",
            langListStr,
            offset != null ? offset : 0,
            limit != null ? limit : 10);
    }

    @Override
    public String toString() {
        String langListStr = langList != null ? String.join(",", langList) : "null";
        return "VoiceListRequestBO{" +
                "langList=[" + langListStr + "]" +
                ", offset=" + offset +
                ", limit=" + limit +
                '}';
    }
}
