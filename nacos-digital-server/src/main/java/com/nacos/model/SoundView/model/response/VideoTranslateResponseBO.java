package com.nacos.model.SoundView.model.response;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 羚羊平台视频翻译响应模型
 * 对应API: /open/v1/video/info/create
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "视频翻译响应结果")
public class VideoTranslateResponseBO {

    /**
     * 返回码
     */
    @Schema(description = "返回码", example = "0")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "SUCCESS")
    @JSONField(name = "message")
    private String message;

    /**
     * 返回结果
     */
    @Schema(description = "返回结果")
    @JSONField(name = "result")
    private VideoTranslateResult result;

    /**
     * 视频翻译结果内部类
     */
    @Data
    @Schema(description = "视频翻译结果详情")
    public static class VideoTranslateResult {

        /**
         * 任务ID
         */
        @Schema(description = "任务ID", example = "task_12345")
        @JSONField(name = "taskId")
        private String taskId;

        /**
         * 任务ID（数值型）
         */
        @Schema(description = "任务ID（数值型）", example = "12345")
        @JSONField(name = "id")
        private Long id;

        /**
         * 任务状态
         */
        @Schema(description = "任务状态", example = "submitted")
        @JSONField(name = "status")
        private String status;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间", example = "2025-01-25 10:30:00")
        @JSONField(name = "createTime")
        private String createTime;

        // ==================== 业务方法 ====================

        /**
         * 获取任务ID字符串
         */
        public String getTaskIdString() {
            if (taskId != null && !taskId.trim().isEmpty()) {
                return taskId;
            }
            return id != null ? id.toString() : null;
        }

        /**
         * 获取任务ID数值
         */
        public Long getTaskIdLong() {
            if (id != null) {
                return id;
            }
            if (taskId != null && !taskId.trim().isEmpty()) {
                try {
                    return Long.parseLong(taskId);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
            return null;
        }

        /**
         * 检查任务是否创建成功
         */
        public boolean isTaskCreated() {
            return (taskId != null && !taskId.trim().isEmpty()) || 
                   (id != null && id > 0);
        }

        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            if (status == null) {
                return "未知状态";
            }
            switch (status.toLowerCase()) {
                case "submitted": return "已提交";
                case "waiting": return "等待中";
                case "processing": return "处理中";
                case "completed": return "已完成";
                case "failed": return "失败";
                case "cancelled": return "已取消";
                default: return status;
            }
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return code == null || code != 0;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "未知错误";
    }

    /**
     * 获取任务ID
     */
    public String getTaskId() {
        return result != null ? result.getTaskIdString() : null;
    }

    /**
     * 获取任务ID（数值）
     */
    public Long getTaskIdLong() {
        return result != null ? result.getTaskIdLong() : null;
    }

    /**
     * 检查任务是否创建成功
     */
    public boolean isTaskCreated() {
        return isSuccess() && result != null && result.isTaskCreated();
    }

    /**
     * 获取任务状态
     */
    public String getTaskStatus() {
        return result != null ? result.getStatus() : null;
    }

    /**
     * 获取创建时间
     */
    public String getCreateTime() {
        return result != null ? result.getCreateTime() : null;
    }

    /**
     * 获取响应摘要信息
     */
    public String getSummary() {
        if (isSuccess()) {
            return String.format("任务创建成功: taskId=%s, status=%s", 
                getTaskId(), getTaskStatus());
        } else {
            return String.format("任务创建失败: code=%d, message=%s", 
                code, message);
        }
    }

    /**
     * 创建成功响应
     */
    public static VideoTranslateResponseBO success(String taskId, String status) {
        VideoTranslateResponseBO response = new VideoTranslateResponseBO();
        response.setCode(0);
        response.setMessage("SUCCESS");
        
        VideoTranslateResult result = new VideoTranslateResult();
        result.setTaskId(taskId);
        result.setStatus(status);
        response.setResult(result);
        
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VideoTranslateResponseBO failure(Integer code, String message) {
        VideoTranslateResponseBO response = new VideoTranslateResponseBO();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    @Override
    public String toString() {
        return "VideoTranslateResponseBO{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
