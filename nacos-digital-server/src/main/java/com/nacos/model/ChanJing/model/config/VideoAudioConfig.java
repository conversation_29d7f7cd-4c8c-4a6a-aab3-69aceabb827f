package com.nacos.model.ChanJing.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频合成 - 音频配置
 */
@Data
@Schema(description = "视频合成 - 音频配置")
public class VideoAudioConfig {
    
    @Schema(description = "TTS文本转语音配置 (如果type为tts)")
    private VideoTtsConfig tts;

    @Schema(description = "音频文件URL (如果type为audio)。支持MP3、M4A、WAV格式。")
    @JsonProperty("wav_url")
    private String wavUrl;

    @Schema(description = "生成声音的类型，tts 或 audio (二选一)。默认为 tts。")
    private String type;

    @Schema(description = "音量，范围0-100。默认 100。")
    private Integer volume;

    @Schema(description = "语言类型，默认 cn (中文)。")
    private String language;
} 