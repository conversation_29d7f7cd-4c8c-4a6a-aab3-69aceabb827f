package com.nacos.model.ChanJing.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 视频合成 - TTS配置
 */
@Data
@Schema(description = "视频合成 - TTS配置")
public class VideoTtsConfig {
    @Schema(description = "文本内容。所有内容放到一个字符串中即可，使用标点符号分割，无需分成多个字符串。例如: [\"君不见黄河之水天上来，奔流到海不复回。\"]")
    private List<String> text;

    @Schema(description = "语速，范围0.5到2.0。")
    private Float speed;

    @Schema(description = "音色ID。参考拉取数字人列表中的 audio_man_id。例如: C-f2429d07554749839849497589199916")
    @JsonProperty("audio_man")
    private String audioMan;

    @Schema(description = "音调参数，定制声音的音调范围")
    private Float pitch;
} 