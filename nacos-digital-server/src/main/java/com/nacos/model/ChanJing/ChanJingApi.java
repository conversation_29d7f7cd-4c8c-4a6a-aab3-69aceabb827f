package com.nacos.model.ChanJing;

import com.nacos.model.ChanJing.model.*;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.MediaType;
import okhttp3.logging.HttpLoggingInterceptor;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class ChanJingApi {
    private static final Logger log = LoggerFactory.getLogger(ChanJingApi.class);
    // OkHttpClient 实例，用于发送HTTP请求
    private final OkHttpClient okHttpClient;
    // ObjectMapper 实例，用于JSON序列化和反序列化
    private final ObjectMapper objectMapper;


    // 禅境APP_ID
    private static final String CHANJING_APP_ID = "1e5054ca";
    // 禅境SECRET_KEY
    private static final String CHANJING_SECRET_KEY = "ee0cf34fb2c74abd882b0f26052bd094";

    // 基础URL
    private static final String CHANJING_API_BASE_URL = "https://www.chanjing.cc/api";
    // 获取token
    private static final String GET_TOKEN_ENDPOINT = "/open/v1/access_token";
    // 生成数字人形象
    private static final String CREATE_CUSTOMISED_PERSON_ENDPOINT = "/open/v1/create_customised_person";
    // 拉取形象列表
    private static final String LIST_CUSTOMISED_PERSON_ENDPOINT = "/open/v1/list_customised_person";
    // 获取形象详情
    private static final String GET_CUSTOMISED_PERSON_DETAIL_ENDPOINT = "/open/v1/customised_person";
    // 创建视频合成任务
    private static final String CREATE_VIDEO_ENDPOINT = "/open/v1/create_video";
    // 获取视频列表
    private static final String LIST_VIDEO_ENDPOINT = "/open/v1/video_list";
    // 获取视频详情
    private static final String GET_VIDEO_DETAIL_ENDPOINT = "/open/v1/video";
    // JSON MediaType
    private static final MediaType MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    

    // 构造函数
    public ChanJingApi(ObjectMapper objectMapper) {
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(
                message -> log.info("OkHttp: {}", message));
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(90, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(90, TimeUnit.SECONDS)    // 读取超时时间
                .writeTimeout(90, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor) // 添加日志拦截器
                .build(); // 构建OkHttpClient实例
        this.objectMapper = objectMapper; // 使用注入的 ObjectMapper
    }

    // 获取token
    public String getToken(String secretKey) {
        // 禅境API请求URL
        String url = CHANJING_API_BASE_URL + GET_TOKEN_ENDPOINT;
        try {
            // if (secretKey == null) {
                secretKey = CHANJING_SECRET_KEY;
            // }
            // 创建请求体
            String requestBodyJson = "{\"app_id\":\"" + CHANJING_APP_ID + "\",\"secret_key\":\"" + secretKey + "\"}";
            RequestBody body = RequestBody.create(requestBodyJson, MEDIA_TYPE);
            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();
            // 发送请求
            Response response = okHttpClient.newCall(request).execute();
            // 获取响应体
            String responseBodyString = response.body().string();
            log.info("禅境API响应体: {}", responseBodyString);
            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(responseBodyString);
            // 从响应中提取access_token
            if(jsonNode.get("code").asInt() == 0 && jsonNode.get("msg").asText().equals("success")){
                String accessToken = jsonNode.get("data").get("access_token").asText();
                log.info("禅境API获取token成功: {}", accessToken);
                return accessToken;
            }else{
                log.error("禅境API获取token失败: {}", responseBodyString);
                return null;
            }
            
        } catch (Exception e) {
            log.error("请求禅境API时发生未预料的异常: " + e.getMessage(), e);
            return null;
        }
    }

    // 调用生成数字人形象
    public CreateCustomisedPersonResponse createCustomisedPerson(CreateCustomisedPersonRequest request,
            String accessToken) {
        // 禅境API请求URL
        String url = CHANJING_API_BASE_URL + CREATE_CUSTOMISED_PERSON_ENDPOINT;
        try {
            // 将请求体转换为JSON字符串
            String requestBodyJson = objectMapper.writeValueAsString(request);
            // 创建请求体
            RequestBody body = RequestBody.create(requestBodyJson, MEDIA_TYPE);
            // 创建请求
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("access_token", accessToken)
                    .addHeader("Content-Type", "application/json; charset=utf-8") // OkHttp的MediaType已含charset
                    .build();

            // 记录请求信息
            log.info("禅境API的请求体: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    // 获取响应体
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    // 记录错误信息
                    log.error("请求禅境API失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    // 创建错误响应
                    CreateCustomisedPersonResponse errorResponse = new CreateCustomisedPersonResponse();
                    // 设置错误码
                    errorResponse.setCode(response.code()); // 使用HTTP状态码作为错误码，或特定负值
                    errorResponse.setMsg("请求禅境API失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }
                // 如果响应体为空
                if (response.body() == null) {
                    // 记录错误信息
                    log.error("请求禅境API成功，但响应体为空");
                    // 创建错误响应
                    CreateCustomisedPersonResponse errorResponse = new CreateCustomisedPersonResponse();
                    // 设置错误码
                    errorResponse.setCode(-2); // 自定义错误码表示响应体为空
                    // 设置错误信息
                    errorResponse.setMsg("请求禅境API成功，但响应体为空");
                    return errorResponse;
                }
                // 获取响应体
                String responseBodyString = response.body().string();
                // 响应体已由HttpLoggingInterceptor记录
                log.info("禅境API响应体: {}", responseBodyString);
                // 将响应体转换为CreateCustomisedPersonResponse对象
                return objectMapper.readValue(responseBodyString, CreateCustomisedPersonResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API时发生IO异常: " + e.getMessage(), e);
                CreateCustomisedPersonResponse errorResponse = new CreateCustomisedPersonResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API时发生未预料的异常: " + e.getMessage(), e);
            CreateCustomisedPersonResponse errorResponse = new CreateCustomisedPersonResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 拉取形象列表
    public ListCustomisedPersonResponse listCustomisedPerson(ListCustomisedPersonRequest request, String accessToken) {
        String url = CHANJING_API_BASE_URL + LIST_CUSTOMISED_PERSON_ENDPOINT;
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            RequestBody body = RequestBody.create(requestBodyJson, MEDIA_TYPE);
            Request httpRequest = new Request.Builder()
                    .url(url) // 设置URL
                    .post(body) // 设置POST方法和请求体
                    .addHeader("access_token", accessToken) // 添加access_token到请求头
                    .addHeader("Content-Type", "application/json; charset=utf-8") // 设置内容类型
                    .build();

            log.info("禅境API 拉取形象列表 请求体: {}", requestBodyJson);
            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    log.error("请求禅境API失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    ListCustomisedPersonResponse errorResponse = new ListCustomisedPersonResponse();
                    errorResponse.setCode(response.code());
                    errorResponse.setMsg("请求禅境API失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }
                if (response.body() == null) {
                    log.error("请求禅境API成功，但响应体为空");
                    ListCustomisedPersonResponse errorResponse = new ListCustomisedPersonResponse();
                    errorResponse.setCode(-2);
                    errorResponse.setMsg("请求禅境API成功，但响应体为空");
                    return errorResponse;
                }
                String responseBodyString = response.body().string();
                log.info("禅境API 拉取形象列表 响应体: {}", responseBodyString);
                // 将响应体转换为ListCustomisedPersonResponse对象
                return objectMapper.readValue(responseBodyString, ListCustomisedPersonResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API时发生IO异常: " + e.getMessage(), e);
                ListCustomisedPersonResponse errorResponse = new ListCustomisedPersonResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API时发生未预料的异常: " + e.getMessage(), e);
            ListCustomisedPersonResponse errorResponse = new ListCustomisedPersonResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 创建视频合成任务
    public CreateVideoResponse createVideo(CreateVideoRequest request, String accessToken) {
        String url = CHANJING_API_BASE_URL + CREATE_VIDEO_ENDPOINT;
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            RequestBody body = RequestBody.create(requestBodyJson, MEDIA_TYPE);
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("access_token", accessToken)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();

            log.info("禅境API 创建视频合成任务 请求体: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    log.error("请求禅境API创建视频合成任务失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    CreateVideoResponse errorResponse = new CreateVideoResponse();
                    errorResponse.setCode(response.code());
                    errorResponse.setMsg("请求禅境API创建视频合成任务失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }

                if (response.body() == null) {
                    log.error("请求禅境API创建视频合成任务成功，但响应体为空");
                    CreateVideoResponse errorResponse = new CreateVideoResponse();
                    errorResponse.setCode(-2); // 自定义错误码表示响应体为空
                    errorResponse.setMsg("请求禅境API创建视频合成任务成功，但响应体为空");
                    return errorResponse;
                }

                String responseBodyString = response.body().string();
                log.info("禅境API 创建视频合成任务 响应体: {}", responseBodyString);
                return objectMapper.readValue(responseBodyString, CreateVideoResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API创建视频合成任务时发生IO异常: " + e.getMessage(), e);
                CreateVideoResponse errorResponse = new CreateVideoResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API创建视频合成任务时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API创建视频合成任务时发生未预料的异常: " + e.getMessage(), e);
            CreateVideoResponse errorResponse = new CreateVideoResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API创建视频合成任务时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 获取视频列表
    public ListVideoResponse listVideo(ListVideoRequest request, String accessToken) {
        String url = CHANJING_API_BASE_URL + LIST_VIDEO_ENDPOINT;
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            RequestBody body = RequestBody.create(requestBodyJson, MEDIA_TYPE);
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("access_token", accessToken)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();

            log.info("禅境API 获取视频列表 请求体: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    log.error("请求禅境API获取视频列表失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    ListVideoResponse errorResponse = new ListVideoResponse();
                    errorResponse.setCode(response.code());
                    errorResponse.setMsg("请求禅境API获取视频列表失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }

                if (response.body() == null) {
                    log.error("请求禅境API获取视频列表成功，但响应体为空");
                    ListVideoResponse errorResponse = new ListVideoResponse();
                    errorResponse.setCode(-2); // 自定义错误码表示响应体为空
                    errorResponse.setMsg("请求禅境API获取视频列表成功，但响应体为空");
                    return errorResponse;
                }

                String responseBodyString = response.body().string();
                log.info("禅境API 获取视频列表 响应体: {}", responseBodyString);
                return objectMapper.readValue(responseBodyString, ListVideoResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API获取视频列表时发生IO异常: " + e.getMessage(), e);
                ListVideoResponse errorResponse = new ListVideoResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API获取视频列表时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API获取视频列表时发生未预料的异常: " + e.getMessage(), e);
            ListVideoResponse errorResponse = new ListVideoResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API获取视频列表时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 获取视频详情
    public GetVideoDetailResponse getVideoDetail(String videoId, String accessToken) {
        String url = CHANJING_API_BASE_URL + GET_VIDEO_DETAIL_ENDPOINT + "?id=" + videoId;
        try {
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("access_token", accessToken)
                    .addHeader("Content-Type", "application/json; charset=utf-8") // Though typically not needed for GET, kept for consistency
                    .build();

            log.info("禅境API 获取视频详情 请求URL: {}", url);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    log.error("请求禅境API获取视频详情失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    GetVideoDetailResponse errorResponse = new GetVideoDetailResponse();
                    errorResponse.setCode(response.code());
                    errorResponse.setMsg("请求禅境API获取视频详情失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }

                if (response.body() == null) {
                    log.error("请求禅境API获取视频详情成功，但响应体为空");
                    GetVideoDetailResponse errorResponse = new GetVideoDetailResponse();
                    errorResponse.setCode(-2); // 自定义错误码表示响应体为空
                    errorResponse.setMsg("请求禅境API获取视频详情成功，但响应体为空");
                    return errorResponse;
                }

                String responseBodyString = response.body().string();
                log.info("禅境API 获取视频详情 响应体: {}", responseBodyString);
                return objectMapper.readValue(responseBodyString, GetVideoDetailResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API获取视频详情时发生IO异常: " + e.getMessage(), e);
                GetVideoDetailResponse errorResponse = new GetVideoDetailResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API获取视频详情时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API获取视频详情时发生未预料的异常: " + e.getMessage(), e);
            GetVideoDetailResponse errorResponse = new GetVideoDetailResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API获取视频详情时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }

    // 获取形象详情
    public CustomisedPersonResponse customisedPerson(CustomisedPersonRequest request, String accessToken) {
        String url = CHANJING_API_BASE_URL + GET_CUSTOMISED_PERSON_DETAIL_ENDPOINT + "?id=" + request.getId();
        try {
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("access_token", accessToken)
                    .addHeader("Content-Type", "application/json; charset=utf-8") // Though typically not needed for GET, kept for consistency
                    .build();

            log.info("禅境API 获取视频详情 请求URL: {}", url);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "N/A";
                    log.error("请求禅境API获取视频详情失败: HTTP Code {}, Body: {}", response.code(), errorBody);
                    CustomisedPersonResponse errorResponse = new CustomisedPersonResponse();
                    errorResponse.setCode(response.code());
                    errorResponse.setMsg("请求禅境API获取视频详情失败，HTTP状态码: " + response.code() + ",详情：" + errorBody);
                    return errorResponse;
                }

                if (response.body() == null) {
                    log.error("请求禅境API获取视频详情成功，但响应体为空");
                    CustomisedPersonResponse errorResponse = new CustomisedPersonResponse();
                    errorResponse.setCode(-2); // 自定义错误码表示响应体为空
                    errorResponse.setMsg("请求禅境API获取视频详情成功，但响应体为空");
                    return errorResponse;
                }

                String responseBodyString = response.body().string();
                log.info("禅境API 获取视频详情 响应体: {}", responseBodyString);
                return objectMapper.readValue(responseBodyString, CustomisedPersonResponse.class);

            } catch (IOException e) {
                log.error("请求禅境API获取视频详情时发生IO异常: " + e.getMessage(), e);
                CustomisedPersonResponse errorResponse = new CustomisedPersonResponse();
                errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
                errorResponse.setMsg("请求禅境API获取视频详情时发生IO异常: " + e.getMessage());
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("请求禅境API获取视频详情时发生未预料的异常: " + e.getMessage(), e);
            CustomisedPersonResponse errorResponse = new CustomisedPersonResponse();
            errorResponse.setCode(-1); // 自定义错误码表示客户端/网络错误
            errorResponse.setMsg("请求禅境API获取视频详情时发生未预料的异常: " + e.getMessage());
            return errorResponse;
        }
    }
}
