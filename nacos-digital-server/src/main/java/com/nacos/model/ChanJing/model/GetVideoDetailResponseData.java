package com.nacos.model.ChanJing.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "获取视频详情响应的数据部分")
public class GetVideoDetailResponseData {

    @Schema(title = "视频ID")
    @JsonProperty("id")
    private String id;

    @Schema(title = "状态，10-生成中；30-成功；4X-参数异常；5X-服务异常")
    @JsonProperty("status")
    private Integer status;

    @Schema(title = "任务进度 0-100")
    @JsonProperty("progress")
    private Integer progress;

    @Schema(title = "异常或失败的错误信息")
    @JsonProperty("msg")
    private String msg;

    @Schema(title = "视频播放地址")
    @JsonProperty("video_url")
    private String videoUrl;

    @Schema(title = "视频生成时间，时间戳")
    @JsonProperty("create_time")
    private Long createTime;

    @Schema(title = "视频预览图片")
    @JsonProperty("preview_url")
    private String previewUrl;

    @Schema(title = "字幕时间轴链接")
    @JsonProperty("subtitle_data_url")
    private String subtitleDataUrl;

    @Schema(title = "视频时长（单位秒）")
    @JsonProperty("duration")
    private Integer duration;
} 