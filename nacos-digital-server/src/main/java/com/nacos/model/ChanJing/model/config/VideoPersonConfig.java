package com.nacos.model.ChanJing.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频合成 - 人物配置
 */
@Data
@Schema(description = "视频合成 - 人物配置")
public class VideoPersonConfig {
    @Schema(description = "形象ID, 例如: C-d1af99e0fee34978bc844d43078bf8c9")
    private String id;

    @Schema(description = "数字人在背景中的x坐标")
    private Integer x;

    @Schema(description = "数字人在背景中的y坐标, 例如: 480")
    private Integer y;

    @Schema(description = "形象类型, 例如: whole_body (仅使用公共数字人时需传该参数)")
    @JsonProperty("figure_type")
    private String figureType;

    @Schema(description = "数字人宽度, 例如: 1080")
    private Integer width;

    @Schema(description = "数字人高度, 例如: 1920")
    private Integer height;

    @Schema(description = "驱动模式。支持正常顺序驱动 (\"\" 或不传) 和随机帧动作驱动 (random)。默认正常顺序驱动。")
    @JsonProperty("drive_mode")
    private String driveMode;

    @Schema(description = "是否驱动四通道WEBM视频。默认为 false。注意事项: 1. 需要数字人是WEBM格式四通道视频定制的。2. 2025年2月8号及之后定制的数字人开始生效。3. 该方式生成的数字人合成视频不含字幕以及背景。")
    @JsonProperty("is_rgba_mode")
    private Boolean isRgbaMode ;

    @Schema(description = "指定数字人驱动到素材末尾的播放顺序。1 为正放 (默认), 2 为倒放。")
    private Integer backway ;
} 