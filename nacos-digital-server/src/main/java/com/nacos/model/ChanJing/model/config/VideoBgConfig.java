package com.nacos.model.ChanJing.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 视频合成 - 背景图片配置
 */
@Data
@Schema(description = "视频合成 - 背景图片配置")
public class VideoBgConfig {
    @Schema(description = "背景图片URL")
    @JsonProperty("src_url")
    private String srcUrl;

    @Schema(description = "背景图片x坐标")
    private Integer x;

    @Schema(description = "背景图片y坐标")
    private Integer y;

    @Schema(description = "背景图片高度")
    private Integer height;
    
    @Schema(description = "背景图片宽度")
    private Integer width;
} 