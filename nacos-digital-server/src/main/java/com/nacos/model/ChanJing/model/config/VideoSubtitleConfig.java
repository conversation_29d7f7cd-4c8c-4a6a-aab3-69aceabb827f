package com.nacos.model.ChanJing.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频合成 - 字幕配置
 */
@Data
@Schema(description = "视频合成 - 字幕配置")
public class VideoSubtitleConfig {
    @Schema(description = "字体显示范围的起始x坐标。推荐 31。")
    private Integer x = 31;

    @Schema(description = "字体显示范围的起始y坐标。推荐 1521。")
    private Integer y = 1521;

    @Schema(description = "是否显示字幕。默认 true。")
    private Boolean show = true;

    @Schema(description = "字体显示范围的宽度。推荐 1000。")
    private Integer width = 1000;

    @Schema(description = "字体显示范围的高度。推荐 200。")
    private Integer height = 200;

    @Schema(description = "字体大小。推荐 64。")
    @JsonProperty("font_size")
    private Integer fontSize = 64;

    @Schema(description = "字体颜色值，HEX格式。例如: #000000")
    private String color;

    @Schema(description = "字体描边颜色值，HEX格式。例如: #000000")
    @JsonProperty("stroke_color")
    private String strokeColor;

    @Schema(description = "字体描边宽度。推荐 7。")
    @JsonProperty("stroke_width")
    private Integer strokeWidth = 7;

    @Schema(description = "字体ID。可选字体列表参考: https://docs.dingtalk.com/i/nodes/kDnRL6jAJM3yQX3zH6RPd1GGWyMoPYe1")
    @JsonProperty("font_id")
    private String fontId;
} 