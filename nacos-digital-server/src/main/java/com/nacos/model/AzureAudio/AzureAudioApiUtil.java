package com.nacos.model.AzureAudio;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.microsoft.cognitiveservices.speech.PropertyCollection;
import com.microsoft.cognitiveservices.speech.PropertyId;
import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisCancellationDetails;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisOutputFormat;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult;
import com.microsoft.cognitiveservices.speech.SynthesisVoicesResult;
import com.microsoft.cognitiveservices.speech.VoiceInfo;
import com.nacos.model.AzureAudio.model.TextToSpeechRequestBO;
import com.nacos.model.AzureAudio.model.TextToSpeechResponseBO;
import com.nacos.result.Result;
import com.nacos.utils.DigitalFileUtil;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * Azure语音服务工具类
 * 提供给内部服务调用的方法
 */
@Slf4j
@Schema(title = "Azure API工具类")
public class AzureAudioApiUtil {

    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAIL = "fail";

    /**
     * 文本转语音
     * 
     * @param request         文本转语音请求
     * @param subscriptionKey 订阅密钥
     * @param region          区域
     * @param userId          用户ID
     * @return 包含OSS URL的响应
     */
    public static Result<TextToSpeechResponseBO> textToSpeech(
            TextToSpeechRequestBO request,
            String fileName,
            String subscriptionKey,
            String region,
            String userId) {
        String methodName = "textToSpeech";
        try {
            if (request == null || !StringUtils.hasText(request.getText())) {
                return Result.ERROR("文本内容不能为空");
            }
            if (!StringUtils.hasText(subscriptionKey) || !StringUtils.hasText(region)) {
                return Result.ERROR("Azure语音服务配置无效");
            }
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "azure-" + userId + UUID.randomUUID().toString();
            }
            SpeechSynthesisOutputFormat outputFormat = SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3;
            if (StringUtils.hasText(request.getOutputFormat())) {
                try {
                    outputFormat = SpeechSynthesisOutputFormat.valueOf(request.getOutputFormat());
                } catch (IllegalArgumentException e) {
                    log.warn("[{}]不支持的输出格式：{}，将使用默认格式", methodName, request.getOutputFormat());
                }
            }
            SpeechSynthesisResult result;
            if (request.isUseSsml()) {
                result = AzureAudioApi.synthesizeSpeechFromSsml(
                        request.getText(),
                        subscriptionKey,
                        region);
            } else {
                result = AzureAudioApi.synthesizeSpeech(
                        request,
                        subscriptionKey,
                        region);
            }
            if (result == null) {
                return Result.ERROR("语音合成失败，结果为空");
            } else if (result.getReason() == ResultReason.Canceled) {
                SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                String errorMessage = String.format("语音合成取消，原因：%s，错误码：%s，错误详情：%s", cancellation.getReason(), cancellation.getErrorCode(), cancellation.getErrorDetails());
                log.error(errorMessage);
                return Result.ERROR(errorMessage);
            } else if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                byte[] audioData = result.getAudioData();
                String ossUrl = null;
                if (StringUtils.hasText(userId)) {
                    // 存入OSS用户音频目录
                    ossUrl = DigitalFileUtil.uploadDigitalResource(audioData, fileName, userId, null, 5, false);
                    if (ossUrl == null) {
                        return Result.ERROR("上传到OSS失败");
                    }
                }
                TextToSpeechResponseBO response = TextToSpeechResponseBO.fromSpeechSynthesisResult(
                        result, ossUrl, outputFormat.toString());
                return Result.SUCCESS(response);
            } else {
                return Result.ERROR("语音合成未完成，原因：" + result.getReason());
            }
        } catch (Exception e) {
            log.error("[{}]文本转语音失败：{}", methodName, e.getMessage(), e);
            return Result.ERROR("文本转语音处理异常：" + e.getMessage());
        }
    }

    /**
     * 获取支持的语音列表
     *
     * @param subscriptionKey 订阅密钥
     * @param region          区域
     * @return 语音列表
     */
    public static Result<List<VoiceInfo>> getVoicesList(String subscriptionKey, String region) {
        String methodName = "getVoicesList";
        try {
            // 参数验证
            if (!StringUtils.hasText(subscriptionKey) || !StringUtils.hasText(region)) {
                return Result.ERROR("Azure语音服务配置无效");
            }

            // 调用API获取语音列表
            SynthesisVoicesResult result = AzureAudioApi.getVoicesList(subscriptionKey, region);

            // 处理结果
            if (result == null) {
                return Result.ERROR("获取语音列表失败");
            }

            if (result.getReason() == ResultReason.VoicesListRetrieved) {
                // 直接返回VoiceInfo列表
                List<VoiceInfo> voicesList = result.getVoices();
                log.info("[{}]成功获取语音列表，共{}个语音", methodName, voicesList.size());
                return Result.SUCCESS(voicesList);

            } else {
                return Result.ERROR("获取语音列表失败，原因：" + result.getReason());
            }

        } catch (Exception e) {
            log.error("[{}]获取语音列表异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("获取语音列表处理异常：" + e.getMessage());
        }
    }



    /**
     * 根据错误码获取错误信息
     * 
     * @param errorCode 错误码
     * @return 错误描述
     */
    public static String getErrorMessageByCode(String errorCode) {
        if (errorCode == null) {
            return "未知错误";
        }

        switch (errorCode) {
            case "401":
                return "身份验证失败，请检查订阅密钥和区域 (401)";
            case "429":
                return "请求过多，已超出配额限制 (429)";
            case "500":
                return "服务器内部错误 (500)";
            case "503":
                return "服务暂时不可用 (503)";
            default:
                return "错误码: " + errorCode;
        }
    }

}