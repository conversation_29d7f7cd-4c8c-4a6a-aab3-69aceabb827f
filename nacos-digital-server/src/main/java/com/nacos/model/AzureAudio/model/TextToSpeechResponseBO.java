package com.nacos.model.AzureAudio.model;

import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文本转语音响应模型
 */
@Data
@Schema(description = "文本转语音响应")
public class TextToSpeechResponseBO {
    
    @Schema(description = "处理状态", example = "success")
    private String status;
    
    @Schema(description = "错误信息（如果有）", example = "")
    private String errorMessage;
    
    @Schema(description = "OSS存储URL")
    private String url;
    
    @Schema(description = "音频时长（毫秒）", example = "3500")
    private long duration;
    
    @Schema(description = "音频格式", example = "Audio24Khz48KBitRateMonoMp3")
    private String format;
    
    @Schema(description = "结果ID", example = "f84f0fd5-6b50-4a9e-8a6f-cdc32c4d9a1e")
    private String resultId;
    
    @Schema(description = "结果原因", example = "SynthesizingAudioCompleted")
    private String reason;
    
    @Schema(description = "音频长度（字节）", example = "84320")
    private long audioLength;
    
    /**
     * 从SpeechSynthesisResult创建响应
     * 
     * @param result 语音合成结果
     * @param url OSS存储URL
     * @param format 音频格式
     * @return 文本转语音响应
     */
    public static TextToSpeechResponseBO fromSpeechSynthesisResult(
            SpeechSynthesisResult result, 
            String url, 
            String format) {
        
        TextToSpeechResponseBO response = new TextToSpeechResponseBO();
        
        if (result != null) {
            response.setResultId(result.getResultId());
            response.setReason(result.getReason().toString());
            response.setAudioLength(result.getAudioLength());
            response.setDuration(result.getAudioDuration() / 10000); // 转换为毫秒
            
            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                response.setStatus("success");
            } else {
                response.setStatus("fail");
                response.setErrorMessage("语音合成未完成，原因：" + result.getReason());
            }
        } else {
            response.setStatus("fail");
            response.setErrorMessage("语音合成结果为空");
        }
        
        response.setUrl(url);
        response.setFormat(format);
        
        return response;
    }
} 