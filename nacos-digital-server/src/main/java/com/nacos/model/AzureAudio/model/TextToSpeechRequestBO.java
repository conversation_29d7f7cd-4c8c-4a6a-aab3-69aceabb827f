package com.nacos.model.AzureAudio.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文本转语音请求模型
 */
@Data
@Schema(description = "文本转语音请求")
public class TextToSpeechRequestBO {
    
    @Schema(description = "要转换为语音的文本内容", example = "你好，这是一段测试文本")
    private String text;
    
    @Schema(description = "语音名称，例如：zh-CN-XiaoxiaoNeural", example = "zh-CN-XiaoxiaoNeural")
    private String voiceName;
    
    @Schema(description = "语言代码，默认为en-US")
    private String language = "en-US";
    
    @Schema(description = "输出格式，默认：Audio24Khz96KBitRateMonoMp3")
    private String outputFormat = "Audio24Khz96KBitRateMonoMp3";
    
    @Schema(description = "是否使用SSML格式文本", example = "false")
    private boolean useSsml = false;
    
    @Schema(description = "语速，范围：0.5-2.0，默认：1.0")
    private Float rate = 1.0f;
    
    @Schema(description = "音调，范围：0.5-2.0，默认：1.0")
    private Float pitch = 1.0f;
    
    @Schema(description = "是否保存到文件", example = "false")
    private boolean saveToFile = false;
} 