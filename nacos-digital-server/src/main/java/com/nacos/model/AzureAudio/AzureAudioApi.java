package com.nacos.model.AzureAudio;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.microsoft.cognitiveservices.speech.audio.AudioOutputStream;
import com.microsoft.cognitiveservices.speech.audio.PullAudioOutputStream;
import com.nacos.model.AzureAudio.model.TextToSpeechRequestBO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * Azure语音服务API
 * 直接调用Azure Speech SDK的方法
 */
@Slf4j
@Schema(title = "Azure API")
public class AzureAudioApi {

    private static final String DEFAULT_SUBSCRIPTION_KEY = "ec363118097946b1adae818d9169b3ee";
    private static final String DEFAULT_REGION = "eastus";

    /**
     * 文本转语音
     * 
     * @param request 文本转语音请求
     * @param subscriptionKey 订阅密钥
     * @param region 区域
     * @return 语音合成结果
     */
    public static SpeechSynthesisResult synthesizeSpeech(TextToSpeechRequestBO request, String subscriptionKey, String region) {
        
        String methodName = "synthesizeSpeech";
        try {
            if (subscriptionKey == null || subscriptionKey.isEmpty()) {
                subscriptionKey = DEFAULT_SUBSCRIPTION_KEY;
            }
            if (region == null || region.isEmpty()) {
                region = DEFAULT_REGION;
            }
            SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
            if (StringUtils.hasText(request.getVoiceName())) {
                speechConfig.setSpeechSynthesisVoiceName(request.getVoiceName());
            } else if (StringUtils.hasText(request.getLanguage())) {
                speechConfig.setSpeechSynthesisLanguage(request.getLanguage());
            } else {
                throw new IllegalArgumentException("语音名称和语言代码不能同时为空");
            }
            if (StringUtils.hasText(request.getOutputFormat())) {
                try {
                    SpeechSynthesisOutputFormat format = SpeechSynthesisOutputFormat.valueOf(request.getOutputFormat());
                    speechConfig.setSpeechSynthesisOutputFormat(format);
                } catch (IllegalArgumentException e) {
                    log.warn("[{}]不支持的输出格式：{}，将使用默认格式", methodName, request.getOutputFormat());
                }
            } else {
                speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3);
            }
            try (SpeechSynthesizer synthesizer = new SpeechSynthesizer(speechConfig, null)) {
                SpeechSynthesisResult result = synthesizer.SpeakText(request.getText());
                // 开发阶段，输出所有响应值
                
                log.info("[{}]语音合成结果：ResultID={}, Reason={}, AudioLength={}, AudioDuration={}",
                        methodName, result.getResultId(), result.getReason(), 
                        result.getAudioLength(), result.getAudioDuration());
                
                // 获取音频数据
                byte[] audioData = getAudioDataFromResult(result, methodName);
                
                if (audioData != null) {
                    log.info("[{}]语音合成成功，音频数据长度：{}", methodName, audioData.length);
                } else {
                    log.error("[{}]无法获取音频数据", methodName);
                }
                return result;
            }
        } catch (Exception e) {
            log.error("[{}]文本转语音失败：{}", methodName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 从SpeechSynthesisResult中获取音频数据
     * 如果getAudioData()返回null，则尝试使用AudioDataStream获取
     * 
     * @param result 语音合成结果
     * @param methodName 方法名称（用于日志记录）
     * @return 音频数据字节数组，如果无法获取则返回null
     */
    private static byte[] getAudioDataFromResult(SpeechSynthesisResult result, String methodName) {
        if (result == null) {
            log.error("[{}]语音合成结果为null，无法获取音频数据", methodName);
            return null;
        }
        
        // 首先尝试直接获取音频数据
        byte[] audioData = result.getAudioData();
        
        // 如果直接获取失败，尝试使用AudioDataStream
        if (audioData == null || audioData.length == 0) {
            log.warn("[{}]getAudioData()返回为null或空，尝试使用AudioDataStream获取音频数据", methodName);
            try {
                AudioDataStream audioStream = AudioDataStream.fromResult(result);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = (int)audioStream.readData(buffer)) > 0) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                audioData = outputStream.toByteArray();
                audioStream.close();
                log.info("[{}]通过AudioDataStream成功获取音频数据，长度：{}", methodName, audioData.length);
            } catch (Exception e) {
                log.error("[{}]通过AudioDataStream获取音频数据失败：{}", methodName, e.getMessage(), e);
                return null;
            }
        }
        
        return audioData;
    }
    
    /**
     * SSML文本转语音
     * 
     * @param ssmlText SSML文本
     * @param subscriptionKey 订阅密钥
     * @param region 区域
     * @return 语音合成结果
     */
    public static SpeechSynthesisResult synthesizeSpeechFromSsml(
            String ssmlText, 
            String subscriptionKey, 
            String region) {
        
        String methodName = "synthesizeSpeechFromSsml";
        try {
            // 创建语音配置
            SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
            
            // 创建合成器 - 使用try-with-resources确保资源释放
            try (SpeechSynthesizer synthesizer = new SpeechSynthesizer(speechConfig, null)) {
                // 执行SSML转语音
                SpeechSynthesisResult result = synthesizer.SpeakSsmlAsync(ssmlText).get();
                
                if (result != null) {
                    // 记录结果信息
                    log.info("[{}]语音合成结果：ResultID={}, Reason={}, AudioLength={}, AudioDuration={}",
                            methodName, result.getResultId(), result.getReason(), 
                            result.getAudioLength(), result.getAudioDuration());
                    
                    // 检查结果是否为取消状态
                    if (result.getReason() == ResultReason.Canceled) {
                        SpeechSynthesisCancellationDetails cancellation = 
                                SpeechSynthesisCancellationDetails.fromResult(result);
                        log.error("[{}]语音合成取消：原因={}, 错误码={}, 错误详情={}", 
                                methodName, cancellation.getReason(), 
                                cancellation.getErrorCode(), cancellation.getErrorDetails());
                    }
                    
                    // 获取音频数据
                    byte[] audioData = getAudioDataFromResult(result, methodName);
                    if (audioData != null) {
                        log.info("[{}]语音合成成功，音频数据长度：{}", methodName, audioData.length);
                    } else {
                        log.error("[{}]无法获取音频数据", methodName);
                    }
                }
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("[{}]SSML转语音失败：{}", methodName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取语音列表
     * 
     * @param subscriptionKey 订阅密钥
     * @param region 区域
     * @return 语音列表结果
     */
    public static SynthesisVoicesResult getVoicesList(String subscriptionKey, String region) {
        try {
            if (subscriptionKey == null || subscriptionKey.isEmpty()) {
                subscriptionKey = DEFAULT_SUBSCRIPTION_KEY;
            }
            if (region == null || region.isEmpty()) {
                region = DEFAULT_REGION;
            }
            // 配置语音服务
            SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
            
            log.info("开始获取Azure语音列表...");
            
            // 使用try-with-resources确保资源释放
            try (SpeechSynthesizer synthesizer = new SpeechSynthesizer(speechConfig, null)) {
                // 获取语音列表
                Future<SynthesisVoicesResult> task = synthesizer.getVoicesAsync();
                SynthesisVoicesResult result = task.get();
                
                if (result.getReason() == ResultReason.VoicesListRetrieved) {
                    log.info("成功获取Azure语音列表，共{}个语音", result.getVoices().size());
                    // 打印前5个语音的详细信息作为示例
                    int count = 0;
                    for (VoiceInfo voice : result.getVoices()) {
                        if (count < 5) {
                            log.info("语音示例 #{}: ShortName={}, DisplayName={}, Locale={}, Gender={}, VoiceType={}", 
                                    count + 1, voice.getShortName(), voice.getLocalName(), voice.getLocale(), 
                                    voice.getGender(), voice.getVoiceType());
                            count++;
                        } else {
                            break;
                        }
                    }
                } else {
                    log.error("获取Azure语音列表失败，原因: {}", result.getReason());
                }
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("获取Azure语音列表异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 估算音频时长（毫秒）
     * 
     * @param audioData 音频数据
     * @param format 输出格式
     * @return 估算的音频时长（毫秒）
     */
    public static long estimateAudioDuration(byte[] audioData, SpeechSynthesisOutputFormat format) {
        if (audioData == null || audioData.length == 0) {
            return 0;
        }
        
        // 根据不同的格式估算时长
        // 这里使用简化的估算方法，实际应用中可能需要更精确的计算
        double bytesPerMs;
        
        switch (format) {
            case Riff16Khz16BitMonoPcm:
                bytesPerMs = 32.0; // 16kHz * 16bit / 8 = 32 bytes per ms
                break;
            case Riff24Khz16BitMonoPcm:
                bytesPerMs = 48.0; // 24kHz * 16bit / 8 = 48 bytes per ms
                break;
            case Audio24Khz48KBitRateMonoMp3:
                bytesPerMs = 6.0;  // 48kbit/s / 8 = 6 bytes per ms
                break;
            case Audio16Khz32KBitRateMonoMp3:
                bytesPerMs = 4.0;  // 32kbit/s / 8 = 4 bytes per ms
                break;
            default:
                bytesPerMs = 4.0;  // 默认估算值
        }
        
        return (long)(audioData.length / bytesPerMs);
    }
} 