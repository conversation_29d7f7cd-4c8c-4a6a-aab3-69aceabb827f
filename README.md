# 点点设计微服务版本
 
## 模块划分

### 网关模块（nacos-gateway）
> API网关，负责请求的路由和转发
> 集中式的请求入口
> 实现请求鉴权和流量控制
> 服务负载均衡和熔断降级

### 用户微服务（nacos-user-server）
> 用户登录认证：支持微信登录、手机号登录，授权管理
> 用户信息管理：个人资料、权限管理
> 灵感社区：banner管理、最新内容、热门内容展示
> 图像绘制：AI图像生成与编辑，多种绘图风格支持
> 内容审核：用户上传内容的安全审核
> 会员模块：会员套餐、权益管理、会员充值与续费
> 活动管理：平台营销活动的创建和管理
> 支付服务：接入第三方支付渠道
> 客服展示：在线客服系统集成

### 管理平台微服务（nacos-admin-server）
> 管理员登录：支持手机号登录、权限分级管理
> 渠道用户管理：渠道合伙人管理、登录提交审核
> 内容管理：用户内容审核、违规内容处理
> 系统配置：平台参数配置、数据字典管理
> 运营统计：用户活跃度、使用量等统计分析
> 模型配置：AI图像模型和视频模型参数配置
> 系统通知：平台通知和公告管理
> 交换管理：平台资源交换服务

### 绘图处理微服务（nacos-draw-server）
> AI绘图处理：支持多种AI绘图模型
> 图像编辑：图像修复、变换、优化
> 图库管理：用户图像作品管理、分类存储
> 文本转图像：根据文本描述生成图像
> 面部识别与处理：AI人脸编辑功能
> 图像风格化：多种艺术风格转换
> 回收站管理：删除图像的临时存储和恢复
> 客户端API：为移动端和PC端提供统一接口

### 文本处理微服务（nacos-text-server）
> 智能文本生成：基于GPT等模型的文本创作
> 文本翻译：多语言翻译服务
> 文本修改建议：语法、风格优化建议
> 实时流式对话：支持SSE（Server-Sent Events）的流式对话
> 图像与视频文本处理：图像和视频文本提取与生成

### 定时任务微服务（nacos-task-server）
> 定时任务调度：系统级定时任务统一管理
> 耗时任务异步处理：处理需要长时间运行的后台任务
> 每日精选内容：自动挑选优质内容进行推荐
> MJ绘图任务处理：管理和执行MidJourney绘图任务
> 定时数据更新：自动更新缓存和索引数据

### Webhook处理微服务（nacos-webhook-server）
> 第三方回调处理：处理来自第三方平台的通知和回调
> 事件通知：接收外部事件并分发到内部系统
> 集成外部API：连接外部服务和API
> 异步通信处理：处理异步消息和通知

### 课程模块微服务（nacos-course-server）
> 课程内容管理：创建、编辑、发布课程
> 课程分类与标签：课程分类体系管理
> 课程目录：章节结构管理
> 课程评论：用户评论和评分功能
> 课程banner管理：课程推广banner设置
> 课程推荐系统：基于用户兴趣的推荐算法

### 数字模块微服务（nacos-digital-server）
> **🎯 核心功能**
> - 数字人头像：AI虚拟形象创建和管理
> - 语音克隆：个人声音复制与定制
> - 数字视频生成：AI视频生成与编辑
> - 数字资产管理：用户数字资产存储与管理
> - 系统头像管理：平台预设的数字化头像库
> - 语音类别管理：语音风格分类
> - 用户分组管理：用户数字资产的分组管理
>
> **🏗️ 模块化架构设计（2025-07-22重构）**
>
> **Admin包 - 数据管理层** (`/admin/v1/`)
> - AvatarAdminController：数字人数据CRUD操作
> - CategoryAdminController：分类数据管理
> - GroupAdminController：组数据管理
> - TaskAdminController：任务数据管理
> - SystemAdminController：系统配置数据管理
>
> **业务包 - 业务逻辑层** (`/api/v1/`)
> - Avatar包：数字人业务逻辑
>   - AvatarController：核心业务逻辑
>   - AvatarTrainingController：训练业务
>   - AvatarChanjingController：禅境集成
> - Media包：媒体处理业务
>   - AudioController：音频生成与处理
>   - VideoController：视频生成与处理
>   - VoiceController：音色管理业务
> - Task包：任务管理业务
>   - AudioTaskController：音频任务业务
>   - VideoTaskController：视频任务业务
> - System包：系统管理业务
>   - CacheController：缓存管理
>   - UploadController：文件上传
>   - VectorController：向量数据库
>
> **Legacy兼容层** (原路径保持不变)
> - DigitalAvatarController：委托模式保持向后兼容
> - OpearController：委托给主Controller
>
> **🎵 音频处理增强**
> - 支持MiniMax和Microsoft Azure语音服务
> - 基于工厂模式的处理器架构，支持动态扩展
> - 统一的API接口，支持同步/异步音频生成
> - 完善的参数验证、错误处理和监控机制
> - 音色克隆和文本转语音功能
>
> **🚀 架构优势**
> - 职责分离：数据操作与业务逻辑完全分离
> - 微服务就绪：Admin包可直接迁移为独立微服务
> - 向后兼容：Legacy层确保现有API 100%兼容
> - 可扩展性：模块化设计便于功能扩展和维护

### 公共模块
> common-utils：公共工具类库
> - HTTP工具：网络请求处理工具
> - 文件处理：文件上传下载、格式转换
> - 加密解密：数据安全相关工具
> - 日期工具：日期时间处理
> - 字符串处理：字符串操作工具
> 
> common-business-plus：公共业务逻辑库
> - 统一响应处理：API响应格式标准化
> - 异常处理：全局异常捕获与处理
> - 用户鉴权：身份验证与授权
> - 数据验证：参数验证和数据校验
> - 缓存管理：统一缓存接口和实现

## 开发规范
> po：数据库实体类，对应数据库表结构
> vo：前端展示对象，用于返回给前端的数据封装
> dto：接收前端参数对象，用于接收和验证前端传入的数据
> bo：业务逻辑对象，用于业务逻辑处理的数据封装
> service：业务逻辑层，实现具体业务逻辑
> controller：控制层，处理请求和响应
> mapper/dao：数据访问层，与数据库交互

## 技术栈
- Spring Boot Version：3.0.13 - 核心框架
- Spring Cloud Version：2022.0.2 - 微服务框架
- Spring Cloud Alibaba Version：2022.0.0.0-RC2 - 阿里巴巴微服务生态
- Java Version：21 - 编程语言版本
- Nacos Version：2.2.1 - 服务注册发现与配置中心
- Druid Version：1.2.22 - 数据库连接池
- MySQL JDBC Version：8.3.0 - MySQL驱动
- MyBatis Plus Version：3.5.6 - ORM框架增强
- Swagger/OpenAPI：API文档自动生成
- Docker：容器化部署支持
- Maven Version：3.9.4 - 项目构建管理工具

## 开发环境
### Nacos部署
```bash
docker-compose -f fcsai-nacos-standalone-derby.yaml up -d
```

### 开发工具推荐
- IntelliJ IDEA：Java开发IDE
- Visual Studio Code：轻量级代码编辑器
- Navicat/DBeaver：数据库管理工具
- Postman：API测试工具

## 接口文档
- Swagger UI: http://192.168.3.76:7000/webjars/swagger-ui/index.html
- API文档: http://192.168.3.76:7000/fcsai-user/v3/api-docs
- Nacos控制台: http://180.184.76.163:8848/nacos/

## 部署架构
- 开发环境：本地开发 + 共享测试服务
- 测试环境：Docker容器化部署
- 生产环境：Kubernetes集群部署

## 🎵 音频处理器架构（v2.0）

### 架构特性
- **多服务商支持**：统一管理MiniMax和Microsoft Azure语音服务
- **工厂模式设计**：基于AudioProviderProcessorFactory的动态处理器管理
- **高性能**：处理器查找平均耗时170纳秒，参数验证262纳秒
- **易扩展**：新增服务商只需实现AudioProviderProcessor接口
- **线程安全**：支持高并发调用，无竞争条件

### 核心组件
```
DigitalAudioServiceImpl
    ↓
AudioProviderProcessorFactory
    ↓
AudioProviderProcessor (接口)
    ├── MiniMaxAudioProcessor
    └── MicrosoftAudioProcessor
```

### 使用示例
```json
POST /api/digital-audio/generateAudioSync
{
  "provider": "MINIMAX",
  "taskName": "音频生成任务",
  "ttsParams": {
    "text": "这是要转换为语音的文本",
    "voiceId": "voice-001",
    "speed": 1.0
  }
}
```

### 支持的服务商
- **MiniMax**：优先级1，MP3格式，高质量音频
- **Microsoft Azure**：优先级20，WAV格式，企业级音质

### 相关文档
- [架构说明](./nacos-digital-server/docs/音频处理器架构说明.md)
- [API文档](./nacos-digital-server/docs/API文档-音频生成服务.md)
- [配置指南](./nacos-digital-server/docs/配置指南-音频服务.md)
- [故障排除](./nacos-digital-server/docs/故障排除-音频服务.md)
- [测试报告](./nacos-digital-server/docs/测试报告-音频处理器架构重构.md)
