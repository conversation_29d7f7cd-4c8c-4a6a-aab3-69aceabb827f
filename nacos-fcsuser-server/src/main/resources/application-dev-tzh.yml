server:
  port: 8833
  tomcat:
    uri-encoding: utf-8
spring:
  mvc:
    servlet:
      path: /fcsuser
  application:
    name: nacos-fcsuser-server
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y

    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 1
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  #cloud配置
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        auth:
          enabled: true
          system:
            type: nacos
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  # config 配置
  config:
    import:
      - optional:nacos:${spring.application.name}-dev.${spring.cloud.nacos.config.file-extension}

logs:
  path: ./work/logs

#微信开放平台：公蜂
wxkfpt:
  appId: wx5d0b4492c9a7578f
  appSecret: 54e3d16d70e6b336724c22b4c4e44bbc
  webAppId: wx7099877bc7412ff1
  webAppSecret: 5b376ffe10c1128d2423f2f261d24844
  webRedirectUrl: https://aiapi.diandiansheji.com/callback-gf/dev01/user/auth/login/callback?type=%s&userId=%s
  # webRedirectUrl: https://b29j121595.vicp.fun/callback-gf/dev01/user/auth/login/callback?type=%s&userId=%s
  # 开放平台支付使用
  mchId: 1627312638
  privateKeyPath: ./work/apiclient_gf_key.pem
  merchantSerialNumber: 6C11856C0A765A7897CCF65FA03E0041DBA1D4E2
  apiV3Key: Ol2dNN1UAQjNxP1UQdrnxooezWuW5QZn
  payNotifyUrl: https://aiapi.diandiansheji.com/callback-gf/dev01/user/pay/wx/callback
  # payNotifyUrl: https://b29j121595.vicp.fun/callback-gf/dev01/user/pay/wx/callback


#微信公众号平台使用：蜗牛
#wxgzh:
#  appId: wx5e4934ab649e98b7
#  appSecret: f369dc31d508b25833511a0f8972ecd6
#  mchId: 1613502989
#  privateKeyPath: ./work/apiclient_key.pem
#  merchantSerialNumber: 5501E325621ADC1E7E50DBEFE447207C2FA9FE4F
#  apiV3Key: Ol2dNN1UAQjNxP1UQdrnxooezWuW5QZn
#  payNotifyUrl: https://aiapi.diandiansheji.com/callback-gf/dev01/user/pay/wx/callback


#微信公众号平台：公蜂
wxgzh:
  appId: wx5e4934ab649e98b7
  appSecret: f369dc31d508b25833511a0f8972ecd6
  # 公众号平台支付：h5
  mchId: 1627312638
  privateKeyPath: ./work/apiclient_gf_key.pem
  merchantSerialNumber: 6C11856C0A765A7897CCF65FA03E0041DBA1D4E2
  apiV3Key: Ol2dNN1UAQjNxP1UQdrnxooezWuW5QZn
  payNotifyUrl: https://aiapi.diandiansheji.com/callback-gf/dev01/user/pay/wx/callback
  # payNotifyUrl: https://b29j121595.vicp.fun/callback-gf/dev01/user/pay/wx/callback

# 支付宝使用：公蜂
zfb:
#  appId: 2021004146606571
#  merchantPrivateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCkx/1bOng4xs+enXra5GkXdSGJAYyQWkcce2GN8hZUjfr8TbseAG5c5Rf6P/NoAnVrGK0NNZXEKo56h44t89EPMLeJgi7LB1irtD9U6Jzh439gjzjvuF1+T1WmMzM3NqYUpTqBw2oUe4P4tw40EDhCJaBTm1bAhN1k8bErtK0aSGCFsxGK9PHkrjGqyaBjbmPcQ6HhmF9Nur/lqtgpgeDeLGqklVoN3R0rWv9eP3azwkBwdRDPaDZPjabL8UKMLVfYEDljDnkhy8+FgHR8/Tp06Mos6k5tb6HXAAPJPW8iY20Hsb8rZbAACT4RXB83u+RaUOGhfTZp1uN23SwFpCbdAgMBAAECggEAfqLrxPa0ef3ghafFSQAM++gas64rFq0qxWrU2D/ahXTwFIKwcSgnqOCvgrUvHRn2ny3BoMszpCSIhwYycIJWgnQQOW+BAqtMq/jESz7RgpBRziWDvkdUIsRZsHbcv2wSSv+IbT1/AurBy5zzH3PvqHY+NsLE8wOpGfDdFheuiOknAEVVj1QGRE5o0qRLqNxvHOpvfXuXUgbbDYl8FYWR85ExB+Afo73idS7e60ievJ6VGtLfdX3yGcRl/RgWo3eI+8fuKOO4BK8pMQxjoENwXvL3ORRvo8xN+pccEH71joBjFPlksCzZ9JLwcHTCdXxvGAeQ/emQyHwUWy80/oD4CQKBgQDPZj4YUrA+FJyj1FeCFINhz1rasAu0wTqY4p8+94jiVu2OzOUcaiBrceLP0pRdno5NhSjnmm9ZhjX0qAuCgl8vSpb+C4/LHlcb7KZBYEusmyth1xyfTAQ8nnds4q2zSQ/NRKy4dwQFGDKT+i4lQaaKtGBu7/8oi976Kf9Qqf8fjwKBgQDLZRwd8dfhv9dCQugrehxxgl1ir9IncfevHfDS/dr89P9hvPzcfN3FpMP9cEjbjew4z4FBH+wWHJif7RL1N30TTMPTwqvXQNRmqbBNyIRgAHQsfXinzMqRF0lbLgfPN5UP5DfQr9b/huKAjplMzz8OgHctcq/CptDSA79uK76c0wKBgQCHprb8j7DomfCuEye9VA9uvQIOwVCWxD/AWZGQzLTCXdr5KLi98ggMXqvcBD64UQmAWJUdjtw/sNG/mN4WWZFKTD4gIY3Ga6zL/efsIMC+P6uLTfhRlJW81GUGWekEt8fXGZDjAi0l6+LA+MSP57W3SS09wkmgyoNoGmn854OPrwKBgEMOv9ljjcBhBnBTLMaAyPXnfDB4NTpKCdYTcf2uMk/p2nT34Qo0pM1vyCo7pdm1DeehCkVVZ/nWTb+UYwaz16nQSlP7v7oFJsH+OiE69eHM5IBykaAJFdQGMGo+X9bCxYNqw7gq8SBn8S9fVE2NmoWGd0RlpmrvorNLBwckpwB/AoGAWOEUoZ6dXu7cn1HJh5JnIaYQ1+xDhTnb2LGDz2tEViXPYVb44LqFAXGgdKInIJI1u2IhEW8pEut7x6fbfqvLFvost8WWxoaj5OTkjtIjGGfNKimoyPKqYQas8rkchZqZrgXml4WndxzfuQ6JuP7eYzrtdt/37Q2fDTg9Wv00J1E=
#  notifyUrl: https://aiapi.diandiansheji.com/callback-gf/dev01/user/pay/zfb/callback
#  # notifyUrl: https://b29j121595.vicp.fun/callback-gf/dev01/user/pay/zfb/callback
#  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAltLqCMgtvnMnG2GV9Q58phbyn49gXP5R4834411MttCmsrlhchOh1MVD5TED/fdGnP5NYyyEarbsxZRY+/thmsC5TeR1f0DmwVF6HzeqVyPO1dFuKYlSaMm2xYSFDMi5/bUI+QKo+8mHBjaqGgBU105RVJxD5LzL4ie4oU8mVcMBAaTX2qfzbmiHcVxbKEyuqPXwLjrRk4x0vIwMZByF8/kEVhbpi/AFfLVWypnVWtBtUpNZ69gmtNlKnxoElAYHjXfnjUFn8fA2RC3Bg9tYGs3yrTh4h2UusiKDlYTmbNVP2EM5/lK9QvFkCrc7HTBKYAnK2ggoBEokUiZ9Z7m+mwIDAQAB

#  appId: 2021004156695401
  appId: 2021004166670507
  merchantPrivateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCOnYm8/mgljYGI8z9jVhj0rwfApuBDL6MkGW/GSNaAqxiK4pv6ObpeohonE8Kjl8FdgE/o2U0fVgcOjQ51kTiNcSn95VId8H3qd1+kAEsCHwOzIZL8OaY3gcnS27qEB8pfT+LY8YvADnoCgd5kz+aH6vJMhu4ZF9fGo2R+Gkb5UdzogTZHqVcAONWZ0rY0v2hJQlmEHc9S3HBTlnXHMgH81MLMaaqtNb3etK3t2U0T1iiYPiIppF386qN4w9yh8jWmgliY5Sq7HYEvj5e9UYkxUkhmbkuO3ZeuLHYTogXA/9320rxwJ76Y02KDj8olZBNCja3plXMzyaGs3Fpdt00LAgMBAAECggEAOO+D8cPPaE6aDEawY8QXqnfGLI9ysN/vzZWqHgsuN6gW4Duf8cHiOa2Vq0wkRJNtSxrSzC8UrxH1N0XxI9aaMT2gehB3PCSq4FRQBnVnfK1NGNKqj6+kQDy8ikXWrKc/j9WgxuOH22DsLNwUIUMu0cxmd07W94WhS1gn4oAzHY2I4y01AQufP1j8DClFRmGekTZDf+vnK0MF3MTxM/ndYQG39E6Laa7KkEBKPm9blcI3AsNU3sxlGYGyZBBxa9gR35NbKKEC8N0mtnGO/q83Bf6+UYEhyU21uyiJwnw0+6UJuiR98TsoQApmc4snBIsTaarYrSK/7/49DBiyu4K9QQKBgQDtn4qVIwq/wAIP7x4QUxdgTIfCBhDh/hpG73gr3zHH8QYgWMg3Az/xF6+EGNIdY4RIVtrkdA/3HPh5u6tenBBeF+I+9Jrgr7dl46RBLaLAFX21P+0cexDUt4GfgSfzadf6gN5bsi0JeiWsAS/U/WiXc+MaXC/Yvm3wmf3DBYdv8wKBgQCZpQi1lw1ZCCqbMO9rN6cfKw9RahiZWYMFgPl628yNOZFp6r3UVPh1eHFa2FbP1Fkb5EuQ+TyJ4mxkJ3HAPcGdZuN39sBzRwE2smsb2i3taqz23vdZAllunPsFviKko4hti/eqJc0O8uWJwX0DDZVBmikV9aBWrySxZzG7MU8MiQKBgAnGbmUENAT/WRZet9XkFSoY+9y3cypY0avpXgAwBlQ7Tg02mcXplE/OsaG1g69B0MdJC2gqfh2kS3R2i+4wbVvxj5UXauejUuAWJUOpJPDR8YHrR03Mojyd7+8n2QTibcgYVQ1gnwmsxK/2vIj+g7b3RXhoIm/AJbWoIBBNweyRAoGAQWbPy0SOSPzB7iepcnFRMWJJ9fT8naJNtHUPPW38p4z+yiBDR/QXgyGO1wXKBlO0zruiMwb07xiz3PoxYIypMxBN+Y3OpANwj9Do6QU5kZtcHwYQMhJe8UffTrV916lI9KCk/wdyP0/Dx5zWUwtgInm1zYQuCVgqfV2Y/L7jkEkCgYEAqVmoyIfSHMTJ/0mv8VqdH0JdEluJNlKomjb0c9qyFDNw1HU77LRt8l/8Yk9bT3NTKQ5IOJB8KI3JkYUzkj5AN6yAxeF05cHFdz87epzQC1Pl8pa10bYjR5lxkeRsavQy5chjlGGZmyoB58ThE6Vc12TdWqK188OMb78qyEvqtPU=
  notifyUrl: https://aiapi.diandiansheji.com/callback-gf/devlon/user/pay/zfb/callback
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnqRNUkotM11pOUm0y0poECEJkopxj+LuQcI4d2qnygkVr5Bv7UdP28dvr+AmeWTcLndFfGP5Ks5VEkJQbHlF32/wxyCel5Kz3E9TJ/WH0i+tJBNKoDG1GmbfsVg2MaLaR6AZwzxxR6gyPHTCmUicv8xcQYZnOy7ntJ5P36qmXzoZJdlfnPns/+PNn3RyML2YJsaJgkCavsg5DqaG45AQUv5X27YSGlBnmCn4Oir0Xxg3PbSusRVitgg8MHm5gJj6vk3XxINrVRodsBYs/QdFOy4uU1m/LYhtBHUP/rLKVyXxNfbp5O7OQpRb1d5urKJY9hC86q3LmY0n9jw6//roSwIDAQAB

# 阿里云短信
sms:
  endpoint: dysmsapi.aliyuncs.com
  accessKeyId: LTAI5t9A9sRyNs1a2AsDT7Hp
  accessKeySecret: ******************************
  signName: 蜗牛科技

# 阿里云验证码
captcha:
  endpoint: captcha.cn-shanghai.aliyuncs.com
  accessKeyId: LTAI5t9A9sRyNs1a2AsDT7Hp
  accessKeySecret: ******************************

# 友盟消息
umeng:
  # 友盟androidAppk
  android-app-id: 654c802c58a9eb5b0a0533cb
  android-master-secret: 9b3bahrqqrrgld7uz4qozsravyae7vlz
  # 友盟iosAppk
  ios-app-id: 654c80c6b2f6fa00ba7b09f2
  ios-master-secret: 4j4lefqrnudlc7mzjs9vernk5q3j7cgj

  # 友盟智能认证一键登陆
  u-verify-app-key: 204521671
  u-verify-app-secret: V9Vkk43OmyA8fQHvWv3hKJ9cVZviiVJ1
  u-verify-app-code: ed4591f6098e46ad88f4999392d57c4a
  u-verify-url: https://verify5.market.alicloudapi.com/api/v1/mobile/info?appkey=

# 极光
aurora:
  aurora-app-key: a10962e9083e2fe0a50248d0
  aurora-master-secret: 99a48218f92df98ed7a92670
  aurora-verify-url: https://api.verification.jpush.cn/v1/web/loginTokenVerify


# 苹果登录
apple:
  keyId: BAHYQSJNHS
  teamId: HZFHS2Z326
  audience: https://appleid.apple.com
  clientId: com.web.apple.login
  clientIdApp: com.snails.diandian
  grantType: authorization_code
  tokenEndpoint: https://appleid.apple.com/auth/token

# 飞书
feishu:
  appId: cli_a69582444e20900e
  appSecret: xd2z6he4w2azC5SJb6OnCevKRDmcvkIV
  appToken: P7HZbErCNanPyqso7Mfc5j9Lndf
  tableId: tblTlU8ZVn75ILB3
  # 上传文件相关
  parentType: bitable_image
  parentNode: P7HZbErCNanPyqso7Mfc5j9Lndf

#绘图模型 为处理复制指令
draw:
  models: LE,FLUX




