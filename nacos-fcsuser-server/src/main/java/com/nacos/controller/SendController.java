package com.nacos.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import com.business.db.model.bo.SmsTmplParamBO;
import com.business.db.model.dto.SmsSendCaptchaDTO;
import com.business.db.model.dto.SmsSendDTO;
import com.business.db.model.dto.UserBindMobileDTO;
import com.business.db.model.po.UserPO;
import com.business.db.model.vo.AreaCodeVO;
import com.business.enums.BRedisKeyEnum;
import com.business.enums.BResultEnum;
import com.business.utils.BSendUtil;
import com.nacos.enums.SMSTmplCodeEnum;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.IUserService;
import com.nacos.utils.CommonUtil;
import com.nacos.utils.JwtUtil;
import com.nacos.utils.ReadFileUtil;
import com.nacos.utils.captcha.CaptchaAliyunUtil;
import com.nacos.utils.sms.SMSAliyunUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Tag(name = "发送短信", description = "发送短信接口服务")
@Validated
@RestController
@RequestMapping("/send")
@Log4j2
public class SendController {

    private Instant lastRequestTime;
    @Resource
    IUserService userService;
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     * 发送验证码到原手机号
     * url: /send/verifyCode
     * 
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/verifyCode", name = "发送验证码到原手机号")
    public Result<Object> verifyCode() throws Exception {
        UserPO userInfo = userService.getById(JwtUtil.getUserId());
        if (ObjectUtil.isNull(userInfo) || StringUtils.isBlank(userInfo.getMobile())) {
            return Result.ERROR("验证失败！");
        }
        boolean isForeign = false;
        SMSTmplCodeEnum smsTmplCodeEnum = null;
        String code = RandomUtil.randomNumbers(4);
        if (PhoneUtil.isPhone(userInfo.getMobile())) {
            smsTmplCodeEnum = SMSTmplCodeEnum.MOBILE_VERIFY_TEMPLATECODE;
        } else {
            isForeign = true;
            smsTmplCodeEnum = SMSTmplCodeEnum.INTL_MOBILE_VERIFY_TEMPLATECODE;
        }
        if (smsTmplCodeEnum == null) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
        String redisKey = BSendUtil.getSendPhoneNumberRedisKeyByUse(2, userInfo.getMobile());
        log.info("====++++++++=====更换手机号验证码= {}", userInfo.getMobile().concat("=").concat(code));
        try {
            sendSmsAndSetRedis(userInfo.getMobile(), smsTmplCodeEnum, code, redisKey,
                    CommonUtil.smsConversionTool(SmsTmplParamBO.buildSmsTmplParamNote(code, isForeign)));
        } catch (Exception e) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
        return Result.SUCCESS();
    }

    /**
     * 校验原手机号验证码
     * url: /send/verify
     * 
     * @param userBindMobileDTO 用户绑定手机号DTO
     * @return
     */
    @PostMapping(value = "/verify", name = "校验原手机号验证码")
    public Result<?> verify(@RequestBody UserBindMobileDTO userBindMobileDTO) {
        if (StringUtils.isBlank(userBindMobileDTO.getVerificationCode())) {
            return Result.ERROR("验证码不可为空！");
        }
        UserPO user = userService.getById(JwtUtil.getUserId());
        String code = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_VERIFY_CODE, user.getMobile()));
        if (userBindMobileDTO.getVerificationCode().equals(code)) {
            RedisUtil.removeKey(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_VERIFY_CODE, user.getMobile()));
            return Result.SUCCESS();
        }
        return Result.ERROR("验证码校验失败！");
    }

    /**
     * 短信验证码发送接口 App端
     * url: /send/v1/smsApp
     * 
     * @param dto 短信发送DTO (包含可选的验证码参数)
     * @return 发送结果
     */
    @PostMapping(value = "/v1/smsApp", name = "验证码发送接口")
    public Result<Object> smsSendApp(@Validated @RequestBody SmsSendDTO dto) {
        String methodName = "smsSendApp";
        // 1、验证阿里云验证码
        boolean captchaResult = false;
        if (StringUtils.isBlank(dto.getCaptchaVerifyParam())) {
            captchaResult = false;
            return Result.ERROR("Captcha参数不能为空，请重试！");
        } else {
            captchaResult = CaptchaAliyunUtil.verifyIntelligentCaptcha(dto.getCaptchaVerifyParam(), "4zzemofj");
            // 临时关闭
            // if (!captchaResult) {
            //     // 验证码错误
            //     resultMap.put("captchaResult", captchaResult);
            //     resultMap.put("bizResult", false);
            //     return Result.SUCCESS(resultMap);
            // }
        }
        // 、重复提交的处理
        SendController requestHandler = new SendController();
        if (!requestHandler.canProcessRequest()) {
            return Result.ERROR(BResultEnum.ERROR_SMS_FREQUENT.getMsg());
        }
        // 2、判断是否为国际手机号
        boolean isForeign = false;
        SMSTmplCodeEnum smsTmplCodeEnum = null;
        String code = RandomUtil.randomNumbers(4);
        if (dto.getAreaCode() == null || BSendUtil.isValidPhoneNumber(dto.getAreaCode())) {
            if (!PhoneUtil.isPhone(dto.getPhone())) {
                return Result.ERROR(BResultEnum.ERROR_SMS_PHONE.getMsg());
            }
            smsTmplCodeEnum = SMSTmplCodeEnum.MOBILE_VERIFY_TEMPLATECODE;
        } else {
            isForeign = true;
            dto.setPhone(dto.getAreaCode() + dto.getPhone());
            smsTmplCodeEnum = SMSTmplCodeEnum.INTL_MOBILE_VERIFY_TEMPLATECODE;
        }
        if (smsTmplCodeEnum == null) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
        // 3、获取redis key
        String redisKey = BSendUtil.getSendPhoneNumberRedisKeyByUse(dto.getUse(), dto.getPhone());
        log.info("[{}],====++++++++=====App验证码= {}", methodName, dto.getPhone().concat("=").concat(code));
        try {
            // 4、发送短信并设置redis
            sendSmsAndSetRedis(dto.getPhone(), smsTmplCodeEnum, code, redisKey,
                    CommonUtil.smsConversionTool(SmsTmplParamBO.buildSmsTmplParamNote(code, isForeign)));
            resultMap.put("captchaResult", captchaResult);
            resultMap.put("bizResult", true);
            return Result.SUCCESS(resultMap);
        } catch (Exception e) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
    }

    /**
     * 短信验证码发送接口 Web端
     * url: /send/v1/smsWeb
     * 
     * @param dto 短信发送DTO (包含可选的验证码参数)
     * @return 发送结果
     */
    @PostMapping(value = "/v1/smsWeb", name = "验证码发送接口")
    public Result<Object> smsSendWeb(@Validated @RequestBody SmsSendDTO dto) {
        String methodName = "smsSendWeb";
        // 1、验证阿里云验证码
        boolean captchaResult = false;
        if (StringUtils.isBlank(dto.getCaptchaVerifyParam())) {
            captchaResult = false;
            return Result.ERROR("Captcha参数不能为空，请重试！");
        } else {
            if ("prod".equals(environment)) {
                captchaResult = CaptchaAliyunUtil.verifyIntelligentCaptcha(dto.getCaptchaVerifyParam(), "1ko4kdoj");
                if (!captchaResult) {
                    // 验证码错误
                    resultMap.put("captchaResult", captchaResult);
                    resultMap.put("bizResult", false);
                    return Result.SUCCESS(resultMap);
                }
            }
        }
        // 2、重复提交的处理
        SendController requestHandler = new SendController();
        if (!requestHandler.canProcessRequest()) {
            return Result.ERROR(BResultEnum.ERROR_SMS_FREQUENT.getMsg());
        }
        // 3、判断是否为国际手机号
        boolean isForeign = false;
        SMSTmplCodeEnum smsTmplCodeEnum = null;
        String code = RandomUtil.randomNumbers(4);
        if (dto.getAreaCode() == null || BSendUtil.isValidPhoneNumber(dto.getAreaCode())) {
            if (!PhoneUtil.isPhone(dto.getPhone())) {
                return Result.ERROR(BResultEnum.ERROR_SMS_PHONE.getMsg());
            }
            smsTmplCodeEnum = SMSTmplCodeEnum.MOBILE_VERIFY_TEMPLATECODE;
        } else {
            isForeign = true;
            dto.setPhone(dto.getAreaCode() + dto.getPhone());
            smsTmplCodeEnum = SMSTmplCodeEnum.INTL_MOBILE_VERIFY_TEMPLATECODE;
        }
        if (smsTmplCodeEnum == null) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
        String redisKey = BSendUtil.getSendPhoneNumberRedisKeyByUse(dto.getUse(), dto.getPhone());
        log.info("[{}],====++++++++=====Web验证码= {}", methodName, dto.getPhone().concat("=").concat(code));
        try {
            // 4、发送短信并设置redis
            sendSmsAndSetRedis(dto.getPhone(), smsTmplCodeEnum, code, redisKey,
                    CommonUtil.smsConversionTool(SmsTmplParamBO.buildSmsTmplParamNote(code,
                            isForeign)));
            resultMap.put("captchaResult", captchaResult);
            resultMap.put("bizResult", true);
            return Result.SUCCESS(resultMap);
        } catch (Exception e) {
            return Result.ERROR(BResultEnum.ERROR_SMS_AUTH_CODE.getMsg());
        }
    }

    /**
     * 发送短信并设置 Redis
     * 
     * @param phone           手机号
     * @param smsTmplCodeEnum 短信模板枚举
     * @param code            验证码
     * @param redisKey        Redis key
     * @param templateParam   模板参数
     */
    @Tag(name = "发送短信", description = "单独抽取发送短信和设置 Redis 的逻辑")
    private void sendSmsAndSetRedis(String phone, SMSTmplCodeEnum smsTmplCodeEnum, String code, String redisKey,
            String templateParam) {
        try {
            // 根据spring:profiles:active的值，如果不是prod，则默认将code设置为1111
            if (!"prod".equals(environment)) {
                RedisUtil.setValueSeconds(redisKey, "1111", 600, TimeUnit.SECONDS);
            } else {
                SMSAliyunUtil.phoneSmsSend(phone, smsTmplCodeEnum, templateParam);
                RedisUtil.setValueSeconds(redisKey, code, 600, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            throw new RuntimeException("验证码发送失败！", e);
        }
    }

    /**
     * 获取地区区号列表
     * url: /send/areaCode
     * 
     * @return
     * @throws IOException
     */
    @GetMapping(value = "/areaCode", name = "获取地区区号列表")
    public Result<List<AreaCodeVO>> getAreaCode() throws IOException {
        return Result.SUCCESS(ReadFileUtil.readSoJsonFile());
    }

    // 重复提交的处理
    public boolean canProcessRequest() {
        Instant currentTime = Instant.now();
        // 检查上次请求时间是否为空或与当前时间相隔超过30秒
        if (lastRequestTime == null || Duration.between(lastRequestTime, currentTime).getSeconds() > 30) {
            // 允许处理请求
            lastRequestTime = currentTime;
            return true;
        } else {
            // 请求间隔不足30秒，不允许处理请求
            return false;
        }
    }

    // 提取的短信返回map
    private static final Map<String, Object> resultMap = new HashMap<String, Object>() {
        {
            put("captchaResult", false);
            put("bizResult", false);
        }
    };

}
