package com.nacos.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.utils.oss.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Objects;

//video视频处理
@Slf4j
public class VideoPikaUtil {

    public static String handleVideoUpload(MultipartFile file) {
        try {
            String fileName = getFilePrefix(file);
            if (fileName == null){
                return null;
            }
            // 将上传的文件保存到本地临时文件
            File tempFile = File.createTempFile(fileName+"uploaded", ".mp4");
            file.transferTo(tempFile);

            // 检测视频帧率和音频比特率
            double frameRate = getFrameRate(tempFile.getAbsolutePath());
            int audioBitrate = getAudioBitrate(tempFile.getAbsolutePath());

            // 如果帧率大于24fps或音频比特率大于16kbps，进行转换
            if (frameRate > 24 || audioBitrate > 16) {
                File outputTempFile = File.createTempFile(fileName+"output", ".mp4");
                convertFrameRateAndAudioBitrate(tempFile.getAbsolutePath(), outputTempFile.getAbsolutePath(), frameRate > 24 ? 24 : frameRate, Math.min(audioBitrate, 16));
                MultipartFile file2 = convertFileToMultipartFile(outputTempFile);
                String fileUrl = OssUtil.uploadFile(file2.getInputStream(), Objects.requireNonNull(file2.getOriginalFilename()), IdWorker.getIdStr(), 14);
                // 删除临时文件
                FileUtils.forceDelete(tempFile);
                FileUtils.forceDelete(outputTempFile);
                return fileUrl;
            }
            String fileUrl = OssUtil.uploadFile(file.getInputStream(), Objects.requireNonNull(file.getOriginalFilename()), IdWorker.getIdStr(), 14);
            // 删除临时文件
            FileUtils.forceDelete(tempFile);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("handleVideoUpload", e.getMessage());
            return null;
        }
    }

    public static String getFilePrefix(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return null; // 文件没有扩展名
        }
        return fileName.substring(0, lastDotIndex);
    }

    private static MultipartFile convertFileToMultipartFile(File file) throws IOException {
        FileInputStream input = new FileInputStream(file);
        return new MockMultipartFile("file", file.getName(), "video/mp4", input);
    }


    private static double getFrameRate(String filePath) throws Exception {
        ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=r_frame_rate",
                "-of", "default=noprint_wrappers=1:nokey=1",
                filePath
        );
        Process process = pb.start();
        BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = in.readLine();
        process.waitFor();
        String[] parts = line.split("/");
        return Double.parseDouble(parts[0]) / Double.parseDouble(parts[1]);
    }

    private static int getAudioBitrate(String filePath) throws Exception {
        ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "error",
                "-select_streams", "a:0",
                "-show_entries", "stream=bit_rate",
                "-of", "default=noprint_wrappers=1:nokey=1",
                filePath
        );

        Process process = pb.start();
        BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = in.readLine();
        process.waitFor();

        return Integer.parseInt(line.trim()) / 1000; // 转换为 kbps
    }

    private static void convertFrameRateAndAudioBitrate(String inputFilePath, String outputFilePath, double targetFrameRate, int targetAudioBitrate) throws Exception {
        ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg",
                "-i", inputFilePath,
                "-r", String.valueOf(targetFrameRate),
                "-b:a", targetAudioBitrate + "k",
                "-y", // Overwrite output file
                outputFilePath
        );
        Process process = pb.start();
        process.waitFor();
    }

    /**
     * 从视频文件中提取音频
     *
     * @param videoFilePath 视频文件路径
     * @param audioFormat 音频格式 (wav, mp3, pcm)
     * @param sampleRate 采样率 (默认16000)
     * @param channels 声道数 (默认1)
     * @return 提取的音频文件路径
     * @throws Exception 处理异常
     */
    public static String extractAudioFromVideo(String videoFilePath, String audioFormat, int sampleRate, int channels) throws Exception {
        if (videoFilePath == null || videoFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("视频文件路径不能为空");
        }

        // 生成输出音频文件路径
        String outputAudioPath = generateTempAudioPath(audioFormat);

        ProcessBuilder pb;
        if ("pcm".equalsIgnoreCase(audioFormat)) {
            // PCM格式提取
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", videoFilePath,
                    "-vn", // 不包含视频流
                    "-acodec", "pcm_s16le", // PCM 16位小端格式
                    "-ar", String.valueOf(sampleRate), // 采样率
                    "-ac", String.valueOf(channels), // 声道数
                    "-f", "wav", // 输出为WAV容器格式
                    "-y", // 覆盖输出文件
                    outputAudioPath
            );
        } else if ("wav".equalsIgnoreCase(audioFormat)) {
            // WAV格式提取
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", videoFilePath,
                    "-vn", // 不包含视频流
                    "-acodec", "pcm_s16le", // PCM 16位小端格式
                    "-ar", String.valueOf(sampleRate), // 采样率
                    "-ac", String.valueOf(channels), // 声道数
                    "-y", // 覆盖输出文件
                    outputAudioPath
            );
        } else if ("mp3".equalsIgnoreCase(audioFormat)) {
            // MP3格式提取
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", videoFilePath,
                    "-vn", // 不包含视频流
                    "-acodec", "libmp3lame", // MP3编码器
                    "-ar", String.valueOf(sampleRate), // 采样率
                    "-ac", String.valueOf(channels), // 声道数
                    "-b:a", "128k", // 音频比特率
                    "-y", // 覆盖输出文件
                    outputAudioPath
            );
        } else {
            throw new IllegalArgumentException("不支持的音频格式: " + audioFormat);
        }

        log.info("开始从视频提取音频: {} -> {}", videoFilePath, outputAudioPath);
        Process process = pb.start();

        // 读取错误输出用于调试
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        StringBuilder errorOutput = new StringBuilder();
        String line;
        while ((line = errorReader.readLine()) != null) {
            errorOutput.append(line).append("\n");
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            log.error("FFmpeg音频提取失败，退出码: {}, 错误信息: {}", exitCode, errorOutput.toString());
            throw new Exception("音频提取失败: " + errorOutput.toString());
        }

        log.info("音频提取成功: {}", outputAudioPath);
        return outputAudioPath;
    }

    /**
     * 从视频文件中提取音频（使用默认参数）
     *
     * @param videoFilePath 视频文件路径
     * @return 提取的音频文件路径（WAV格式，16kHz，单声道）
     * @throws Exception 处理异常
     */
    public static String extractAudioFromVideo(String videoFilePath) throws Exception {
        return extractAudioFromVideo(videoFilePath, "wav", 16000, 1);
    }

    /**
     * 将新音频与原视频合并，生成新的视频文件
     *
     * @param originalVideoPath 原视频文件路径
     * @param newAudioPath 新音频文件路径
     * @param outputVideoPath 输出视频文件路径
     * @throws Exception 处理异常
     */
    public static void mergeAudioWithVideo(String originalVideoPath, String newAudioPath, String outputVideoPath) throws Exception {
        if (originalVideoPath == null || originalVideoPath.trim().isEmpty()) {
            throw new IllegalArgumentException("原视频文件路径不能为空");
        }
        if (newAudioPath == null || newAudioPath.trim().isEmpty()) {
            throw new IllegalArgumentException("新音频文件路径不能为空");
        }
        if (outputVideoPath == null || outputVideoPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出视频文件路径不能为空");
        }

        ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg",
                "-i", originalVideoPath, // 输入视频
                "-i", newAudioPath, // 输入音频
                "-c:v", "copy", // 复制视频流，不重新编码
                "-c:a", "aac", // 音频编码为AAC
                "-map", "0:v:0", // 使用第一个输入的视频流
                "-map", "1:a:0", // 使用第二个输入的音频流
                "-shortest", // 以最短的流为准
                "-y", // 覆盖输出文件
                outputVideoPath
        );

        log.info("开始合并音频和视频: {} + {} -> {}", originalVideoPath, newAudioPath, outputVideoPath);
        Process process = pb.start();

        // 读取错误输出用于调试
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        StringBuilder errorOutput = new StringBuilder();
        String line;
        while ((line = errorReader.readLine()) != null) {
            errorOutput.append(line).append("\n");
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            log.error("FFmpeg音视频合并失败，退出码: {}, 错误信息: {}", exitCode, errorOutput.toString());
            throw new Exception("音视频合并失败: " + errorOutput.toString());
        }

        log.info("音视频合并成功: {}", outputVideoPath);
    }

    /**
     * 获取音频文件时长（秒）
     *
     * @param audioFilePath 音频文件路径
     * @return 音频时长（秒）
     * @throws Exception 处理异常
     */
    public static double getAudioDuration(String audioFilePath) throws Exception {
        ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                audioFilePath
        );

        Process process = pb.start();
        BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = in.readLine();
        process.waitFor();

        if (line != null && !line.trim().isEmpty()) {
            return Double.parseDouble(line.trim());
        }
        throw new Exception("无法获取音频时长");
    }

    /**
     * 获取视频文件时长（秒）
     *
     * @param videoFilePath 视频文件路径
     * @return 视频时长（秒）
     * @throws Exception 处理异常
     */
    public static double getVideoDuration(String videoFilePath) throws Exception {
        ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                videoFilePath
        );

        Process process = pb.start();
        BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = in.readLine();
        process.waitFor();

        if (line != null && !line.trim().isEmpty()) {
            return Double.parseDouble(line.trim());
        }
        throw new Exception("无法获取视频时长");
    }

    /**
     * 转换音频格式
     *
     * @param inputAudioPath 输入音频文件路径
     * @param outputAudioPath 输出音频文件路径
     * @param targetFormat 目标格式 (wav, mp3, pcm)
     * @param sampleRate 采样率
     * @param channels 声道数
     * @throws Exception 处理异常
     */
    public static void convertAudioFormat(String inputAudioPath, String outputAudioPath, String targetFormat, int sampleRate, int channels) throws Exception {
        ProcessBuilder pb;

        if ("pcm".equalsIgnoreCase(targetFormat)) {
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", inputAudioPath,
                    "-acodec", "pcm_s16le",
                    "-ar", String.valueOf(sampleRate),
                    "-ac", String.valueOf(channels),
                    "-f", "wav",
                    "-y",
                    outputAudioPath
            );
        } else if ("wav".equalsIgnoreCase(targetFormat)) {
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", inputAudioPath,
                    "-acodec", "pcm_s16le",
                    "-ar", String.valueOf(sampleRate),
                    "-ac", String.valueOf(channels),
                    "-y",
                    outputAudioPath
            );
        } else if ("mp3".equalsIgnoreCase(targetFormat)) {
            pb = new ProcessBuilder(
                    "ffmpeg",
                    "-i", inputAudioPath,
                    "-acodec", "libmp3lame",
                    "-ar", String.valueOf(sampleRate),
                    "-ac", String.valueOf(channels),
                    "-b:a", "128k",
                    "-y",
                    outputAudioPath
            );
        } else {
            throw new IllegalArgumentException("不支持的音频格式: " + targetFormat);
        }

        log.info("开始转换音频格式: {} -> {} ({})", inputAudioPath, outputAudioPath, targetFormat);
        Process process = pb.start();

        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        StringBuilder errorOutput = new StringBuilder();
        String line;
        while ((line = errorReader.readLine()) != null) {
            errorOutput.append(line).append("\n");
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            log.error("FFmpeg音频格式转换失败，退出码: {}, 错误信息: {}", exitCode, errorOutput.toString());
            throw new Exception("音频格式转换失败: " + errorOutput.toString());
        }

        log.info("音频格式转换成功: {}", outputAudioPath);
    }

    /**
     * 生成临时音频文件路径
     *
     * @param audioFormat 音频格式
     * @return 临时音频文件路径
     * @throws IOException IO异常
     */
    private static String generateTempAudioPath(String audioFormat) throws IOException {
        String extension = audioFormat.toLowerCase();
        if ("pcm".equals(extension)) {
            extension = "wav"; // PCM使用WAV容器
        }
        File tempFile = File.createTempFile("audio_extract_", "." + extension);
        return tempFile.getAbsolutePath();
    }

    /**
     * 生成临时视频文件路径
     *
     * @return 临时视频文件路径
     * @throws IOException IO异常
     */
    public static String generateTempVideoPath() throws IOException {
        File tempFile = File.createTempFile("video_merge_", ".mp4");
        return tempFile.getAbsolutePath();
    }

    /**
     * 清理临时文件
     *
     * @param filePaths 要清理的文件路径列表
     */
    public static void cleanupTempFiles(String... filePaths) {
        for (String filePath : filePaths) {
            if (filePath != null) {
                try {
                    File file = new File(filePath);
                    if (file.exists()) {
                        FileUtils.forceDelete(file);
                        log.info("已清理临时文件: {}", filePath);
                    }
                } catch (IOException e) {
                    log.warn("清理临时文件失败: {}, 错误: {}", filePath, e.getMessage());
                }
            }
        }
    }

    /**
     * 检查FFmpeg是否可用
     *
     * @return FFmpeg是否可用
     */
    public static boolean isFFmpegAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("ffmpeg", "-version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.warn("FFmpeg不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查FFprobe是否可用
     *
     * @return FFprobe是否可用
     */
    public static boolean isFFprobeAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("ffprobe", "-version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.warn("FFprobe不可用: {}", e.getMessage());
            return false;
        }
    }

}
