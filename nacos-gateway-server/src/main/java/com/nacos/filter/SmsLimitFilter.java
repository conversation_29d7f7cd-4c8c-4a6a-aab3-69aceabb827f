// package com.nacos.filter;

// import com.nacos.feign.BlacklistServiceClient;
// import com.nacos.feign.RateLimitConfigClient;
// import com.nacos.util.IpUtil;

// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.ObjectProvider;
// import org.springframework.cloud.gateway.filter.GatewayFilterChain;
// import org.springframework.cloud.gateway.filter.GlobalFilter;
// import org.springframework.core.Ordered;
// import org.springframework.core.io.buffer.DataBuffer;
// import org.springframework.data.redis.core.ReactiveRedisTemplate;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.server.reactive.ServerHttpRequest;
// import org.springframework.http.server.reactive.ServerHttpResponse;
// import org.springframework.stereotype.Component;
// import org.springframework.web.server.ServerWebExchange;
// import reactor.core.publisher.Mono;
// import reactor.core.scheduler.Schedulers;

// import java.nio.charset.StandardCharsets;
// import java.time.Duration;
// import java.time.LocalDate;
// import java.time.format.DateTimeFormatter;
// import java.util.HashMap;
// import java.util.Map;

// /**
//  * 短信发送限流过滤器 (结合每日请求计数和违规记录)
//  */
// @Slf4j
// @Component
// public class SmsLimitFilter implements GlobalFilter, Ordered {

//     // 黑名单服务依赖注入
//     private final ObjectProvider<BlacklistServiceClient> blacklistServiceClientProvider;
//     // 限流配置服务依赖注入
//     private final ObjectProvider<RateLimitConfigClient> rateLimitConfigClientProvider;
//     // Redis 模板依赖注入
//     private final ReactiveRedisTemplate<String, String> redisTemplate;

//     // 日期格式化器
//     private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
//     // Redis key 前缀
//     private static final String SMS_REQUEST_COUNT_KEY_PREFIX = "gateway:sms:req:count:";
//     // 每日限制配置 key
//     private static final String DAILY_LIMIT_CONFIG_KEY = "sms.request.limit.daily";
//     // 默认每日限制
//     private static final int DEFAULT_DAILY_LIMIT = 10;

//     // 构造函数
//     public SmsLimitFilter(ObjectProvider<BlacklistServiceClient> blacklistServiceClientProvider,
//                           ObjectProvider<RateLimitConfigClient> rateLimitConfigClientProvider,
//                           ReactiveRedisTemplate<String, String> redisTemplate) {
//         this.blacklistServiceClientProvider = blacklistServiceClientProvider;
//         this.rateLimitConfigClientProvider = rateLimitConfigClientProvider;
//         this.redisTemplate = redisTemplate;
//     }

//     /**
//      * 短信发送限流过滤器
//      * @param exchange
//      * @param chain
//      * @return
//      */
//     @Override
//     public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//         // 获取请求
//         ServerHttpRequest request = exchange.getRequest();
//         // 获取路径
//         String path = request.getPath().toString();
        
//         // 只针对短信发送接口进行限流
//         if (path.contains("/send/sms") || path.contains("/send/v1/smsApp") || path.contains("/send/v2/sms")) {
//             // 获取真实客户端IP地址
//             String clientIp = IpUtil.getIpAddress(request);
//             // 依赖注入
//             BlacklistServiceClient blacklistServiceClient = blacklistServiceClientProvider.getIfAvailable();
//             if (blacklistServiceClient == null) {
//                 log.error("BlacklistServiceClient 不可用，跳过短信限流检查 for IP: {}", clientIp);
//                 return chain.filter(exchange);
//             }
//             // 检查IP是否在黑名单中
//             return Mono.fromCallable(() -> blacklistServiceClient.checkIpBlacklisted(clientIp))
//                 // 使用弹性调度器
//                 .subscribeOn(Schedulers.boundedElastic())
//                 .onErrorResume(e -> {
//                     log.error("调用黑名单检查服务异常 for IP: {}", clientIp, e);
//                     Map<String, Object> errorResult = new HashMap<>();
//                     errorResult.put("data", false);
//                     return Mono.just(errorResult);
//                 })
//                 .flatMap(checkResult -> {
//                     boolean blacklisted = false;
//                     Object data = checkResult.get("data");
//                      if (data instanceof Boolean) {
//                          blacklisted = (Boolean) data;
//                      } else if (data != null) {
//                          log.warn("黑名单检查返回的 data 类型不是 Boolean: {}", data.getClass().getName());
//                      }

//                     if (blacklisted) {
//                         log.warn("IP {} 在黑名单中，拒绝短信发送请求", clientIp);
//                         return responseError(exchange, "您的IP已被限制访问");
//                     } else {
//                         // 构建 Redis key
//                         String dailyKey = SMS_REQUEST_COUNT_KEY_PREFIX + LocalDate.now().format(DATE_FORMATTER) + ":" + clientIp;

//                         // 获取当前请求次数
//                         return redisTemplate.opsForValue().increment(dailyKey)
//                             .flatMap(currentCount -> {
//                                 // 获取每日限制配置
//                                 return getDailySmsLimit(clientIp) // 调用辅助方法获取限制值
//                                     .flatMap(dailyLimit -> {
//                                         Mono<Void> mainActionMono; // 用于存储主要操作（记录违规或放行）
//                                         if (currentCount > dailyLimit) {
//                                             // 请求次数超限，记录违规并返回错误
//                                             log.warn("IP {} 当日短信请求次数 ({}) 超出限制 ({})，将记录违规", clientIp, currentCount, dailyLimit);
//                                             mainActionMono = recordViolationAndRespond(exchange, clientIp, blacklistServiceClient);
//                                         } else {
//                                             // 未超限，放行
//                                             log.debug("IP {} 当日短信请求次数 ({}), 未超限制 ({})", clientIp, currentCount, dailyLimit);
//                                             mainActionMono = chain.filter(exchange);
//                                         }

//                                         // 如果是当天第一次请求，则先设置过期时间，再执行主要操作
//                                         if (currentCount == 1) {
//                                             log.debug("IP {} 当日首次短信请求，设置 Redis key 过期时间", clientIp);
//                                             // 设置24小时过期
//                                             return redisTemplate.expire(dailyKey, Duration.ofHours(24)).then(mainActionMono);
//                                         } else {
//                                             // 非首次请求，直接执行主要操作
//                                             return mainActionMono;
//                                         }
//                                     });
//                             })
//                             .onErrorResume(e -> {
//                                 log.error("Redis 操作失败 for IP: {}", clientIp, e);
//                                 return chain.filter(exchange);
//                             });
//                     }
//                 });
//         }

//         return chain.filter(exchange);
//     }

//     /**
//      * 获取每日短信限制配置
//      * @param ipForLog 用于日志记录的IP地址
//      * @return 每日短信限制配置值
//      */
//     private Mono<Integer> getDailySmsLimit(String ipForLog) {
//         // 依赖注入
//         RateLimitConfigClient client = rateLimitConfigClientProvider.getIfAvailable();
//         if (client == null) {
//             log.warn("RateLimitConfigClient 不可用，使用默认每日短信限制: {}", DEFAULT_DAILY_LIMIT);
//             return Mono.just(DEFAULT_DAILY_LIMIT);
//         }
//         // 获取每日限制配置
//         return Mono.fromCallable(() -> client.getConfigValue(DAILY_LIMIT_CONFIG_KEY))
//                 .subscribeOn(Schedulers.boundedElastic())
//                 .map(result -> {
//                     // 检查配置是否包含 data 键
//                     if (result != null && result.containsKey("data")) {
//                         Object data = result.get("data");
//                         if (data instanceof Integer) {
//                             // 配置值为整数，直接返回
//                             return (Integer) data;
//                         } else if (data instanceof String) {
//                             // 配置值为字符串，尝试转换为整数
//                             try {
//                                 return Integer.parseInt((String) data);
//                             } catch (NumberFormatException e) {
//                                 log.warn("配置值 '{}' for key '{}' 不是有效的整数, 使用默认值: {}", data, DAILY_LIMIT_CONFIG_KEY, DEFAULT_DAILY_LIMIT);
//                                 return DEFAULT_DAILY_LIMIT;
//                             }
//                         } else if (data != null) {
//                             // 配置值为其他类型，记录警告日志并返回默认值
//                             log.warn("配置值 for key '{}' 类型不是 Integer 或 String (而是 {}), 使用默认值: {}", DAILY_LIMIT_CONFIG_KEY, data.getClass().getName(), DEFAULT_DAILY_LIMIT);
//                             return DEFAULT_DAILY_LIMIT;
//                         } else {
//                             // 配置值为 null，记录警告日志并返回默认值
//                            log.warn("配置值 for key '{}' 为 null, 使用默认值: {}", DAILY_LIMIT_CONFIG_KEY, DEFAULT_DAILY_LIMIT);
//                            return DEFAULT_DAILY_LIMIT; 
//                         }
//                     } else {
//                         // 无法从配置服务获取 key 或返回结果不包含 'data' 键，记录警告日志并返回默认值
//                         log.warn("无法从配置服务获取 key '{}' 或返回结果不包含 'data' 键, 使用默认值: {}", DAILY_LIMIT_CONFIG_KEY, DEFAULT_DAILY_LIMIT);
//                         return DEFAULT_DAILY_LIMIT;
//                     }
//                 })
//                 .onErrorResume(e -> {
//                     // 记录错误日志
//                     log.error("调用配置服务获取 key '{}' 异常 for IP: {}, 使用默认值: {}", DAILY_LIMIT_CONFIG_KEY, ipForLog, DEFAULT_DAILY_LIMIT, e);
//                     // 返回默认值
//                     return Mono.just(DEFAULT_DAILY_LIMIT);
//                 });
//     }

//     /**
//      * 记录违规并返回错误响应
//      * @param exchange 
//      * @param clientIp 
//      * @param blacklistServiceClient
//      * @return 
//      */
//     private Mono<Void> recordViolationAndRespond(ServerWebExchange exchange, String clientIp, BlacklistServiceClient blacklistServiceClient) {
//         // 构建参数
//         Map<String, String> params = new HashMap<>();
//         params.put("ip", clientIp);
//         params.put("reason", "短信请求次数超当日限制");

//         // 调用记录违规服务
//         return Mono.fromCallable(() -> blacklistServiceClient.recordViolation(params))
//             .subscribeOn(Schedulers.boundedElastic())
//             .flatMap(violationResult -> {
//                 log.warn("IP {} 已记录短信超限违规，拒绝本次请求", clientIp);
//                 // 检查是否添加到黑名单
//                 boolean addedToBlacklist = false;
//                 Object data = violationResult.get("data");
//                   if (data instanceof Boolean) {
//                       addedToBlacklist = (Boolean) data;
//                   } else if (data != null) {
//                       log.warn("记录违规返回的 data 类型不是 Boolean: {}", data.getClass().getName());
//                   }

//                 // 根据是否添加到黑名单返回错误响应
//                 if (addedToBlacklist) {
//                     return responseError(exchange, "您的操作过于频繁，已暂时限制发送短信");
//                 } else {
//                     return responseError(exchange, "短信发送次数已达今日上限");
//                 }
//             })
//             .onErrorResume(e -> {
//                 log.error("调用记录违规服务异常 for IP: {}", clientIp, e);
//                 return responseError(exchange, "处理请求时发生错误，请稍后再试");
//             });
//     }

//     /**
//      * 获取过滤器执行顺序
//      * @return 过滤器执行顺序
//      */
//     @Override
//     public int getOrder() {
//         return -100; // 确保在其他过滤器之前执行
//     }
    
//     /**
//      * 返回错误响应
//      * @param exchange
//      * @param message
//      * @return
//      */
//     private Mono<Void> responseError(ServerWebExchange exchange, String message) {
//         ServerHttpResponse response = exchange.getResponse();
//         response.setStatusCode(HttpStatus.OK);  // 将状态码改为 200
//         response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

//         String body = String.format(
//             "{\"success\":false,\"message\":\"%s\",\"status\":429,\"data\":null,\"timestamp\":%d}",
//             message,
//             System.currentTimeMillis()
//         );
//         byte[] bytes = body.getBytes(StandardCharsets.UTF_8);
//         DataBuffer buffer = response.bufferFactory().wrap(bytes);
//         return response.writeWith(Mono.just(buffer));
//     }
// } 