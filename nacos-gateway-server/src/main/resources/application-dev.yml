server:
  port: 8801
spring:
  application:
    name: nacos-gateway-server # 服务名称
  main:
    web-application-type: reactive
    allow-bean-definition-overriding: true
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
    gateway:
      discovery:
        locator:
          enabled: true # 启用服务发现
          lower-case-service-id: true # 服务ID小写
      # 默认过滤器
      default-filters:
        - PreserveHostHeader
      routes:
        # 用户服务
        - id: user-route
          uri: lb://nacos-user-server
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=0
        # 方程式用户服务
        - id: fcsuser-route
          uri: lb://nacos-fcsuser-server
          predicates:
            - Path=/fcsuser/**
          filters:
            - StripPrefix=0

        # 绘画服务
        - id: draw-route
          uri: lb://nacos-draw-server
          predicates:
            - Path=/draw/**
          filters:
            - StripPrefix=0

        # 数字人服务
        - id: digital-route
          uri: lb://nacos-digital-server
          predicates:
            - Path=/digital/**
          filters:
            - StripPrefix=0
            
        # 文本服务
        - id: text-route
          uri: lb://nacos-text-server
          predicates:
            - Path=/text/**
          filters:
            - StripPrefix=0
            
        # 任务服务
        - id: task-route
          uri: lb://nacos-task-server
          predicates:
            - Path=/task/**
          filters:
            - StripPrefix=0
            
        # Webhook服务
        - id: webhook-route
          uri: lb://nacos-webhook-server
          predicates:
            - Path=/webhook/**
          filters:
            - StripPrefix=0
            
        # 课程服务
        - id: course-route
          uri: lb://nacos-course-server
          predicates:
            - Path=/course/**
          filters:
            - StripPrefix=0
            
        # 管理后台服务
        - id: admin-route
          uri: lb://nacos-admin-server
          predicates:
            - Path=/admin/**
          filters:
            - StripPrefix=0

  config:
    import:
      - optional:nacos:${spring.application.name}-dev.${spring.cloud.nacos.config.file-extension}

# 日志配置
logs:
  path: ./work/logs

# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always


