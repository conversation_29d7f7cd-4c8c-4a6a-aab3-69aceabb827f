server:
  port: 8829
  tomcat:
    uri-encoding: utf-8
# spring
spring:
  application:
    name: nacos-draw-server
  mvc:
    servlet:
      path: /draw
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y

    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 5
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  #cloud配置
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        auth:
          enabled: true
          system:
            type: nacos
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  config:
    import:
      - optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}

logs:
  path: ./work/logs

#go api对接参数
go:
  # goApi画图 主域名地址=所有 api 请求最推荐的域
  # goApiDrawUrl: https://api.goapi.ai
  # goApi画图 辅助域名地址=具有所有 API 端点的备份域
  goApiDrawUrl: https://api.goapi.xyz
  # 辅助域名=具有所有 API 端点的备份域
  # goApiDrawUrl: https://api.midjourneyapi.xyz
  # 特殊域名=针对长时间等待响应进行了改进的域。如果您有长期停止 GPT 请求，请使用此域
  # goApiDrawUrl: https://proxy.goapi.xyz 一直在用的
  # webhook 回调地址
  webhookCallbackUrl: https://291p2159s5.imdo.co
  # go 秘钥
  goApiKey: 35f8585523e5a17fe6ae164bbe7f551e442e995a456fbd92f9f0fad9e4901f03
  # go 渐显翻墙
  goProxyUrl: https://jump.diandiansheji.com/discordapp

#oss 后期可能会用
ali:
  oss:
    # 图片访问地址
    endpoint: https://oss-cn-beijing.aliyuncs.com
    # oss Id
    accessKeyId: LTAI5t9A9sRyNs1a2AsDT7Hp
    # ossKye
    secretAccessKey: ******************************
    # 存储桶名称
    bucketName: idotdesign
    accessPath: https://cdn.diandiansheji.com/
    #accessPath: https://image.diandiansheji.com/
    #accessPath: https://idotdesign.oss-cn-beijing.aliyuncs.com/
    # 图片存储文件夹
    fileName: mj/
  text:
    # 图片检测地址、外网
    endpoint: green-cip.cn-beijing.aliyuncs.com
    # 图片检测地址、内网
    # endpoint: green-cip-vpc.cn-beijing.aliyuncs.com
    # 文本检测id
    accessKeyId: LTAI5t9A9sRyNs1a2AsDT7Hp
    # 文本检测key
    secretAccessKey: ******************************
    # 检测规则名
    service: aigcCheck
    # 图片检测分值 默认50
    confidence: 89

mjweb:
  # 正式
  channelId: singleplayer_eb5a1822-138b-45c2-b2e7-040cf2e72246
  cookie: AMP_MKTG_437c42b22c=JTdCJTdE; _ga=GA1.1.1107113351.1718614850; _gcl_au=1.1.1320823074.1718614851; __stripe_mid=6d86acdb-d128-4639-b230-72cf42e2ec0caa0d3b; darkMode=disabled; cf_clearance=tNCoGCrgTGlV6mTIJpH6ZMHzix29I_PYwUGhfMZusvE-1718629576-*******-8OcTgekpyXoeDMDqaFpv9HenMFRXuOlHtAN3yZB3aTCHc_LOHp2DagmTASr2hZmU18FGnRzh2_Jrlx6wug4MbA; __cf_bm=cyr6Z4z1YBd5ePOLC0j6n3TvtMKq4ppPhOwHyc9S6w8-1718634711-*******-QWfr5ppNqqojBkr84yxgwUEYA.Gsxik6ebwpekMfzSvWPwvAkiM6YWVV0HLTnF_OAGG01zz4zU9Vs5htjhG2zg; __Host-Midjourney.AuthUserToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XEznY723FE_LePfrpZZFzAKZyHiYOKArdwMEWNho30M; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI2NTJkZmRlNS1kMzdiLTQyNzctOWNiOS04MmFmMzJhMWU5ZTclMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjJlYjVhMTgyMi0xMzhiLTQ1YzItYjJlNy0wNDBjZjJlNzIyNDYlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzE4NjM0NzMxNDE1JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxODYzNDczMTUyMyUyQyUyMmxhc3RFdmVudElkJTIyJTNBNzglN0Q=; _ga_Q0DQ5L7K0D=GS1.1.1718634732.3.0.1718634732.0.0.0; __stripe_sid=5574ab47-16e5-48c9-af42-fb00c378f411510562
  # 测试
  # channelId: singleplayer_96950bf9-99f9-43fe-899e-f16f30f262e7
  # cookie: AMP_MKTG_437c42b22c=JTdCJTdE; darkMode=disabled; _ga=GA1.1.1257128399.1716948811; _gcl_au=1.1.1063012360.1716948811; __stripe_mid=09cb6648-8d13-4424-bfb2-f7977981ad357078f3; __Host-Midjourney.AuthUserToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2AMHRQoi4fTOlT961L0MYZpMjn5f6ZzW9QlVJbaxyZI; __stripe_sid=466c468b-4d13-4d4f-a2ae-fbfbdf1ee8081bc93b; cf_clearance=7.UScJ9F3YvLT5UvHTIPSTHkTsigkSv1CRm7n0kGQOw-1718688160-*******-bJC5yssJ5XvgxIAU2SAgLPSg2Mb1ApQlNP3HJTmfiCpGFuDSS8Mcx_tpWiE81eM06epNGv_YjQMhEzuT5Nt_ww; __cf_bm=dvN04.qCfPuVdTWML6S2G.ZRGDxoRLw8GaeRBOdKgjw-1718688431-*******-74.Y1guXOwjE.yGEuEPNAgXlFYYCwlONCuorcg2caO6Xe_GEtjukLqQxXKplDl0qr5JLz.jhMumTGU_.e9cPcA; _ga_Q0DQ5L7K0D=GS1.1.1718686956.5.1.1718688519.0.0.0; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI5ZjEyOGEwYS1jNzYxLTRjNTQtYjEzYy1lNjNkNTliNTYwNDElMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI5Njk1MGJmOS05OWY5LTQzZmUtODk5ZS1mMTZmMzBmMjYyZTclMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzE4Njg2NjA5OTE1JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxODY4ODUzNzkwMCUyQyUyMmxhc3RFdmVudElkJTIyJTNBNjclN0Q=

# RocketMQ 配置
rocketmq:
  nameSrvAddr: rmq-cn-g4t3t88sy01.cn-beijing.rmq.aliyuncs.com:8080
  instanceName: rmq-cn-g4t3t88sy01
  accessKey: yOslyLV26wn0mg1X
  secretKey: bZBj50ZGD0sfxuG2
  topic: test
  groupId: testConsume