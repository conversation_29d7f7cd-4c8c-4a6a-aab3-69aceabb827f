package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.DictConfigDTO;
import com.business.db.model.po.DictConfigPO;
import com.nacos.result.Result;


public interface IDictConfigService extends IService<DictConfigPO> {

    /**
     * 查询数据字典分页
     * @param dictConfigDTO 字典配置DTO
     * @return 结果
     */
    Result<Page<DictConfigPO>> queryPage(DictConfigDTO dictConfigDTO);
    
    /**
     * 新增数据字典
     * @param dictConfigPO 字典配置PO
     * @return 结果
     */ 
    Result<Boolean> add(DictConfigPO dictConfigPO);

    /**
     * 更新数据字典
     * @param dictConfigPO 字典配置PO
     * @return 结果
     */
    Result<Boolean>  update(DictConfigPO dictConfigPO);

    /**
     * 同步字典配置到Redis缓存
     * @return 同步结果
     */
    Result<Boolean> syncDictConfigToRedis();

}
