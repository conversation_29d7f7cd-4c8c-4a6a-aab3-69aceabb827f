server:
  port: 8832
  tomcat:
    uri-encoding: utf-8
# spring
spring:
  application:
    name: nacos-webhook-server
  mvc:
    servlet:
      path: /webhook
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y
    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 5
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  #cloud配置
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        auth:
          enabled: true
          system:
            type: nacos
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  config:
    import:
      - optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}

logs:
  path: ./work/logs

#oss 后期可能会用
ali:
  oss:
    # 图片访问地址
    endpoint: https://oss-cn-beijing.aliyuncs.com
    # oss Id
    accessKeyId: LTAI5t9A9sRyNs1a2AsDT7Hp
    # ossKye
    secretAccessKey: ******************************
    # 存储桶名称
    bucketName: idotdesign
    accessPath: https://cdn.diandiansheji.com/
    #accessPath: https://image.diandiansheji.com/
    #accessPath: https://idotdesign.oss-cn-beijing.aliyuncs.com/
    # 图片存储文件夹
    fileName: mj/


