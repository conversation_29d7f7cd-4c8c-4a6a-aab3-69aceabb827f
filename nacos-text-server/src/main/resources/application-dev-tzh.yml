server:
  port: 8814
  tomcat:
    uri-encoding: utf-8
spring:
  mvc:
    servlet:
      path: /text
  application:
    name: nacos-text-server
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y
    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 1
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  #cloud配置
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        auth:
          enabled: true
          system:
            type: nacos
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  # config 配置
  config:
    import:
      - optional:nacos:${spring.application.name}-dev.${spring.cloud.nacos.config.file-extension}

logs:
  path: ./work/logs


# okhttp3配置
ok:
  http:
    connect-timeout: 30
    read-timeout: 30
    write-timeout: 30
    max-idle-connections: 200
    keep-alive-duration: 300

wenxin:
  api-key: xP6OQATWevHTWzu7gqDCKMAP
  secret-key: uXXnhcpmHwpSBxwRGEm8F3whwoPTO4yc
  #获取AccessToken的url地址
  access-token-url: https://aip.baidubce.com/oauth/2.0/token
  #文心ERNIE-Bot4.0模型访问地址
  ERNIE_Bot4_0URL: https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro

pika:
  #
  # domain-name: https://pikapika-api.suxiaobaoai.com
  # 皮卡翻墙代理域名
  domain-name: https://pikapika.iworks.cn

#梦工厂拉取任务开关
dreamfactory:
  pull-task-switch: true

aliapiKey: sk-560ad349cb6a4a7eaeb86194ffc77bb9
commonIndex: 99d30ofeu7
