<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目基础信息 -->
    <groupId>com.nacos</groupId>
    <artifactId>ddsj-server-nacos</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>ddsj-server-nacos</name>
    <description>nacos object</description>
    <packaging>pom</packaging>

    <!-- 强制 spring boot版本-->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.13</version>
        <!--        <version>3.1.0</version>-->
        <relativePath/>
    </parent>

    <modules>
        <!-- 本地工具包 -->
        <module>common-utils</module>
        <module>common-business-plus</module>
        <!-- 网关模块 -->
        <module>nacos-gateway-server</module>
        <!-- 公蜂管理平台 -->
        <module>nacos-admin-server</module>
        <!--点点设计用户模块 -->
        <module>nacos-user-server</module>
        <!--方程式用户模块 -->
        <module>nacos-fcsuser-server</module>
        <!--新的画图模块 -->
        <module>nacos-draw-server</module>
        <!--webhook模块 -->
        <module>nacos-webhook-server</module>
        <!--定时任务模块 -->
        <module>nacos-task-server</module>
        <!--文本模块 -->
        <module>nacos-text-server</module>
        <!--课程模块 -->
        <module>nacos-course-server</module>
        <!--数字模块 -->
        <module>nacos-digital-server</module>
    </modules>

    <properties>
        <!-- spring cloud 版本信息-->
        <spring-cloud.version>2022.0.2</spring-cloud.version>
        <!-- spring cloud alibaba 版本信息-->
        <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>
        <!-- java 版本信息-->
        <java.version>21</java.version>
        <!-- lombok 工具版本-->
        <nacos.common.utils.version>0.0.1-SNAPSHOT</nacos.common.utils.version>
        <nacos.common.business.version>0.0.1-SNAPSHOT</nacos.common.business.version>
        <!-- 聚合接口文档 -->
        <springdoc.version>2.1.0</springdoc.version>
        <springdoc.webmvc.core.version>1.7.0</springdoc.webmvc.core.version>
        <!-- 数据持久化版本 -->
        <druid.version>1.2.22</druid.version>
        <mysqljdbc.version>8.3.0</mysqljdbc.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <common-business-plus.version>0.0.1-SNAPSHOT</common-business-plus.version>


        <!-- yaml 工具包版本 -->
        <snakeyaml.version>2.2</snakeyaml.version>
        <!-- 三方引入工具 -->
        <okhttp-sse.version>3.14.9</okhttp-sse.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>

            <!-- yaml 工具包 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- 项目公共包 -->
            <dependency>
                <groupId>com.nacos</groupId>
                <artifactId>common-utils</artifactId>
                <version>${nacos.common.utils.version}</version>
            </dependency>

            <!-- 项目公共业务包 -->
            <dependency>
                <groupId>com.nacos</groupId>
                <artifactId>common-business-plus</artifactId>
                <version>${common-business-plus.version}</version>
            </dependency>

            <!-- spring cloud alibaba 依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring cloud 依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- commons-codec dependency -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>application-${env}.yml</include>
                    <include>application.yml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
